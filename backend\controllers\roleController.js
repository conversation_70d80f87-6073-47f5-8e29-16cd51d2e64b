const Role = require("../models/Role");
const User = require("../models/User");
const { success, error, paginate } = require("../utils/response");

/**
 * 获取角色列表（分页）
 * @route GET /api/roles
 * @access Private
 */
const getRoles = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const query = {};

    // 角色名称搜索
    if (req.query.name) {
      query.name = { $regex: req.query.name, $options: "i" };
    }

    // 角色编码搜索
    if (req.query.code) {
      query.code = { $regex: req.query.code, $options: "i" };
    }

    // 状态筛选
    if (req.query.status !== undefined) {
      query.status = parseInt(req.query.status);
    }

    // 查询角色总数
    const total = await Role.countDocuments(query);

    // 查询角色列表
    const roles = await Role.find(query)
      .populate({
        path: "permissions",
        select: "name type path perms",
        match: { status: 1 },
      })
      .sort({ createTime: -1 })
      .skip(skip)
      .limit(limit);

    return paginate(res, "获取角色列表成功", roles, total, page, limit);
  } catch (err) {
    console.error("获取角色列表错误:", err.message);
    return error(res, "获取角色列表失败: " + err.message, 500);
  }
};

/**
 * 获取所有角色（不分页，用于下拉选择）
 * @route GET /api/roles/all
 * @access Private
 */
const getAllRoles = async (req, res) => {
  try {
    // 只查询启用状态的角色
    const roles = await Role.find({ status: 1 })
      .select("name code")
      .sort({ createTime: -1 });

    return success(res, "获取所有角色成功", roles);
  } catch (err) {
    console.error("获取所有角色错误:", err.message);
    return error(res, "获取所有角色失败: " + err.message, 500);
  }
};

/**
 * 获取单个角色信息
 * @route GET /api/roles/:id
 * @access Private
 */
const getRoleById = async (req, res) => {
  try {
    const role = await Role.findById(req.params.id).populate({
      path: "permissions",
      select: "name type path perms",
      match: { status: 1 },
    });

    if (!role) {
      return error(res, "角色不存在", 404);
    }

    return success(res, "获取角色信息成功", role);
  } catch (err) {
    console.error("获取角色信息错误:", err.message);
    return error(res, "获取角色信息失败: " + err.message, 500);
  }
};

/**
 * 创建角色
 * @route POST /api/roles
 * @access Private
 */
const createRole = async (req, res) => {
  try {
    const { name, code, status, permissions, remark } = req.body;

    // 创建新角色
    const role = new Role({
      name,
      code,
      status,
      permissions,
      remark,
    });

    // 保存角色
    await role.save();

    // 返回角色信息
    const savedRole = await Role.findById(role._id).populate({
      path: "permissions",
      select: "name type path perms",
    });

    return success(res, "创建角色成功", savedRole, 201);
  } catch (err) {
    console.error("创建角色错误:", err.message);
    return error(res, "创建角色失败: " + err.message, 500);
  }
};

/**
 * 更新角色
 * @route PUT /api/roles/:id
 * @access Private
 */
const updateRole = async (req, res) => {
  try {
    const { name, code, status, permissions, remark } = req.body;

    // 查找角色
    const role = await Role.findById(req.params.id);
    if (!role) {
      return error(res, "角色不存在", 404);
    }

    // 更新角色信息
    if (name !== undefined) role.name = name;
    if (code !== undefined) role.code = code;
    if (status !== undefined) role.status = status;
    if (permissions !== undefined) role.permissions = permissions;
    if (remark !== undefined) role.remark = remark;

    // 保存更新
    await role.save();

    // 返回更新后的角色信息
    const updatedRole = await Role.findById(role._id).populate({
      path: "permissions",
      select: "name type path perms",
    });

    return success(res, "更新角色成功", updatedRole);
  } catch (err) {
    console.error("更新角色错误:", err.message);
    return error(res, "更新角色失败: " + err.message, 500);
  }
};

/**
 * 删除角色
 * @route DELETE /api/roles/:id
 * @access Private
 */
const deleteRole = async (req, res) => {
  try {
    const role = await Role.findById(req.params.id);
    if (!role) {
      return error(res, "角色不存在", 404);
    }

    // 检查是否有用户使用此角色
    const usersWithRole = await User.countDocuments({ roles: req.params.id });
    if (usersWithRole > 0) {
      return error(
        res,
        `该角色正在被 ${usersWithRole} 个用户使用，无法删除`,
        400
      );
    }

    await role.remove();

    return success(res, "删除角色成功");
  } catch (err) {
    console.error("删除角色错误:", err.message);
    return error(res, "删除角色失败: " + err.message, 500);
  }
};

/**
 * 批量删除角色
 * @route DELETE /api/roles/batch
 * @access Private
 */
const batchDeleteRoles = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return error(res, "请提供有效的角色ID数组", 400);
    }

    // 检查是否有用户使用这些角色
    const usersWithRoles = await User.countDocuments({ roles: { $in: ids } });
    if (usersWithRoles > 0) {
      return error(
        res,
        `选中的角色中有 ${usersWithRoles} 个用户正在使用，无法删除`,
        400
      );
    }

    const result = await Role.deleteMany({ _id: { $in: ids } });

    return success(res, `成功删除 ${result.deletedCount} 个角色`);
  } catch (err) {
    console.error("批量删除角色错误:", err.message);
    return error(res, "批量删除角色失败: " + err.message, 500);
  }
};

/**
 * 更新角色状态
 * @route PATCH /api/roles/:id/status
 * @access Private
 */
const updateRoleStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (status === undefined || ![0, 1].includes(status)) {
      return error(res, "请提供有效的状态值", 400);
    }

    const role = await Role.findById(req.params.id);
    if (!role) {
      return error(res, "角色不存在", 404);
    }

    role.status = status;
    await role.save();

    return success(res, `角色状态已${status === 1 ? "启用" : "禁用"}`);
  } catch (err) {
    console.error("更新角色状态错误:", err.message);
    return error(res, "更新角色状态失败: " + err.message, 500);
  }
};

module.exports = {
  getRoles,
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  batchDeleteRoles,
  updateRoleStatus,
};
