<template>
  <div class="app-wrapper" :class="{ 'sidebar-collapsed': !sidebarOpened }">
    <!-- 侧边栏 -->
    <div class="sidebar-container" :class="{ 'collapsed': !sidebarOpened }">
      <div class="logo-container">
        <img src="@/assets/logo.png" alt="Logo" class="logo">
        <h1 class="title" v-if="sidebarOpened">后台管理系统</h1>
      </div>
      <Sidebar :is-collapse="!sidebarOpened" />
    </div>

    <!-- 主要内容区 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <div class="navbar">
        <!-- 折叠按钮 -->
        <div class="hamburger-container" @click="toggleSidebar">
          <el-icon :class="{ 'is-active': sidebarOpened }">
            <Fold v-if="sidebarOpened" />
            <Expand v-else />
          </el-icon>
        </div>

        <!-- 面包屑导航 -->
        <div class="breadcrumb-wrapper">
          <Breadcrumb />
        </div>

        <div class="right-menu">
          <!-- 全屏按钮 -->
          <div class="right-menu-item hover-effect" @click="toggleFullScreen">
            <el-icon :size="18">
              <FullScreen v-if="!isFullscreen" />
              <Aim v-else />
            </el-icon>
          </div>

          <!-- 布局设置 -->
          <div class="right-menu-item hover-effect" @click="openSettings">
            <el-icon :size="18">
              <Setting />
            </el-icon>
          </div>

          <!-- 用户信息下拉菜单 -->
          <el-dropdown class="avatar-container" trigger="click">
            <div class="avatar-wrapper">
              <el-avatar :size="40" :src="userStore.userInfo?.avatar || ''" />
              <span class="name">{{ userStore.userInfo?.nickname }}</span>
              <el-icon class="el-icon-caret-bottom">
                <CaretBottom />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <router-link to="/user/profile">
                  <el-dropdown-item>个人中心</el-dropdown-item>
                </router-link>
                <el-dropdown-item @click="handleChangePassword">修改密码</el-dropdown-item>
                <el-dropdown-item divided @click="handleLogout">
                  <span style="display:block;">退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 标签栏 -->
      <TagsView />

      <!-- 主要内容 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>

    <!-- 设置抽屉 -->
    <el-drawer v-model="showSettings" title="系统布局设置" direction="rtl" size="300px">
      <div class="drawer-content">
        <div class="setting-item">
          <span>侧边栏Logo</span>
          <el-switch v-model="sidebarLogo" />
        </div>
        <div class="setting-item">
          <span>固定Header</span>
          <el-switch v-model="fixedHeader" />
        </div>
        <div class="setting-item">
          <span>显示标签栏</span>
          <el-switch v-model="tagsView" />
        </div>
        <div class="setting-item">
          <span>主题颜色</span>
          <el-color-picker v-model="themeColor" @change="handleThemeChange" />
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { useTagsViewStore } from '@/stores/tagsView'
import { useRouter, useRoute, onBeforeRouteUpdate } from 'vue-router'
import { computed, watch, ref } from 'vue'
import { Fold, Expand, FullScreen, Aim, Setting, CaretBottom } from '@element-plus/icons-vue'
import Sidebar from './components/Sidebar.vue'
import Breadcrumb from './components/Breadcrumb.vue'
import TagsView from './components/TagsView.vue'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()
const appStore = useAppStore()
const tagsViewStore = useTagsViewStore()
const router = useRouter()
const route = useRoute()

// 侧边栏状态
const sidebarOpened = computed(() => appStore.sidebar.opened)

// 缓存的视图
const cachedViews = computed(() => tagsViewStore.cachedViews)

// 全屏状态
const isFullscreen = ref(false)

// 设置抽屉
const showSettings = ref(false)
const sidebarLogo = ref(true)
const fixedHeader = ref(true)
const tagsView = ref(true)
const themeColor = ref('#409EFF')

// 切换侧边栏
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

// 切换全屏
const toggleFullScreen = () => {
  const element = document.documentElement

  if (!isFullscreen.value) {
    if (element.requestFullscreen) {
      element.requestFullscreen()
    } else if (element.webkitRequestFullscreen) {
      element.webkitRequestFullscreen()
    } else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen()
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen()
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen()
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen()
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen()
    }
  }

  isFullscreen.value = !isFullscreen.value
}

// 监听全屏变化
document.addEventListener('fullscreenchange', () => {
  isFullscreen.value = !!document.fullscreenElement
})

// 打开设置抽屉
const openSettings = () => {
  showSettings.value = true
}

// 处理主题颜色变化
const handleThemeChange = (color) => {
  document.documentElement.style.setProperty('--el-color-primary', color)
  ElMessage.success('主题色更新成功')
}

// 监听路由变化，确保菜单状态更新
watch(
  () => route.path,
  (newPath) => {
    console.log('路由变化:', newPath)
  }
)

// 路由更新前触发
onBeforeRouteUpdate((to, from) => {
  console.log('路由更新:', {
    from: from.path,
    to: to.path
  })
})

const handleChangePassword = () => {
  // TODO: 实现修改密码功能
  ElMessage.info('修改密码功能开发中')
}

const handleLogout = async () => {
  await userStore.logoutAction()
  router.push('/login')
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  display: flex;
  width: 100%;
  height: 100vh;
  transition: all 0.3s;
}

.sidebar-container {
  width: 210px;
  height: 100%;
  background-color: #304156;
  transition: width 0.28s ease-out;
  overflow: hidden;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  z-index: 1001;

  &.collapsed {
    width: 64px;
  }

  .logo-container {
    height: 50px;
    padding: 10px;
    display: flex;
    align-items: center;
    background: #2b2f3a;
    overflow: hidden;

    .logo {
      width: 32px;
      height: 32px;
      margin-right: 12px;
      transition: margin 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    .title {
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      overflow: hidden;
      white-space: nowrap;
      transition: opacity 0.3s;
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f0f2f5;

  .navbar {
    height: 50px;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    align-items: center;
    padding: 0 16px;
    justify-content: space-between;

    .hamburger-container {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 16px 0 0;
      cursor: pointer;

      .el-icon {
        font-size: 20px;
        transition: transform 0.3s;

        &.is-active {
          transform: rotate(180deg);
        }
      }
    }

    .breadcrumb-wrapper {
      flex: 1;
      padding-right: 16px;
    }

    .right-menu {
      display: flex;
      align-items: center;

      .right-menu-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 12px;
        height: 100%;
        font-size: 18px;
        cursor: pointer;
        transition: background 0.3s;

        &.hover-effect {
          &:hover {
            background: rgba(0, 0, 0, 0.025);
          }
        }
      }

      .avatar-container {
        margin-left: 10px;

        .avatar-wrapper {
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 0 8px;

          .name {
            margin-left: 8px;
            font-size: 14px;
            color: #606266;
          }

          .el-icon-caret-bottom {
            margin-left: 5px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .app-main {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    position: relative;
  }
}

// 路由切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

// 设置抽屉样式
.drawer-content {
  padding: 10px 20px;

  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #ebeef5;

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>