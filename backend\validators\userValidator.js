const { body, param } = require("express-validator");

// 创建用户验证规则
const createUserValidator = [
  body("username")
    .notEmpty()
    .withMessage("用户名不能为空")
    .isLength({ min: 3, max: 50 })
    .withMessage("用户名长度必须在3-50个字符之间")
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage("用户名只能包含字母、数字和下划线"),

  body("password")
    .notEmpty()
    .withMessage("密码不能为空")
    .isLength({ min: 6, max: 20 })
    .withMessage("密码长度必须在6-20个字符之间"),

  body("nickname")
    .optional()
    .isLength({ max: 50 })
    .withMessage("昵称长度不能超过50个字符"),

  body("email").optional().isEmail().withMessage("邮箱格式不正确"),

  body("phone")
    .optional()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage("手机号格式不正确"),

  body("status").optional().isIn([0, 1]).withMessage("状态值必须是0或1"),

  body("deptId").optional().isMongoId().withMessage("部门ID格式不正确"),

  body("roles").optional().isArray().withMessage("角色必须是数组格式"),

  body("roles.*").optional().isMongoId().withMessage("角色ID格式不正确"),

  body("remark")
    .optional()
    .isLength({ max: 500 })
    .withMessage("备注长度不能超过500个字符"),
];

// 更新用户验证规则
const updateUserValidator = [
  param("id").isMongoId().withMessage("用户ID格式不正确"),

  body("username")
    .optional()
    .isLength({ min: 3, max: 50 })
    .withMessage("用户名长度必须在3-50个字符之间")
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage("用户名只能包含字母、数字和下划线"),

  body("nickname")
    .optional()
    .isLength({ max: 50 })
    .withMessage("昵称长度不能超过50个字符"),

  body("email").optional().isEmail().withMessage("邮箱格式不正确"),

  body("phone")
    .optional()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage("手机号格式不正确"),

  body("status").optional().isIn([0, 1]).withMessage("状态值必须是0或1"),

  body("deptId").optional().isMongoId().withMessage("部门ID格式不正确"),

  body("roles").optional().isArray().withMessage("角色必须是数组格式"),

  body("roles.*").optional().isMongoId().withMessage("角色ID格式不正确"),

  body("remark")
    .optional()
    .isLength({ max: 500 })
    .withMessage("备注长度不能超过500个字符"),
];

// 重置密码验证规则
const resetPasswordValidator = [
  param("id").isMongoId().withMessage("用户ID格式不正确"),

  body("password")
    .notEmpty()
    .withMessage("密码不能为空")
    .isLength({ min: 6, max: 20 })
    .withMessage("密码长度必须在6-20个字符之间"),
];

// 用户状态验证规则
const userStatusValidator = [
  param("id").isMongoId().withMessage("用户ID格式不正确"),

  body("status")
    .notEmpty()
    .withMessage("状态不能为空")
    .isIn([0, 1])
    .withMessage("状态值必须是0或1"),
];

// 批量操作验证规则
const batchValidator = [
  body("ids").isArray({ min: 1 }).withMessage("请提供有效的ID数组"),

  body("ids.*").isMongoId().withMessage("ID格式不正确"),
];

// 批量状态更新验证规则
const batchStatusValidator = [
  ...batchValidator,
  body("status")
    .notEmpty()
    .withMessage("状态不能为空")
    .isIn([0, 1])
    .withMessage("状态值必须是0或1"),
];

// 注册验证规则
const registerValidator = [
  body("username")
    .notEmpty()
    .withMessage("用户名不能为空")
    .isLength({ min: 3, max: 50 })
    .withMessage("用户名长度必须在3-50个字符之间")
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage("用户名只能包含字母、数字和下划线"),

  body("password")
    .notEmpty()
    .withMessage("密码不能为空")
    .isLength({ min: 6, max: 20 })
    .withMessage("密码长度必须在6-20个字符之间"),

  body("email").optional().isEmail().withMessage("邮箱格式不正确"),

  body("phone")
    .optional()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage("手机号格式不正确"),
];

// 登录验证规则
const loginValidator = [
  body("username").notEmpty().withMessage("用户名不能为空"),
  body("password").notEmpty().withMessage("密码不能为空"),
];

// 修改密码验证规则
const changePasswordValidator = [
  body("oldPassword").notEmpty().withMessage("原密码不能为空"),
  body("newPassword")
    .notEmpty()
    .withMessage("新密码不能为空")
    .isLength({ min: 6, max: 20 })
    .withMessage("新密码长度必须在6-20个字符之间"),
  body("confirmPassword")
    .notEmpty()
    .withMessage("确认密码不能为空")
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error("确认密码与新密码不匹配");
      }
      return true;
    }),
];

module.exports = {
  createUserValidator,
  updateUserValidator,
  resetPasswordValidator,
  userStatusValidator,
  batchValidator,
  batchStatusValidator,
  registerValidator,
  loginValidator,
  changePasswordValidator,
};
