<template>
  <div class="profile-container">
    <el-row :gutter="20">
      <!-- 个人信息卡片 -->
      <el-col :span="8">
        <el-card class="profile-card">
          <template #header>
            <span>个人信息</span>
          </template>
          
          <div class="profile-info">
            <div class="avatar-section">
              <el-avatar :size="100" :src="userInfo.avatar" class="profile-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="avatar-actions">
                <el-button size="small" @click="showAvatarUpload = true">
                  更换头像
                </el-button>
              </div>
            </div>
            
            <div class="user-basic-info">
              <h3>{{ userInfo.nickname || userInfo.username }}</h3>
              <p class="user-role">
                <el-tag 
                  v-for="role in userInfo.roles" 
                  :key="role._id" 
                  type="primary" 
                  size="small"
                  class="role-tag"
                >
                  {{ role.name }}
                </el-tag>
              </p>
              <p class="user-dept">{{ userInfo.deptId?.name || '暂无部门' }}</p>
            </div>
          </div>
          
          <el-divider />
          
          <div class="profile-stats">
            <div class="stat-item">
              <div class="stat-value">{{ loginStats.totalLogins || 0 }}</div>
              <div class="stat-label">登录次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ loginStats.lastLogin || '-' }}</div>
              <div class="stat-label">最后登录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 详细信息和设置 -->
      <el-col :span="16">
        <el-tabs v-model="activeTab" class="profile-tabs">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-card>
              <el-form 
                ref="basicFormRef" 
                :model="basicForm" 
                :rules="basicFormRules" 
                label-width="100px"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="用户名">
                      <el-input v-model="basicForm.username" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="昵称" prop="nickname">
                      <el-input v-model="basicForm.nickname" placeholder="请输入昵称" />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="邮箱" prop="email">
                      <el-input v-model="basicForm.email" placeholder="请输入邮箱" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="手机号" prop="phone">
                      <el-input v-model="basicForm.phone" placeholder="请输入手机号" />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-form-item label="个人简介" prop="remark">
                  <el-input 
                    v-model="basicForm.remark" 
                    type="textarea" 
                    :rows="4"
                    placeholder="请输入个人简介" 
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="updateBasicInfo" :loading="basicLoading">
                    保存修改
                  </el-button>
                  <el-button @click="resetBasicForm">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-tab-pane>
          
          <!-- 安全设置 -->
          <el-tab-pane label="安全设置" name="security">
            <el-card>
              <el-form 
                ref="passwordFormRef" 
                :model="passwordForm" 
                :rules="passwordFormRules" 
                label-width="100px"
              >
                <el-form-item label="当前密码" prop="oldPassword">
                  <el-input 
                    v-model="passwordForm.oldPassword" 
                    type="password" 
                    placeholder="请输入当前密码" 
                    show-password 
                  />
                </el-form-item>
                
                <el-form-item label="新密码" prop="newPassword">
                  <el-input 
                    v-model="passwordForm.newPassword" 
                    type="password" 
                    placeholder="请输入新密码" 
                    show-password 
                  />
                </el-form-item>
                
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input 
                    v-model="passwordForm.confirmPassword" 
                    type="password" 
                    placeholder="请再次输入新密码" 
                    show-password 
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="changePassword" :loading="passwordLoading">
                    修改密码
                  </el-button>
                  <el-button @click="resetPasswordForm">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-tab-pane>
          
          <!-- 操作日志 -->
          <el-tab-pane label="操作日志" name="logs">
            <el-card>
              <el-table :data="operationLogs" v-loading="logsLoading">
                <el-table-column prop="action" label="操作" width="120" />
                <el-table-column prop="description" label="描述" />
                <el-table-column prop="ip" label="IP地址" width="120" />
                <el-table-column prop="userAgent" label="浏览器" width="200" show-overflow-tooltip />
                <el-table-column prop="createTime" label="操作时间" width="160" />
              </el-table>
              
              <div class="pagination-container">
                <el-pagination
                  :current-page="logsPagination.page"
                  :page-size="logsPagination.pageSize"
                  :total="logsPagination.total"
                  layout="prev, pager, next"
                  @current-change="handleLogsPageChange"
                />
              </div>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    
    <!-- 头像上传对话框 -->
    <el-dialog v-model="showAvatarUpload" title="更换头像" width="400px">
      <AvatarUpload 
        v-model="tempAvatar" 
        @success="handleAvatarSuccess"
      />
      <template #footer>
        <el-button @click="showAvatarUpload = false">取消</el-button>
        <el-button type="primary" @click="saveAvatar" :loading="avatarLoading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import { getUserInfo, updateUser, updateUserAvatar } from '@/api/user'
import { changePassword as changeUserPassword } from '@/api/auth'
import AvatarUpload from '@/components/Upload/AvatarUpload.vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 响应式数据
const activeTab = ref('basic')
const userInfo = ref({})
const basicLoading = ref(false)
const passwordLoading = ref(false)
const avatarLoading = ref(false)
const logsLoading = ref(false)
const showAvatarUpload = ref(false)
const tempAvatar = ref('')

// 登录统计
const loginStats = ref({
  totalLogins: 0,
  lastLogin: ''
})

// 基本信息表单
const basicFormRef = ref()
const basicForm = reactive({
  username: '',
  nickname: '',
  email: '',
  phone: '',
  remark: ''
})

const basicFormRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 密码修改表单
const passwordFormRef = ref()
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordFormRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (_, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 操作日志
const operationLogs = ref([])
const logsPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 初始化
onMounted(() => {
  loadUserInfo()
  loadOperationLogs()
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const res = await getUserInfo()
    userInfo.value = res.data
    
    // 填充基本信息表单
    Object.assign(basicForm, {
      username: res.data.username,
      nickname: res.data.nickname || '',
      email: res.data.email || '',
      phone: res.data.phone || '',
      remark: res.data.remark || ''
    })
    
    // 模拟登录统计数据
    loginStats.value = {
      totalLogins: 156,
      lastLogin: new Date().toLocaleString()
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 更新基本信息
const updateBasicInfo = () => {
  basicFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    basicLoading.value = true
    try {
      await updateUser(userInfo.value._id, basicForm)
      ElMessage.success('基本信息更新成功')
      
      // 更新用户信息
      Object.assign(userInfo.value, basicForm)
      
      // 更新store中的用户信息
      userStore.setUserInfo(userInfo.value)
    } catch (error) {
      console.error('更新基本信息失败:', error)
      ElMessage.error('更新基本信息失败')
    } finally {
      basicLoading.value = false
    }
  })
}

// 重置基本信息表单
const resetBasicForm = () => {
  Object.assign(basicForm, {
    username: userInfo.value.username,
    nickname: userInfo.value.nickname || '',
    email: userInfo.value.email || '',
    phone: userInfo.value.phone || '',
    remark: userInfo.value.remark || ''
  })
}

// 修改密码
const changePassword = () => {
  passwordFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    passwordLoading.value = true
    try {
      await changeUserPassword({
        oldPassword: passwordForm.oldPassword,
        newPassword: passwordForm.newPassword
      })
      ElMessage.success('密码修改成功')
      resetPasswordForm()
    } catch (error) {
      console.error('密码修改失败:', error)
      ElMessage.error('密码修改失败')
    } finally {
      passwordLoading.value = false
    }
  })
}

// 重置密码表单
const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  passwordFormRef.value?.resetFields()
}

// 头像上传成功
const handleAvatarSuccess = (response) => {
  tempAvatar.value = response.data.url
}

// 保存头像
const saveAvatar = async () => {
  if (!tempAvatar.value) {
    ElMessage.warning('请先上传头像')
    return
  }
  
  avatarLoading.value = true
  try {
    await updateUserAvatar(userInfo.value._id, tempAvatar.value)
    userInfo.value.avatar = tempAvatar.value
    showAvatarUpload.value = false
    ElMessage.success('头像更新成功')
    
    // 更新store中的用户信息
    userStore.setUserInfo(userInfo.value)
  } catch (error) {
    console.error('头像更新失败:', error)
    ElMessage.error('头像更新失败')
  } finally {
    avatarLoading.value = false
  }
}

// 加载操作日志
const loadOperationLogs = async () => {
  logsLoading.value = true
  try {
    // 模拟操作日志数据
    operationLogs.value = [
      {
        action: '登录系统',
        description: '用户登录系统',
        ip: '*************',
        userAgent: 'Chrome 120.0.0.0',
        createTime: '2024-01-15 10:30:00'
      },
      {
        action: '修改资料',
        description: '更新个人基本信息',
        ip: '*************',
        userAgent: 'Chrome 120.0.0.0',
        createTime: '2024-01-15 14:20:00'
      }
    ]
    logsPagination.total = 2
  } catch (error) {
    console.error('获取操作日志失败:', error)
  } finally {
    logsLoading.value = false
  }
}

// 操作日志分页
const handleLogsPageChange = (page) => {
  logsPagination.page = page
  loadOperationLogs()
}
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 20px;
}

.profile-card {
  height: fit-content;
}

.profile-info {
  text-align: center;
  
  .avatar-section {
    margin-bottom: 20px;
    
    .profile-avatar {
      border: 3px solid #f0f0f0;
      margin-bottom: 10px;
    }
  }
  
  .user-basic-info {
    h3 {
      margin: 0 0 10px 0;
      color: #303133;
    }
    
    .user-role {
      margin: 10px 0;
      
      .role-tag {
        margin-right: 5px;
      }
    }
    
    .user-dept {
      color: #909399;
      font-size: 14px;
      margin: 0;
    }
  }
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  
  .stat-item {
    text-align: center;
    
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 5px;
    }
    
    .stat-label {
      font-size: 12px;
      color: #909399;
    }
  }
}

.profile-tabs {
  :deep(.el-tabs__content) {
    padding-top: 20px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}
</style>