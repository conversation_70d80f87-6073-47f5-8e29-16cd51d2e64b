import request from "@/utils/request";

// 用户登录
export function login(data) {
  return request({
    url: "/auth/login",
    method: "post",
    data,
  });
}

// 用户注册
export function register(data) {
  return request({
    url: "/auth/register",
    method: "post",
    data,
  });
}

// 用户退出登录
export function logout() {
  return request({
    url: "/auth/logout",
    method: "post",
  });
}

// 获取当前用户信息
export function getCurrentUser() {
  return request({
    url: "/auth/profile",
    method: "get",
  });
}

// 获取用户信息（别名）
export function getInfo() {
  return getCurrentUser();
}

// 刷新Token
export function refreshToken() {
  return request({
    url: "/auth/refresh",
    method: "post",
  });
}

// 修改密码
export function changePassword(data) {
  return request({
    url: "/auth/change-password",
    method: "post",
    data,
  });
}

// 忘记密码
export function forgotPassword(data) {
  return request({
    url: "/auth/forgot-password",
    method: "post",
    data,
  });
}

// 重置密码
export function resetPassword(data) {
  return request({
    url: "/auth/reset-password",
    method: "post",
    data,
  });
}

// 验证邮箱
export function verifyEmail(token) {
  return request({
    url: `/auth/verify-email/${token}`,
    method: "get",
  });
}

// 发送验证码
export function sendVerificationCode(data) {
  return request({
    url: "/auth/send-verification-code",
    method: "post",
    data,
  });
}

// 验证验证码
export function verifyCode(data) {
  return request({
    url: "/auth/verify-code",
    method: "post",
    data,
  });
}
