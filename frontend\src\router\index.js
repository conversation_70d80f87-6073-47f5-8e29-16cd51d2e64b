import { createRouter, createWebHashHistory } from "vue-router";
import { useUserStore } from "@/stores/user";
import { useTagsViewStore } from "@/stores/tagsView";
import { usePermissionStore } from "@/stores/permission";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
// 导入routes.js中定义的路由
import {
  constantRoutes as baseRoutes,
  asyncRoutes as baseAsyncRoutes,
} from "./routes";

// 路由白名单
const whiteList = ["/login", "/register"];

// 添加register和redirect路由到constantRoutes
const constantRoutes = [
  {
    path: "/redirect",
    component: () => import("@/layout/index.vue"),
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
        meta: { title: "重定向" },
      },
    ],
  },
  {
    path: "/register",
    component: () => import("@/views/register/index.vue"),
    hidden: true,
    meta: { title: "注册" },
  },
  {
    path: "/user",
    component: () => import("@/layout/index.vue"),
    hidden: true,
    children: [
      {
        path: "profile",
        component: () => import("@/views/user/profile/index.vue"),
        name: "Profile",
        meta: { title: "个人中心" },
      },
    ],
  },
  {
    path: "/401",
    component: () => import("@/views/error-page/401.vue"),
    hidden: true,
    meta: { title: "401" },
  },
  {
    path: "/404",
    component: () => import("@/views/error-page/404.vue"),
    hidden: true,
    meta: { title: "404" },
  },
  // 调试路由（仅开发环境）
  ...(import.meta.env.MODE === "development"
    ? [
        {
          path: "/debug",
          component: () => import("@/layout/index.vue"),
          hidden: true,
          meta: { title: "调试工具" },
          children: [
            {
              path: "request-test",
              name: "RequestTest",
              component: () => import("@/views/debug/RequestTest.vue"),
              meta: { title: "请求测试" },
            },
            {
              path: "login-test",
              name: "LoginTest",
              component: () => import("@/views/debug/LoginTest.vue"),
              meta: { title: "登录测试" },
            },
          ],
        },
      ]
    : []),
  // 合并从routes.js导入的基础路由
  ...baseRoutes,
];

// 创建路由
const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  // 平滑滚动
  scrollBehavior(_, __, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

// 动态路由，基于用户权限动态去加载
// 使用从routes.js导入的异步路由
const dynamicRoutes = [
  ...baseAsyncRoutes,
  // 404页面必须放在最后
  { path: "/:pathMatch(.*)*", redirect: "/404", hidden: true },
];

NProgress.configure({ showSpinner: false });

// 白名单路径，直接跳转
const isWhiteList = (to) => {
  return whiteList.indexOf(to.path) !== -1;
};

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start();

  // 设置页面标题
  document.title = to.meta.title
    ? `${to.meta.title} - 后台管理系统`
    : "后台管理系统";

  const userStore = useUserStore();
  const permissionStore = usePermissionStore();
  const hasToken = userStore.token;

  if (hasToken) {
    if (to.path === "/login") {
      // 已登录，跳转到首页
      next({ path: "/" });
      NProgress.done();
    } else {
      // 判断是否已获取用户信息
      if (userStore.roles.length === 0) {
        try {
          // 获取用户信息
          await userStore.getUserInfo();

          // 使用permissionStore生成路由
          const accessRoutes = await permissionStore.generateRoutes(
            userStore.roles
          );

          // 动态添加路由
          accessRoutes.forEach((route) => {
            router.addRoute(route);
          });

          // 初始化标签视图
          const tagsViewStore = useTagsViewStore();
          if (to.path !== from.path) {
            tagsViewStore.addVisitedView(to);
            tagsViewStore.addCachedView(to);
          }

          // 确保路由已添加完成
          next({ ...to, replace: true });
        } catch (error) {
          console.error("路由守卫错误", error);
          // 移除token并跳转登录页
          await userStore.resetToken();
          next(`/login?redirect=${to.path}`);
          NProgress.done();
        }
      } else {
        next();
      }
    }
  } else {
    // 未登录
    if (isWhiteList(to)) {
      // 白名单路径直接跳转
      next();
    } else {
      // 非白名单路径重定向到登录页
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});

export { constantRoutes, dynamicRoutes };
export default router;
