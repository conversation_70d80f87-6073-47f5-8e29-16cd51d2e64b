const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const Role = require('../models/Role');
const Permission = require('../models/Permission');
const Department = require('../models/Department');
const config = require('../config/config');

async function addSampleData() {
  try {
    console.log('🔍 连接数据库...');
    await mongoose.connect(config.mongodbUri);
    console.log('✅ 数据库连接成功\n');

    // 获取现有的总公司部门
    const rootDept = await Department.findOne({ name: '总公司' });
    if (!rootDept) {
      console.log('❌ 未找到总公司部门，请先运行初始化脚本');
      return;
    }

    // 创建子部门
    console.log('🏢 创建部门结构...');
    const departments = [
      {
        name: '技术部',
        parentId: rootDept._id,
        ancestors: rootDept._id.toString(),
        leader: '张技术',
        phone: '13800138001',
        email: '<EMAIL>',
        status: 1,
        sort: 1,
      },
      {
        name: '市场部',
        parentId: rootDept._id,
        ancestors: rootDept._id.toString(),
        leader: '李市场',
        phone: '13800138002',
        email: '<EMAIL>',
        status: 1,
        sort: 2,
      },
      {
        name: '人事部',
        parentId: rootDept._id,
        ancestors: rootDept._id.toString(),
        leader: '王人事',
        phone: '13800138003',
        email: '<EMAIL>',
        status: 1,
        sort: 3,
      },
      {
        name: '财务部',
        parentId: rootDept._id,
        ancestors: rootDept._id.toString(),
        leader: '赵财务',
        phone: '13800138004',
        email: '<EMAIL>',
        status: 1,
        sort: 4,
      }
    ];

    const createdDepts = [];
    for (const deptData of departments) {
      const existingDept = await Department.findOne({ name: deptData.name });
      if (!existingDept) {
        const dept = await Department.create(deptData);
        createdDepts.push(dept);
        console.log(`   ✅ 创建部门: ${dept.name}`);
      } else {
        createdDepts.push(existingDept);
        console.log(`   ⚠️  部门已存在: ${existingDept.name}`);
      }
    }

    // 创建更多角色
    console.log('\n🎭 创建角色...');
    const systemPermissions = await Permission.find({ 
      code: { $in: ['system:user:query', 'system:user:list'] } 
    });
    
    const roles = [
      {
        name: '部门经理',
        code: 'manager',
        status: 1,
        permissions: systemPermissions.map(p => p._id),
        remark: '部门经理角色，拥有部分管理权限',
      },
      {
        name: '普通员工',
        code: 'employee',
        status: 1,
        permissions: [],
        remark: '普通员工角色，基础权限',
      },
      {
        name: '财务专员',
        code: 'finance',
        status: 1,
        permissions: [],
        remark: '财务专员角色',
      }
    ];

    const createdRoles = [];
    for (const roleData of roles) {
      const existingRole = await Role.findOne({ code: roleData.code });
      if (!existingRole) {
        const role = await Role.create(roleData);
        createdRoles.push(role);
        console.log(`   ✅ 创建角色: ${role.name}`);
      } else {
        createdRoles.push(existingRole);
        console.log(`   ⚠️  角色已存在: ${existingRole.name}`);
      }
    }

    // 创建示例用户
    console.log('\n👥 创建示例用户...');
    const users = [
      {
        username: 'zhangsan',
        password: await bcrypt.hash('123456', config.bcrypt.saltRounds),
        nickname: '张三',
        email: '<EMAIL>',
        phone: '13800138101',
        status: 1,
        deptId: createdDepts.find(d => d.name === '技术部')?._id || rootDept._id,
        roles: [createdRoles.find(r => r.code === 'manager')?._id].filter(Boolean),
        remark: '技术部经理',
      },
      {
        username: 'lisi',
        password: await bcrypt.hash('123456', config.bcrypt.saltRounds),
        nickname: '李四',
        email: '<EMAIL>',
        phone: '13800138102',
        status: 1,
        deptId: createdDepts.find(d => d.name === '市场部')?._id || rootDept._id,
        roles: [createdRoles.find(r => r.code === 'manager')?._id].filter(Boolean),
        remark: '市场部经理',
      },
      {
        username: 'wangwu',
        password: await bcrypt.hash('123456', config.bcrypt.saltRounds),
        nickname: '王五',
        email: '<EMAIL>',
        phone: '13800138103',
        status: 1,
        deptId: createdDepts.find(d => d.name === '技术部')?._id || rootDept._id,
        roles: [createdRoles.find(r => r.code === 'employee')?._id].filter(Boolean),
        remark: '前端开发工程师',
      },
      {
        username: 'zhaoliu',
        password: await bcrypt.hash('123456', config.bcrypt.saltRounds),
        nickname: '赵六',
        email: '<EMAIL>',
        phone: '13800138104',
        status: 1,
        deptId: createdDepts.find(d => d.name === '财务部')?._id || rootDept._id,
        roles: [createdRoles.find(r => r.code === 'finance')?._id].filter(Boolean),
        remark: '财务专员',
      },
      {
        username: 'sunqi',
        password: await bcrypt.hash('123456', config.bcrypt.saltRounds),
        nickname: '孙七',
        email: '<EMAIL>',
        phone: '13800138105',
        status: 0, // 禁用状态
        deptId: createdDepts.find(d => d.name === '人事部')?._id || rootDept._id,
        roles: [createdRoles.find(r => r.code === 'employee')?._id].filter(Boolean),
        remark: '人事专员（已离职）',
      }
    ];

    for (const userData of users) {
      const existingUser = await User.findOne({ username: userData.username });
      if (!existingUser) {
        const user = await User.create(userData);
        console.log(`   ✅ 创建用户: ${user.username} (${user.nickname})`);
      } else {
        console.log(`   ⚠️  用户已存在: ${existingUser.username}`);
      }
    }

    // 显示最终统计
    console.log('\n📊 数据添加完成，最终统计：');
    const finalStats = {
      users: await User.countDocuments(),
      roles: await Role.countDocuments(),
      permissions: await Permission.countDocuments(),
      departments: await Department.countDocuments(),
    };

    console.log(`👥 用户总数: ${finalStats.users}`);
    console.log(`🎭 角色总数: ${finalStats.roles}`);
    console.log(`🔐 权限总数: ${finalStats.permissions}`);
    console.log(`🏢 部门总数: ${finalStats.departments}`);

    console.log('\n🔑 测试账号信息：');
    console.log('管理员账号：admin / 123456');
    console.log('部门经理账号：zhangsan / 123456 (技术部经理)');
    console.log('部门经理账号：lisi / 123456 (市场部经理)');
    console.log('普通员工账号：wangwu / 123456 (前端开发)');
    console.log('财务专员账号：zhaoliu / 123456 (财务专员)');
    console.log('禁用账号：sunqi / 123456 (已禁用)');

    await mongoose.disconnect();
    console.log('\n✅ 示例数据添加完成！');

  } catch (error) {
    console.error('❌ 添加示例数据失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

addSampleData();
