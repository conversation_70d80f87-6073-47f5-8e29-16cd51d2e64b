const { validationResult } = require("express-validator");
const { error } = require("../utils/response");

/**
 * 验证请求数据
 * 使用express-validator验证请求数据，并返回统一格式的错误响应
 */
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    // 格式化错误信息
    const formattedErrors = {};
    errors.array().forEach((err) => {
      formattedErrors[err.param] = err.msg;
    });

    return error(res, "请求数据验证失败", 400, formattedErrors);
  }
  next();
};

module.exports = {
  validate,
};
