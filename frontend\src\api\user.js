import request from "@/utils/request";

// 用户登录
export function login(data) {
  return request({
    url: "/auth/login",
    method: "post",
    data,
  });
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: "/users/me",
    method: "get",
  });
}

// 用户退出登录
export function logout() {
  return request({
    url: "/auth/logout",
    method: "post",
  });
}

// 获取用户列表
export function getUserList(params) {
  return request({
    url: "/users",
    method: "get",
    params,
  });
}

// 创建用户
export function createUser(data) {
  return request({
    url: "/users",
    method: "post",
    data,
  });
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/users/${id}`,
    method: "put",
    data,
  });
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/users/${id}`,
    method: "delete",
  });
}

// 批量删除用户
export function batchDeleteUsers(ids) {
  return request({
    url: "/users",
    method: "delete",
    data: { ids },
  });
}

// 重置密码
export function resetPassword(id, data) {
  return request({
    url: `/users/${id}/password-reset`,
    method: "patch",
    data,
  });
}

// 更新用户状态
export function updateUserStatus(id, status) {
  console.log("用户状态更新 - 原始数据:", {
    id,
    status,
    statusType: typeof status,
  });

  // 确保status是数字类型，防止数据格式错误
  let numericStatus = typeof status === "number" ? status : Number(status);

  // 如果转换失败，尝试其他方式
  if (isNaN(numericStatus)) {
    numericStatus = status ? 1 : 0;
  }

  // 强制转换为0或1
  numericStatus = numericStatus ? 1 : 0;

  console.log("用户状态更新 - 最终值:", numericStatus);

  return request({
    url: `/users/${id}/status`,
    method: "patch",
    data: { status: numericStatus },
  });
}

// 更新用户头像
export function updateUserAvatar(id, avatar) {
  return request({
    url: `/users/${id}/avatar`,
    method: "patch",
    data: { avatar },
  });
}

// 获取用户统计信息
export function getUserStats() {
  return request({
    url: "/users/stats/overview",
    method: "get",
  });
}

// 导出用户数据
export function exportUserData() {
  return request({
    url: "/users/export/data",
    method: "get",
  });
}

// 批量更新用户状态
export function batchUpdateUserStatus(ids, status) {
  return request({
    url: "/users/batch/status",
    method: "patch",
    data: { ids, status },
  });
}

// 获取用户详情（包含完整信息）
export function getUserDetail(id) {
  return request({
    url: `/users/${id}`,
    method: "get",
  });
}
