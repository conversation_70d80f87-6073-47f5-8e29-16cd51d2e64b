# 🚀 基于 Node.js 和 Vue3 的后台管理系统

[![Node.js Version](https://img.shields.io/badge/node-%3E%3D16.0.0-brightgreen.svg)](https://nodejs.org/)
[![Vue.js Version](https://img.shields.io/badge/vue-3.x-brightgreen.svg)](https://vuejs.org/)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

这是一个使用现代技术栈构建的企业级后台管理系统，提供完整的用户权限管理、系统监控、文件上传等功能。

## ✨ 功能特性

### 🔐 权限管理

- **用户管理**：用户的增删改查、密码重置、状态管理、头像上传
- **角色管理**：角色的增删改查、权限分配、状态管理
- **权限管理**：菜单权限、按钮权限、API 权限的统一管理
- **部门管理**：树形结构的部门管理，支持层级关系

### 🛡️ 安全特性

- **JWT 认证**：基于 Token 的身份认证
- **权限控制**：细粒度的权限控制系统
- **安全防护**：请求频率限制、安全头设置、输入验证
- **密码安全**：bcrypt 加密存储，强密码策略

### 📊 系统监控

- **实时监控**：系统资源使用情况监控
- **健康检查**：数据库连接、内存使用、磁盘空间检查
- **日志管理**：结构化日志记录和查看
- **统计信息**：用户、角色、权限等数据统计

### 📁 文件管理

- **文件上传**：支持多种文件格式上传
- **头像上传**：用户头像上传和管理
- **文件预览**：图片预览、文件信息查看
- **存储管理**：文件分类存储、自动清理

### ⚡ 开发体验

- **热重载**：开发环境自动重启
- **API 文档**：Swagger 自动生成 API 文档
- **代码规范**：ESLint + Prettier 代码格式化
- **开发工具**：丰富的开发和调试工具

## 🛠️ 技术栈

### 后端技术

- **Node.js** - JavaScript 运行环境
- **Express.js** - Web 应用框架
- **MongoDB** - NoSQL 数据库
- **Mongoose** - MongoDB 对象建模工具
- **JWT** - JSON Web Token 认证
- **bcryptjs** - 密码加密
- **express-validator** - 数据验证
- **winston** - 日志管理
- **multer** - 文件上传
- **nodemailer** - 邮件发送
- **node-cron** - 定时任务
- **helmet** - 安全中间件

### 前端技术

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 前端构建工具
- **Pinia** - 状态管理
- **Element Plus** - UI 组件库
- **Vue Router** - 路由管理
- **Axios** - HTTP 客户端
- **SCSS** - CSS 预处理器

## 📁 项目结构

```
ruo-admin/
├── backend/                 # 后端项目
│   ├── config/             # 配置文件
│   │   ├── config.js       # 应用配置
│   │   ├── database.js     # 数据库配置
│   │   └── swagger.js      # API文档配置
│   ├── controllers/        # 控制器
│   │   ├── authController.js
│   │   ├── userController.js
│   │   ├── roleController.js
│   │   ├── permissionController.js
│   │   ├── departmentController.js
│   │   └── systemController.js
│   ├── middleware/         # 中间件
│   │   ├── auth.js         # 认证中间件
│   │   ├── permission.js   # 权限中间件
│   │   ├── security.js     # 安全中间件
│   │   ├── validation.js   # 验证中间件
│   │   └── upload.js       # 上传中间件
│   ├── models/             # 数据模型
│   │   ├── User.js
│   │   ├── Role.js
│   │   ├── Permission.js
│   │   └── Department.js
│   ├── routes/             # 路由
│   │   ├── auth.js
│   │   ├── users.js
│   │   ├── roles.js
│   │   ├── permissions.js
│   │   ├── departments.js
│   │   ├── system.js
│   │   └── upload.js
│   ├── services/           # 服务层
│   │   ├── cronService.js  # 定时任务服务
│   │   └── emailService.js # 邮件服务
│   ├── utils/              # 工具函数
│   ├── logs/               # 日志文件
│   ├── uploads/            # 上传文件
│   └── app.js              # 应用入口
│
├── frontend/               # 前端项目
│   ├── public/             # 静态资源
│   ├── src/
│   │   ├── api/            # API请求
│   │   ├── assets/         # 资源文件
│   │   ├── components/     # 公共组件
│   │   │   ├── Upload/     # 上传组件
│   │   │   ├── Table/      # 表格组件
│   │   │   └── Form/       # 表单组件
│   │   ├── directives/     # 自定义指令
│   │   ├── hooks/          # 组合式函数
│   │   ├── layout/         # 布局组件
│   │   ├── router/         # 路由配置
│   │   ├── stores/         # Pinia状态管理
│   │   ├── styles/         # 样式文件
│   │   ├── utils/          # 工具函数
│   │   ├── views/          # 页面组件
│   │   │   ├── system/     # 系统管理页面
│   │   │   ├── login/      # 登录页面
│   │   │   └── dashboard/  # 仪表盘
│   │   └── main.js         # 入口文件
│   └── vite.config.js      # Vite配置
│
├── scripts/                # 脚本文件
│   ├── setup.js           # 项目初始化脚本
│   ├── dev-tools.js       # 开发工具脚本
│   └── deploy.sh          # 部署脚本
│
├── docs/                   # 文档
├── docker-compose.yml      # Docker编排文件
└── README.md              # 项目说明
```

## 🚀 快速开始

### 环境要求

- **Node.js** >= 16.0.0
- **MongoDB** >= 5.0
- **npm** >= 8.0.0

### 一键安装

```bash
# 克隆项目
git clone <repository-url>
cd ruo-admin

# 运行安装脚本（推荐）
npm run setup
```

### 手动安装

```bash
# 1. 安装依赖
npm run install:all

# 2. 配置环境变量
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env.development

# 3. 初始化数据库
npm run init-db

# 4. 启动开发服务器
npm run dev
```

### 访问应用

- **前端地址**: http://localhost:5173
- **后端地址**: http://localhost:3000
- **API 文档**: http://localhost:3000/api-docs

### 默认账号

- **用户名**: admin
- **密码**: 123456

## 🔧 开发工具

项目提供了丰富的开发工具命令：

```bash
# 项目状态检查
npm run tools:check

# 清理项目文件
npm run tools:clean

# 重置项目数据
npm run tools:reset

# 数据库备份
npm run tools:backup

# 数据库恢复
npm run tools:restore

# 查看日志
npm run tools:logs

# 查看所有工具命令
npm run tools
```

## 📊 系统监控

访问系统监控页面查看：

- 系统资源使用情况
- 数据库连接状态
- 应用统计信息
- 内存使用情况
- 健康检查状态

## 🔒 安全特性

### 认证安全

- JWT Token 认证
- 密码强度验证
- 登录频率限制
- 会话管理

### 数据安全

- 输入数据验证
- SQL 注入防护
- XSS 攻击防护
- CSRF 保护

### 系统安全

- 安全 HTTP 头设置
- API 频率限制
- 文件上传安全检查
- 敏感信息脱敏

## 🚀 部署指南

### Docker 部署（推荐）

```bash
# 生产环境部署
npm run deploy:prod

# 开发环境部署
npm run deploy:dev

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 手动部署

```bash
# 构建前端
npm run build

# 启动后端服务
cd backend
npm install --production
npm start
```

## 📝 环境变量配置

### 后端环境变量 (backend/.env)

```bash
# 服务器配置
PORT=3000
NODE_ENV=production

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ruo-admin

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# 邮件配置
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads

# 安全配置
RATE_LIMIT_MAX=100
LOGIN_RATE_LIMIT_MAX=5
```

### 前端环境变量 (frontend/.env.development)

```bash
# API配置
VITE_APP_BASE_API=http://localhost:3000/api/v1

# 应用配置
VITE_APP_TITLE=后台管理系统
VITE_APP_VERSION=1.0.0
```

## 📚 API 文档

启动开发服务器后，访问 http://localhost:3000/api-docs 查看完整的 API 文档。

### 主要 API 端点

| 模块 | 端点                  | 描述                 |
| ---- | --------------------- | -------------------- |
| 认证 | `/api/v1/auth`        | 登录、注册、密码重置 |
| 用户 | `/api/v1/users`       | 用户管理 CRUD        |
| 角色 | `/api/v1/roles`       | 角色管理 CRUD        |
| 权限 | `/api/v1/permissions` | 权限管理 CRUD        |
| 部门 | `/api/v1/departments` | 部门管理 CRUD        |
| 系统 | `/api/v1/system`      | 系统监控和管理       |
| 上传 | `/api/v1/upload`      | 文件上传管理         |

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 查看 [文档](docs/)
2. 搜索 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
