#!/usr/bin/env node

/**
 * 请求调试脚本
 * 用于调试前端请求路径问题
 */

const http = require('http');

/**
 * 发送HTTP请求
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    console.log('发送请求:', {
      hostname: options.hostname,
      port: options.port,
      path: options.path,
      method: options.method,
      headers: options.headers
    });
    
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsed
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * 测试不同的请求路径
 */
async function testPaths() {
  console.log('🧪 测试不同的请求路径...\n');
  
  const testCases = [
    {
      name: '直接访问后端 - 完整路径',
      options: {
        hostname: 'localhost',
        port: 3000,
        path: '/api/v1/permissions/68635ae95af3638ae7ffe7cf/status',
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        }
      }
    },
    {
      name: '通过前端代理 - 完整路径',
      options: {
        hostname: 'localhost',
        port: 5173,
        path: '/api/v1/permissions/68635ae95af3638ae7ffe7cf/status',
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        }
      }
    },
    {
      name: '错误的路径（缺少前缀）',
      options: {
        hostname: 'localhost',
        port: 3000,
        path: '/68635ae95af3638ae7ffe7cf/status',
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        }
      }
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`🔍 测试: ${testCase.name}`);
    
    try {
      const result = await makeRequest(testCase.options, { status: 1 });
      console.log(`   状态码: ${result.statusCode}`);
      
      if (result.data && typeof result.data === 'object') {
        console.log(`   响应: ${JSON.stringify(result.data, null, 2)}`);
      } else {
        console.log(`   响应: ${result.data}`);
      }
    } catch (error) {
      console.log(`   错误: ${error.message}`);
    }
    
    console.log('');
  }
}

/**
 * 检查环境变量
 */
function checkEnvironment() {
  console.log('🔧 检查环境配置...\n');
  
  // 模拟前端环境变量
  const frontendEnv = {
    VITE_APP_BASE_API: '/api/v1',
    VITE_APP_UPLOAD_URL: '/api/v1/upload',
    VITE_USE_MOCK: 'false'
  };
  
  console.log('前端环境变量:');
  Object.entries(frontendEnv).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });
  
  console.log('\n预期的请求URL构建:');
  const baseAPI = frontendEnv.VITE_APP_BASE_API;
  const permissionId = '68635ae95af3638ae7ffe7cf';
  const expectedURL = `${baseAPI}/permissions/${permissionId}/status`;
  console.log(`   baseURL: ${baseAPI}`);
  console.log(`   相对路径: /permissions/${permissionId}/status`);
  console.log(`   完整URL: ${expectedURL}`);
  
  console.log('\n');
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始请求路径调试...\n');
  
  // 检查环境配置
  checkEnvironment();
  
  // 测试不同路径
  await testPaths();
  
  console.log('💡 调试建议:');
  console.log('1. 检查前端网络面板中的实际请求URL');
  console.log('2. 确认 baseURL 配置是否正确');
  console.log('3. 检查代理配置是否正常工作');
  console.log('4. 验证请求拦截器是否修改了URL');
  
  console.log('\n🎉 调试完成！');
}

// 运行调试
if (require.main === module) {
  main().catch(console.error);
}
