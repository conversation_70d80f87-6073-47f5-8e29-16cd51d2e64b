{"name": "frontend", "version": "1.0.0", "description": "基于Vue3的后台管理系统前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "keywords": ["vue3", "admin", "dashboard", "element-plus"], "author": "", "license": "MIT", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.7", "element-plus": "^2.5.6", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "path-browserify": "^1.0.1", "sass": "^1.89.2", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^5.1.4"}}