<template>
    <div class="register-container">
        <div class="register-box">
            <div class="register-form-container">
                <div class="title-container">
                    <h2 class="title">后台管理系统</h2>
                    <h3 class="subtitle">欢迎注册！请填写以下信息</h3>
                </div>

                <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" class="register-form">
                    <el-form-item prop="username">
                        <el-input v-model="registerForm.username" placeholder="请输入用户名" :prefix-icon="User" size="large"
                            clearable />
                    </el-form-item>

                    <el-form-item prop="password">
                        <el-input v-model="registerForm.password" :type="passwordVisible ? 'text' : 'password'"
                            placeholder="请输入密码" :prefix-icon="Lock" size="large" clearable show-password />
                    </el-form-item>

                    <el-form-item prop="confirmPassword">
                        <el-input v-model="registerForm.confirmPassword" :type="passwordVisible ? 'text' : 'password'"
                            placeholder="请确认密码" :prefix-icon="Lock" size="large" clearable show-password />
                    </el-form-item>

                    <el-form-item prop="email">
                        <el-input v-model="registerForm.email" placeholder="请输入邮箱" :prefix-icon="Message" size="large"
                            clearable />
                    </el-form-item>

                    <div class="agreement">
                        <el-checkbox v-model="agreeTerms">我已阅读并同意<a href="javascript:;">用户协议</a>和<a
                                href="javascript:;">隐私政策</a></el-checkbox>
                    </div>

                    <el-button :loading="loading" type="primary" class="register-button" size="large"
                        @click="handleRegister">
                        {{ loading ? '注册中...' : '注册' }}
                    </el-button>

                    <div class="login-link">
                        已有账号？<router-link to="/login">立即登录</router-link>
                    </div>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { User, Lock, Message } from '@element-plus/icons-vue';

const router = useRouter();
const registerFormRef = ref(null);
const loading = ref(false);
const passwordVisible = ref(false);
const agreeTerms = ref(false);

const registerForm = reactive({
    username: '',
    password: '',
    confirmPassword: '',
    email: ''
});

const validatePass = (rule, value, callback) => {
    if (value === '') {
        callback(new Error('请输入密码'));
    } else {
        if (registerForm.confirmPassword !== '') {
            registerFormRef.value.validateField('confirmPassword');
        }
        callback();
    }
};

const validateConfirmPass = (rule, value, callback) => {
    if (value === '') {
        callback(new Error('请再次输入密码'));
    } else if (value !== registerForm.password) {
        callback(new Error('两次输入密码不一致!'));
    } else {
        callback();
    }
};

const registerRules = {
    username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, message: '用户名长度不能小于3位', trigger: 'blur' }
    ],
    password: [
        { required: true, validator: validatePass, trigger: 'blur' },
        { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
    ],
    confirmPassword: [
        { required: true, validator: validateConfirmPass, trigger: 'blur' }
    ],
    email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ]
};

const handleRegister = async () => {
    if (!agreeTerms.value) {
        ElMessage.warning('请阅读并同意用户协议和隐私政策');
        return;
    }

    try {
        await registerFormRef.value.validate();

        loading.value = true;
        // 这里添加注册逻辑，调用API进行注册
        // await userApi.register(registerForm);

        // 模拟注册成功
        setTimeout(() => {
            ElMessage.success('注册成功，请登录');
            router.push('/login');
        }, 1500);
    } catch (error) {
        console.error('注册失败:', error);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.register-container {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;

    .register-box {
        width: 460px;
        padding: 40px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

        .register-form-container {
            .title-container {
                text-align: center;
                margin-bottom: 40px;

                .title {
                    font-size: 28px;
                    color: #333;
                    margin-bottom: 10px;
                    font-weight: 600;
                }

                .subtitle {
                    font-size: 16px;
                    color: #666;
                    font-weight: normal;
                }
            }

            .register-form {
                :deep(.el-input) {
                    .el-input__wrapper {
                        background-color: #f5f7fa;
                        border: 1px solid #e4e7ed;
                        border-radius: 8px;
                        padding: 0 15px;
                        height: 48px;
                        transition: all 0.3s;

                        &:hover,
                        &.is-focus {
                            border-color: #409eff;
                            background-color: #fff;
                        }
                    }

                    .el-input__inner {
                        height: 48px;
                        font-size: 16px;
                    }
                }

                .agreement {
                    margin: 20px 0;

                    a {
                        color: #409eff;
                        text-decoration: none;

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }

                .register-button {
                    width: 100%;
                    height: 48px;
                    font-size: 16px;
                    font-weight: 500;
                    margin-bottom: 20px;
                    border-radius: 8px;
                    background: linear-gradient(to right, #409eff, #66b1ff);
                    border: none;

                    &:hover {
                        background: linear-gradient(to right, #66b1ff, #409eff);
                    }
                }

                .login-link {
                    text-align: center;
                    font-size: 14px;
                    color: #666;

                    a {
                        color: #409eff;
                        text-decoration: none;

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
            }
        }
    }
}

// 响应式设计
@media screen and (max-width: 576px) {
    .register-container {
        .register-box {
            width: 90%;
            padding: 20px;
        }
    }
}
</style>