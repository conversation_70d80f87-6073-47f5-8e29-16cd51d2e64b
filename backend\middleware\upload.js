const multer = require("multer");
const path = require("path");
const fs = require("fs");

// 确保上传目录存在
const ensureUploadDir = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

// 文件存储配置
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, "../uploads");
    ensureUploadDir(uploadPath);

    // 根据文件类型创建子目录
    let subDir = "others";
    if (file.mimetype.startsWith("image/")) {
      subDir = "images";
    } else if (file.mimetype.startsWith("video/")) {
      subDir = "videos";
    } else if (
      file.mimetype.includes("document") ||
      file.mimetype.includes("pdf")
    ) {
      subDir = "documents";
    }

    const finalPath = path.join(uploadPath, subDir);
    ensureUploadDir(finalPath);
    cb(null, finalPath);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名：时间戳 + 随机数 + 原扩展名
    const timestamp = Date.now();
    const random = Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    cb(null, `${timestamp}_${random}${ext}`);
  },
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "text/plain",
    "text/csv",
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`不支持的文件类型: ${file.mimetype}`), false);
  }
};

// 基础上传配置
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 5, // 最多5个文件
  },
});

// 头像上传配置（更严格）
const avatarUpload = multer({
  storage: multer.diskStorage({
    destination: function (req, file, cb) {
      const uploadPath = path.join(__dirname, "../uploads/avatars");
      ensureUploadDir(uploadPath);
      cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
      const timestamp = Date.now();
      const ext = path.extname(file.originalname);
      cb(null, `avatar_${timestamp}${ext}`);
    },
  }),
  fileFilter: (req, file, cb) => {
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("头像只支持 JPG、PNG、WebP 格式"), false);
    }
  },
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB
    files: 1,
  },
});

// 错误处理中间件
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    let message = "文件上传失败";

    switch (error.code) {
      case "LIMIT_FILE_SIZE":
        message = "文件大小超出限制";
        break;
      case "LIMIT_FILE_COUNT":
        message = "文件数量超出限制";
        break;
      case "LIMIT_UNEXPECTED_FILE":
        message = "意外的文件字段";
        break;
      default:
        message = error.message;
    }

    return res.status(400).json({
      code: 400,
      success: false,
      message,
    });
  }

  if (error) {
    return res.status(400).json({
      code: 400,
      success: false,
      message: error.message,
    });
  }

  next();
};

// 文件信息处理中间件
const processFileInfo = (req, res, next) => {
  if (req.file) {
    req.fileInfo = {
      filename: req.file.filename,
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: req.file.path,
      url: `/uploads/${path.basename(path.dirname(req.file.path))}/${
        req.file.filename
      }`,
    };
  }

  if (req.files) {
    req.filesInfo = req.files.map((file) => ({
      filename: file.filename,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path,
      url: `/uploads/${path.basename(path.dirname(file.path))}/${
        file.filename
      }`,
    }));
  }

  next();
};

module.exports = {
  upload,
  avatarUpload,
  handleUploadError,
  processFileInfo,
};
