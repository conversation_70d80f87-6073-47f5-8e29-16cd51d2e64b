const cron = require("node-cron");
const fs = require("fs");
const path = require("path");
const winston = require("winston");

class CronService {
  constructor() {
    this.tasks = new Map();

    // 确保日志目录存在
    const logsDir = path.join(__dirname, "../logs");
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    this.logger = winston.createLogger({
      level: "info",
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.File({
          filename: path.join(__dirname, "../logs/cron.log"),
        }),
        new winston.transports.Console({
          format: winston.format.simple(),
        }),
      ],
    });
  }

  // 添加定时任务
  addTask(name, schedule, task, options = {}) {
    try {
      if (this.tasks.has(name)) {
        this.logger.warn(`定时任务 ${name} 已存在，将被覆盖`);
        this.removeTask(name);
      }

      const cronTask = cron.schedule(
        schedule,
        async () => {
          const startTime = Date.now();
          this.logger.info(`定时任务 ${name} 开始执行`);

          try {
            await task();
            const duration = Date.now() - startTime;
            this.logger.info(`定时任务 ${name} 执行成功，耗时 ${duration}ms`);
          } catch (error) {
            this.logger.error(`定时任务 ${name} 执行失败:`, error);
          }
        },
        {
          scheduled: false,
          timezone: options.timezone || "Asia/Shanghai",
        }
      );

      this.tasks.set(name, {
        task: cronTask,
        schedule,
        options,
        createdAt: new Date(),
      });

      if (options.immediate !== false) {
        cronTask.start();
      }

      this.logger.info(`定时任务 ${name} 已添加，计划: ${schedule}`);
      return true;
    } catch (error) {
      this.logger.error(`添加定时任务 ${name} 失败:`, error);
      return false;
    }
  }

  // 移除定时任务
  removeTask(name) {
    if (this.tasks.has(name)) {
      const taskInfo = this.tasks.get(name);
      taskInfo.task.stop();
      taskInfo.task.destroy();
      this.tasks.delete(name);
      this.logger.info(`定时任务 ${name} 已移除`);
      return true;
    }
    return false;
  }

  // 启动定时任务
  startTask(name) {
    if (this.tasks.has(name)) {
      this.tasks.get(name).task.start();
      this.logger.info(`定时任务 ${name} 已启动`);
      return true;
    }
    return false;
  }

  // 停止定时任务
  stopTask(name) {
    if (this.tasks.has(name)) {
      this.tasks.get(name).task.stop();
      this.logger.info(`定时任务 ${name} 已停止`);
      return true;
    }
    return false;
  }

  // 获取所有任务信息
  getAllTasks() {
    const tasks = [];
    for (const [name, info] of this.tasks) {
      tasks.push({
        name,
        schedule: info.schedule,
        status: info.task.getStatus(),
        createdAt: info.createdAt,
        options: info.options,
      });
    }
    return tasks;
  }

  // 获取单个任务信息
  getTask(name) {
    if (this.tasks.has(name)) {
      const info = this.tasks.get(name);
      return {
        name,
        schedule: info.schedule,
        status: info.task.getStatus(),
        createdAt: info.createdAt,
        options: info.options,
      };
    }
    return null;
  }

  // 初始化默认任务
  initDefaultTasks() {
    try {
      // 每天凌晨2点清理过期日志
      this.addTask("cleanLogs", "0 2 * * *", async () => {
        await this.cleanOldLogs();
      });

      // 每小时检查系统健康状态
      this.addTask("healthCheck", "0 * * * *", async () => {
        await this.systemHealthCheck();
      });

      // 每天凌晨3点备份数据库（如果配置了）
      if (process.env.ENABLE_DB_BACKUP === "true") {
        this.addTask("dbBackup", "0 3 * * *", async () => {
          await this.backupDatabase();
        });
      }

      // 每周日凌晨4点清理临时文件
      this.addTask("cleanTempFiles", "0 4 * * 0", async () => {
        await this.cleanTempFiles();
      });
    } catch (error) {
      this.logger.error("初始化默认任务失败:", error);
    }
  }

  // 清理过期日志
  async cleanOldLogs() {
    try {
      const logsDir = path.join(__dirname, "../logs");
      if (!fs.existsSync(logsDir)) return;

      const files = fs.readdirSync(logsDir);
      const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
      const now = Date.now();

      let cleanedCount = 0;
      for (const file of files) {
        const filePath = path.join(logsDir, file);
        const stats = fs.statSync(filePath);

        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath);
          cleanedCount++;
        }
      }

      this.logger.info(`清理过期日志完成，删除 ${cleanedCount} 个文件`);
    } catch (error) {
      this.logger.error("清理过期日志失败:", error);
    }
  }

  // 系统健康检查
  async systemHealthCheck() {
    try {
      const mongoose = require("mongoose");
      const os = require("os");

      // 检查数据库连接
      if (mongoose.connection.readyState !== 1) {
        this.logger.warn("数据库连接异常");
      }

      // 检查内存使用率
      const memoryUsage =
        ((os.totalmem() - os.freemem()) / os.totalmem()) * 100;
      if (memoryUsage > 90) {
        this.logger.warn(`内存使用率过高: ${memoryUsage.toFixed(2)}%`);
      }

      // 检查磁盘空间（简单检查）
      const stats = fs.statSync(process.cwd());

      this.logger.info("系统健康检查完成");
    } catch (error) {
      this.logger.error("系统健康检查失败:", error);
    }
  }

  // 备份数据库
  async backupDatabase() {
    try {
      // 这里可以实现数据库备份逻辑
      // 例如使用 mongodump 命令
      this.logger.info("数据库备份功能待实现");
    } catch (error) {
      this.logger.error("数据库备份失败:", error);
    }
  }

  // 清理临时文件
  async cleanTempFiles() {
    try {
      const tempDirs = [
        path.join(__dirname, "../uploads/temp"),
        path.join(__dirname, "../temp"),
      ];

      let cleanedCount = 0;
      for (const dir of tempDirs) {
        if (fs.existsSync(dir)) {
          const files = fs.readdirSync(dir);
          for (const file of files) {
            const filePath = path.join(dir, file);
            fs.unlinkSync(filePath);
            cleanedCount++;
          }
        }
      }

      this.logger.info(`清理临时文件完成，删除 ${cleanedCount} 个文件`);
    } catch (error) {
      this.logger.error("清理临时文件失败:", error);
    }
  }
}

// 创建单例实例
const cronService = new CronService();

module.exports = cronService;
