<template>
  <div class="error-page">
    <div class="error-content">
      <!-- 错误图标 -->
      <div class="error-icon">
        <el-icon :size="120">
          <component :is="errorIcon" />
        </el-icon>
      </div>
      
      <!-- 错误码 -->
      <div class="error-code">{{ errorCode }}</div>
      
      <!-- 错误描述 -->
      <div class="error-description">{{ errorDescription }}</div>
      
      <!-- 错误详情 -->
      <div v-if="errorDetail" class="error-detail">
        <el-collapse>
          <el-collapse-item title="错误详情" name="detail">
            <pre>{{ errorDetail }}</pre>
          </el-collapse-item>
        </el-collapse>
      </div>
      
      <!-- 操作按钮 -->
      <div class="error-actions">
        <slot name="actions">
          <el-button @click="goBack">返回上页</el-button>
          <el-button type="primary" @click="goHome">回到首页</el-button>
          <el-button v-if="showRefresh" @click="refresh">刷新页面</el-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { Warning, CircleClose, Lock, QuestionFilled } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  // 错误码
  errorCode: {
    type: [String, Number],
    default: '404',
  },
  // 错误描述
  errorDescription: {
    type: String,
    default: '',
  },
  // 错误详情
  errorDetail: {
    type: String,
    default: '',
  },
  // 是否显示刷新按钮
  showRefresh: {
    type: Boolean,
    default: false,
  },
});

// Router
const router = useRouter();

// 错误图标映射
const errorIconMap = {
  '400': Warning,
  '401': Lock,
  '403': Lock,
  '404': QuestionFilled,
  '500': CircleClose,
};

// 错误描述映射
const errorDescriptionMap = {
  '400': '请求参数错误',
  '401': '您还没有登录或登录已过期',
  '403': '您没有权限访问此页面',
  '404': '抱歉，您访问的页面不存在',
  '500': '服务器内部错误',
};

// 计算错误图标
const errorIcon = computed(() => {
  return errorIconMap[props.errorCode] || QuestionFilled;
});

// 计算错误描述
const computedErrorDescription = computed(() => {
  return props.errorDescription || errorDescriptionMap[props.errorCode] || '发生了未知错误';
});

// 返回上页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    router.push('/');
  }
};

// 回到首页
const goHome = () => {
  router.push('/');
};

// 刷新页面
const refresh = () => {
  window.location.reload();
};
</script>

<style lang="scss" scoped>
.error-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.error-content {
  background: white;
  border-radius: 12px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
}

.error-icon {
  margin-bottom: 30px;
  color: #f56c6c;
}

.error-code {
  font-size: 72px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20px;
  line-height: 1;
}

.error-description {
  font-size: 18px;
  color: #606266;
  margin-bottom: 30px;
  line-height: 1.5;
}

.error-detail {
  margin-bottom: 30px;
  text-align: left;
  
  pre {
    background: #f5f7fa;
    padding: 15px;
    border-radius: 6px;
    font-size: 12px;
    color: #606266;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
  }
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .error-content {
    padding: 40px 20px;
  }
  
  .error-code {
    font-size: 48px;
  }
  
  .error-description {
    font-size: 16px;
  }
  
  .error-actions {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
