<template>
    <div class="app-container">
        <el-card class="box-card">
            <template #header>
                <div class="card-header">
                    <div class="left">
                        <el-button type="primary" @click="handleAdd" v-permission="'system:role:create'">
                            新增角色
                        </el-button>
                        <el-button type="danger" @click="handleBatchDelete" :disabled="selectedIds.length === 0"
                            v-permission="'system:role:delete'">
                            批量删除
                        </el-button>
                    </div>
                    <el-form :inline="true" :model="queryParams" class="right">
                        <el-form-item>
                            <el-input v-model="queryParams.name" placeholder="角色名称" clearable
                                @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item>
                            <el-input v-model="queryParams.code" placeholder="角色编码" clearable
                                @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item>
                            <el-select v-model="queryParams.status" placeholder="角色状态" clearable>
                                <el-option label="正常" :value="1" />
                                <el-option label="禁用" :value="0" />
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleQuery">查询</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 角色表格 -->
            <el-table v-loading="loading" :data="roleList" row-key="_id" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="角色名称" prop="name" />
                <el-table-column label="权限字符" prop="code" />
                <el-table-column label="显示顺序" prop="sort" width="100" align="center" />
                <el-table-column label="状态" align="center" width="100">
                    <template #default="{ row }">
                        <el-switch :model-value="row.status" :active-value="1" :inactive-value="0"
                            @change="(val) => handleStatusChange(row, val)" v-permission="'system:role:update'" />
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                    <template #default="{ row }">
                        {{ formatDate(row.createTime) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="250">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="handleEdit(row)" v-permission="'system:role:update'">
                            修改
                        </el-button>
                        <el-button type="primary" link @click="handleDelete(row)" v-permission="'system:role:delete'">
                            删除
                        </el-button>
                        <el-button type="primary" link @click="handlePermission(row)"
                            v-permission="'system:role:update'">
                            分配权限
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination">
                <el-pagination :current-page="queryParams.page" :page-size="queryParams.limit"
                    :page-sizes="[10, 20, 30, 50]" :background="true" layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </div>
        </el-card>

        <!-- 角色表单对话框 -->
        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px" @close="handleDialogClose">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="角色名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入角色名称" />
                </el-form-item>
                <el-form-item label="权限字符" prop="code">
                    <el-input v-model="form.code" placeholder="请输入权限字符" />
                </el-form-item>
                <el-form-item label="显示顺序" prop="sort">
                    <el-input-number v-model="form.sort" :min="0" :max="999" />
                </el-form-item>
                <el-form-item label="状态">
                    <el-radio-group v-model="form.status">
                        <el-radio :label="1">正常</el-radio>
                        <el-radio :label="0">停用</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="handleSubmit">确 定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 分配权限对话框 -->
        <el-dialog title="分配权限" v-model="permissionDialogVisible" width="400px" @close="handlePermissionDialogClose">
            <el-tree ref="permissionTreeRef" :data="permissionTree" :props="permissionProps" show-checkbox node-key="id"
                :default-checked-keys="checkedPermissions" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="permissionDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="handlePermissionSubmit">确 定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
    getRoleList,
    createRole,
    updateRole,
    deleteRole,
    batchDeleteRoles,
    updateRoleStatus,
    updateRolePermissions
} from '@/api/role'
import { getPermissionTree } from '@/api/permission'
import usePermission from '@/hooks/usePermission'
import { formatDate } from '@/utils/format'

const { permission } = usePermission()

// 查询参数
const queryParams = ref({
    page: 1,
    limit: 10,
    name: '',
    status: undefined
})

// 数据列表
const loading = ref(false)
const roleList = ref([])
const total = ref(0)
const selectedIds = ref([])

// 表单相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref(null)
const form = ref({
    name: '',
    code: '',
    sort: 0,
    status: 1,
    remark: ''
})

// 表单校验规则
const rules = {
    name: [
        { required: true, message: '请输入角色名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    code: [
        { required: true, message: '请输入权限字符', trigger: 'blur' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
    ],
    sort: [
        { type: 'number', message: '请输入数字', trigger: 'blur' }
    ],
    remark: [
        { max: 500, message: '长度不能超过500个字符', trigger: 'blur' }
    ]
}

// 权限树相关
const permissionDialogVisible = ref(false)
const permissionTreeRef = ref(null)
const permissionTree = ref([])
const checkedPermissions = ref([])
const currentRoleId = ref(null)
const permissionProps = {
    children: 'children',
    label: 'name'
}

// 获取角色列表
const getList = async () => {
    loading.value = true
    try {
        const res = await getRoleList(queryParams.value)
        roleList.value = res.data.list
        console.log(roleList.value)
        total.value = res.data.total
    } catch (error) {
        console.error('获取角色列表失败:', error)
    } finally {
        loading.value = false
    }
}

// 查询操作
const handleQuery = () => {
    queryParams.value.page = 1
    getList()
}

// 重置查询
const resetQuery = () => {
    queryParams.value = {
        page: 1,
        limit: 10,
        name: '',
        status: undefined
    }
    getList()
}

// 选择项变化
const handleSelectionChange = (selection) => {
    selectedIds.value = selection.map(item => item._id)
}

// 新增角色
const handleAdd = () => {
    resetForm()
    dialogTitle.value = '新增角色'
    dialogVisible.value = true
}

// 编辑角色
const handleEdit = (row) => {
    resetForm()
    dialogTitle.value = '编辑角色'
    Object.assign(form.value, row)
    dialogVisible.value = true
}

// 删除角色
const handleDelete = async (row) => {
    try {
        await ElMessageBox.confirm('确认要删除该角色吗？', '警告', {
            type: 'warning'
        })
        await deleteRole(row._id)
        ElMessage.success('删除成功')
        getList()
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除角色失败:', error)
        }
    }
}

// 批量删除
const handleBatchDelete = async () => {
    try {
        await ElMessageBox.confirm(
            `确认要删除选中的 ${selectedIds.value.length} 个角色吗？`,
            '警告',
            {
                type: 'warning'
            }
        )
        await batchDeleteRoles(selectedIds.value)
        ElMessage.success('批量删除成功')
        getList()
    } catch (error) {
        if (error !== 'cancel') {
            console.error('批量删除失败:', error)
        }
    }
}

// 状态变更
const handleStatusChange = async (row, status) => {
    try {
        await updateRoleStatus(row._id, status)
        ElMessage.success(`${status === 1 ? '启用' : '停用'}成功`)
        // 更新本地数据
        row.status = status
    } catch (error) {
        console.error('更新状态失败:', error)
        ElMessage.error('更新状态失败')
    }
}

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return
    try {
        await formRef.value.validate()
        if (form.value._id) {
            await updateRole(form.value._id, form.value)
            ElMessage.success('更新成功')
        } else {
            await createRole(form.value)
            ElMessage.success('创建成功')
        }
        dialogVisible.value = false
        getList()
    } catch (error) {
        console.error('提交表单失败:', error)
    }
}

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields()
    }
    form.value = {
        name: '',
        code: '',
        sort: 0,
        status: 1,
        remark: ''
    }
}

// 分配权限
const handlePermission = async (row) => {
    currentRoleId.value = row._id
    try {
        // 获取权限树
        const res = await getPermissionTree()
        permissionTree.value = res.data
        // 设置已选权限
        checkedPermissions.value = row.permissions || []
        permissionDialogVisible.value = true
    } catch (error) {
        console.error('获取权限树失败:', error)
    }
}

// 提交权限分配
const handlePermissionSubmit = async () => {
    if (!permissionTreeRef.value || !currentRoleId.value) return
    try {
        const { checkedKeys, halfCheckedKeys } = permissionTreeRef.value.getCheckedNodes()
        const permissions = [...checkedKeys, ...halfCheckedKeys]
        await updateRolePermissions(currentRoleId.value, permissions)
        ElMessage.success('权限分配成功')
        permissionDialogVisible.value = false
        getList()
    } catch (error) {
        console.error('分配权限失败:', error)
    }
}

// 关闭对话框
const handleDialogClose = () => {
    resetForm()
}

const handlePermissionDialogClose = () => {
    currentRoleId.value = null
    checkedPermissions.value = []
}

// 分页大小变化
const handleSizeChange = (val) => {
    queryParams.value.limit = val
    getList()
}

// 页码变化
const handleCurrentChange = (val) => {
    queryParams.value.page = val
    getList()
}

// 页面加载时获取数据
onMounted(() => {
    getList()
})
</script>

<style lang="scss" scoped>
.app-container {
    padding: 20px;
}

.box-card {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .right {
        display: flex;
        align-items: center;
    }
}

.pagination {
    margin-top: 20px;
    text-align: right;
}
</style>