const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");

// 加载环境变量
dotenv.config();

// 创建Express应用
const app = express();

// 基础中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// 测试路由
app.get("/", (req, res) => {
  res.json({
    success: true,
    message: "服务器运行正常",
    timestamp: new Date().toISOString(),
  });
});

app.get("/api/v1/test", (req, res) => {
  res.json({
    success: true,
    message: "API测试成功",
    data: {
      time: new Date().toISOString(),
    },
  });
});

// 错误处理
app.use((error, req, res, next) => {
  console.error("服务器错误:", error);
  res.status(500).json({
    success: false,
    message: "服务器内部错误",
  });
});

// 启动服务器
const PORT = process.env.PORT || 3000;

const server = app.listen(PORT, () => {
  console.log(`测试服务器运行在端口 ${PORT}`);
  console.log(`访问地址: http://localhost:${PORT}`);
});

// 优雅关闭
process.on("SIGTERM", () => {
  console.log("收到SIGTERM信号，正在关闭服务器...");
  server.close(() => {
    console.log("服务器已关闭");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  console.log("收到SIGINT信号，正在关闭服务器...");
  server.close(() => {
    console.log("服务器已关闭");
    process.exit(0);
  });
});

module.exports = app;
