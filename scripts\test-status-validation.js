#!/usr/bin/env node

/**
 * 状态验证测试脚本
 * 测试不同类型的状态值转换
 */

// 模拟前端的状态转换逻辑
function testStatusConversion(status) {
  console.log(`\n测试输入: ${JSON.stringify(status)} (类型: ${typeof status})`);
  
  // 处理可能的复杂数据结构
  let actualStatus = status;
  
  // 如果status是对象，尝试提取实际值
  if (typeof status === 'object' && status !== null) {
    console.log("检测到对象类型的status:", status);
    if (status.hasOwnProperty('status')) {
      actualStatus = status.status;
      console.log("从对象中提取status:", actualStatus);
    } else if (status.hasOwnProperty('value')) {
      actualStatus = status.value;
      console.log("从对象中提取value:", actualStatus);
    }
  }

  // 确保status是数字类型，防止数据格式错误
  let numericStatus;
  
  if (typeof actualStatus === "number") {
    numericStatus = actualStatus;
  } else if (typeof actualStatus === "string") {
    numericStatus = actualStatus === "true" ? 1 : actualStatus === "false" ? 0 : Number(actualStatus);
  } else if (typeof actualStatus === "boolean") {
    numericStatus = actualStatus ? 1 : 0;
  } else {
    numericStatus = Number(actualStatus);
  }

  console.log("初步转换结果:", {
    actualStatus,
    numericStatus,
    numericType: typeof numericStatus,
    isNaN: isNaN(numericStatus),
  });

  // 验证状态值 - 使用新的宽松验证
  if (isNaN(numericStatus)) {
    console.log("❌ 验证失败 - 不是数字");
    return null;
  }
  
  // 强制转换为0或1
  numericStatus = numericStatus ? 1 : 0;
  
  console.log("✅ 最终状态值:", numericStatus);
  return numericStatus;
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始状态值转换测试...\n');
  
  const testCases = [
    // 正常情况
    1,
    0,
    "1",
    "0",
    true,
    false,
    
    // 边界情况
    "true",
    "false",
    2,
    -1,
    "2",
    "-1",
    
    // 异常情况
    null,
    undefined,
    "",
    "abc",
    [],
    {},
    
    // 嵌套对象（之前遇到的问题）
    { status: 1 },
    { status: "1" },
    { status: { status: 1 } },
    { value: 1 },
    
    // 其他可能的情况
    NaN,
    Infinity,
    -Infinity,
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`\n=== 测试用例 ${index + 1} ===`);
    const result = testStatusConversion(testCase);
    console.log(`结果: ${result}`);
  });
  
  console.log('\n🎉 测试完成！');
  
  console.log('\n📋 总结:');
  console.log('- 数字 1, 0 → 1, 0');
  console.log('- 字符串 "1", "0" → 1, 0');
  console.log('- 布尔值 true, false → 1, 0');
  console.log('- 其他真值 → 1');
  console.log('- 其他假值 → 0');
  console.log('- 无效值 (NaN) → 验证失败');
}

// 运行测试
if (require.main === module) {
  main();
}
