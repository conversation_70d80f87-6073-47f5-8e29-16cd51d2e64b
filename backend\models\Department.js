const mongoose = require("mongoose");

const DepartmentSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
    },
    parentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Department",
      default: null,
    },
    ancestors: {
      type: String,
      default: "",
    },
    leader: {
      type: String,
      trim: true,
      maxlength: 50,
    },
    phone: {
      type: String,
      trim: true,
      maxlength: 20,
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        "请提供有效的邮箱地址",
      ],
    },
    status: {
      type: Number,
      enum: [0, 1], // 0: 禁用, 1: 正常
      default: 1,
    },
    sort: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: {
      createdAt: "createTime",
      updatedAt: "updateTime",
    },
  }
);

const Department = mongoose.model("Department", DepartmentSchema);

module.exports = Department;
