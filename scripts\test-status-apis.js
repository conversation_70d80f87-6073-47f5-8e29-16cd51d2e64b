#!/usr/bin/env node

/**
 * 状态更新API测试脚本
 * 测试用户、角色、权限的状态更新功能
 */

const http = require('http');

// 测试配置
const API_BASE = 'http://localhost:3000/api/v1';

/**
 * 发送HTTP请求
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsed
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * 获取认证token
 */
async function getAuthToken() {
  console.log('🔐 获取认证token...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/v1/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  try {
    const result = await makeRequest(options, {
      username: 'admin',
      password: '123456'
    });
    
    if (result.statusCode === 200 && result.data.success) {
      console.log('✅ 登录成功');
      return result.data.data.token;
    } else {
      console.log('❌ 登录失败');
      console.log(`   响应: ${JSON.stringify(result.data, null, 2)}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ 登录请求失败: ${error.message}`);
    return null;
  }
}

/**
 * 测试用户状态更新
 */
async function testUserStatusUpdate(token, userId) {
  console.log(`🧪 测试用户状态更新 (ID: ${userId})...`);
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: `/api/v1/users/${userId}/status`,
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  };

  try {
    const result = await makeRequest(options, { status: 1 });
    
    if (result.statusCode === 200) {
      console.log('✅ 用户状态更新成功');
    } else {
      console.log('❌ 用户状态更新失败');
      console.log(`   状态码: ${result.statusCode}`);
      console.log(`   响应: ${JSON.stringify(result.data, null, 2)}`);
    }
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
  }
}

/**
 * 测试角色状态更新
 */
async function testRoleStatusUpdate(token, roleId) {
  console.log(`🧪 测试角色状态更新 (ID: ${roleId})...`);
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: `/api/v1/roles/${roleId}/status`,
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  };

  try {
    const result = await makeRequest(options, { status: 1 });
    
    if (result.statusCode === 200) {
      console.log('✅ 角色状态更新成功');
    } else {
      console.log('❌ 角色状态更新失败');
      console.log(`   状态码: ${result.statusCode}`);
      console.log(`   响应: ${JSON.stringify(result.data, null, 2)}`);
    }
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
  }
}

/**
 * 测试权限状态更新
 */
async function testPermissionStatusUpdate(token, permissionId) {
  console.log(`🧪 测试权限状态更新 (ID: ${permissionId})...`);
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: `/api/v1/permissions/${permissionId}/status`,
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  };

  try {
    const result = await makeRequest(options, { status: 1 });
    
    if (result.statusCode === 200) {
      console.log('✅ 权限状态更新成功');
    } else {
      console.log('❌ 权限状态更新失败');
      console.log(`   状态码: ${result.statusCode}`);
      console.log(`   响应: ${JSON.stringify(result.data, null, 2)}`);
    }
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
  }
}

/**
 * 获取测试数据ID
 */
async function getTestIds(token) {
  console.log('📋 获取测试数据ID...');
  
  const testIds = {};
  
  // 获取用户ID
  try {
    const userResult = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/users?page=1&limit=5',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (userResult.statusCode === 200 && userResult.data.data.length > 0) {
      testIds.userId = userResult.data.data[0]._id;
      console.log(`   用户ID: ${testIds.userId}`);
    }
  } catch (error) {
    console.log(`   获取用户ID失败: ${error.message}`);
  }
  
  // 获取角色ID
  try {
    const roleResult = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/roles?page=1&limit=5',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (roleResult.statusCode === 200 && roleResult.data.data.length > 0) {
      testIds.roleId = roleResult.data.data[0]._id;
      console.log(`   角色ID: ${testIds.roleId}`);
    }
  } catch (error) {
    console.log(`   获取角色ID失败: ${error.message}`);
  }
  
  // 获取权限ID
  try {
    const permResult = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/permissions/tree',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (permResult.statusCode === 200 && permResult.data.data.length > 0) {
      testIds.permissionId = permResult.data.data[0]._id;
      console.log(`   权限ID: ${testIds.permissionId}`);
    }
  } catch (error) {
    console.log(`   获取权限ID失败: ${error.message}`);
  }
  
  return testIds;
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始状态更新API测试...\n');
  
  // 测试后端连接
  try {
    const result = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/test',
      method: 'GET'
    });
    
    if (result.statusCode !== 200) {
      console.log('❌ 后端服务不可用，请确保后端服务已启动');
      return;
    }
  } catch (error) {
    console.log('❌ 无法连接到后端服务，请确保后端服务已启动在 http://localhost:3000');
    return;
  }
  
  // 获取认证token
  const token = await getAuthToken();
  if (!token) {
    console.log('❌ 无法获取认证token，测试终止');
    return;
  }
  
  // 获取测试数据ID
  const testIds = await getTestIds(token);
  
  console.log('\n🧪 开始状态更新测试...');
  
  // 测试用户状态更新
  if (testIds.userId) {
    await testUserStatusUpdate(token, testIds.userId);
  } else {
    console.log('⚠️  跳过用户状态更新测试（未找到用户数据）');
  }
  
  // 测试角色状态更新
  if (testIds.roleId) {
    await testRoleStatusUpdate(token, testIds.roleId);
  } else {
    console.log('⚠️  跳过角色状态更新测试（未找到角色数据）');
  }
  
  // 测试权限状态更新
  if (testIds.permissionId) {
    await testPermissionStatusUpdate(token, testIds.permissionId);
  } else {
    console.log('⚠️  跳过权限状态更新测试（未找到权限数据）');
  }
  
  console.log('\n🎉 状态更新API测试完成！');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}
