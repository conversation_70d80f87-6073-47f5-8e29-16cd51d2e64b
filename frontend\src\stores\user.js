import { defineStore } from "pinia";
import { login, logout, getInfo } from "@/api/auth";
import { getToken, setToken, removeToken } from "@/utils/auth";
import { usePermissionStore } from "@/stores/permission";
// 移除对路由的直接导入，避免循环依赖
// import { constantRoutes, dynamicRoutes } from "@/router";

export const useUserStore = defineStore("user", {
  state: () => ({
    token: getToken(),
    userInfo: null,
    roles: [],
    permissions: [],
  }),

  getters: {
    isLogin: (state) => !!state.token,
    userId: (state) => state.userInfo?._id,
    username: (state) => state.userInfo?.username,
    nickname: (state) => state.userInfo?.nickname,
    avatar: (state) => state.userInfo?.avatar || "",
    hasRole: (state) => (role) => state.roles.includes(role),
  },

  actions: {
    // 登录
    async loginAction(loginData) {
      try {
        const { data } = await login(loginData);
        const { token } = data;
        setToken(token);
        this.token = token;
        return data;
      } catch (error) {
        throw error;
      }
    },

    // 获取用户信息 (原有的 getUserInfoAction 函数)
    async getUserInfoAction() {
      try {
        const { data } = await getInfo();
        // 如果是测试用户，直接使用返回的数据
        if (data._id === "test-user-id") {
          this.userInfo = data;
          this.roles = data.roles.map((role) => role.code);
          this.permissions = data.permissions;
        } else {
          // 正常用户处理
          const { user, roles, permissions } = data;
          this.userInfo = user;
          this.roles = roles || [];
          this.permissions = permissions || [];
        }
        return data;
      } catch (error) {
        throw error;
      }
    },

    // 获取用户信息 (新增的 getUserInfo 函数，用于路由守卫)
    async getUserInfo() {
      try {
        return await this.getUserInfoAction();
      } catch (error) {
        throw error;
      }
    },

    // 生成路由 (使用permissionStore)
    async generateRoutes() {
      const permissionStore = usePermissionStore();
      return permissionStore.generateRoutes(this.roles);
    },

    // 登出
    async logoutAction() {
      try {
        await logout();
        this.resetState();
        // 重置权限路由
        const permissionStore = usePermissionStore();
        permissionStore.resetRoutes();
        return true;
      } catch (error) {
        throw error;
      }
    },

    // 重置状态
    resetState() {
      this.token = "";
      this.userInfo = null;
      this.roles = [];
      this.permissions = [];
      removeToken();
    },

    // 重置令牌 (新增的 resetToken 函数，用于路由守卫)
    async resetToken() {
      return new Promise((resolve) => {
        this.resetState();
        // 重置权限路由
        const permissionStore = usePermissionStore();
        permissionStore.resetRoutes();
        resolve();
      });
    },
  },
});

/**
 * 根据角色和权限过滤路由
 * @param {Array} routes 路由表
 * @param {Array} roles 角色
 * @param {Array} permissions 权限
 * @returns {Array}
 */
function filterAsyncRoutes(routes, roles, permissions) {
  const res = [];

  routes.forEach((route) => {
    const tmp = { ...route };

    // 检查是否有权限访问该路由
    if (hasPermission(roles, permissions, tmp)) {
      // 如果有子路由，递归过滤
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles, permissions);
      }
      res.push(tmp);
    }
  });

  return res;
}

/**
 * 检查是否有权限
 * @param {Array} roles 角色
 * @param {Array} permissions 权限
 * @param {Object} route 路由
 * @returns {boolean}
 */
function hasPermission(roles, permissions, route) {
  // 如果路由没有设置 meta 或没有设置 roles，视为无需权限
  if (!route.meta || !route.meta.roles) {
    return true;
  }

  // 检查角色权限
  if (route.meta.roles) {
    return roles.some((role) => route.meta.roles.includes(role));
  }

  // 默认无权限
  return false;
}
