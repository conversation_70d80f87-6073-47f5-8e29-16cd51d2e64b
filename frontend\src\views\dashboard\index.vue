<template>
    <div class="dashboard-container">
        <h1>仪表盘</h1>
        <p>欢迎使用管理系统</p>

        <div class="debug-section">
            <h3>路由调试</h3>
            <el-button type="primary" @click="navigateToUser">用户管理</el-button>
            <el-button type="success" @click="navigateToRole">角色管理</el-button>
            <el-button type="warning" @click="navigateToPermission">权限管理</el-button>
            <el-button type="danger" @click="navigateToDepartment">部门管理</el-button>

            <div class="route-info" v-if="currentRoute">
                <h4>当前路由信息：</h4>
                <pre>{{ currentRoute }}</pre>
            </div>
        </div>

        <el-row :gutter="20">
            <el-col :span="6">
                <el-card shadow="hover" class="dashboard-card">
                    <template #header>
                        <div class="card-header">
                            <span>用户总数</span>
                        </div>
                    </template>
                    <div class="card-body">
                        <div class="card-value">{{ stats.userCount }}</div>
                        <div class="card-icon">
                            <el-icon>
                                <User />
                            </el-icon>
                        </div>
                    </div>
                </el-card>
            </el-col>

            <el-col :span="6">
                <el-card shadow="hover" class="dashboard-card">
                    <template #header>
                        <div class="card-header">
                            <span>角色总数</span>
                        </div>
                    </template>
                    <div class="card-body">
                        <div class="card-value">{{ stats.roleCount }}</div>
                        <div class="card-icon">
                            <el-icon>
                                <UserFilled />
                            </el-icon>
                        </div>
                    </div>
                </el-card>
            </el-col>

            <el-col :span="6">
                <el-card shadow="hover" class="dashboard-card">
                    <template #header>
                        <div class="card-header">
                            <span>权限总数</span>
                        </div>
                    </template>
                    <div class="card-body">
                        <div class="card-value">{{ stats.permissionCount }}</div>
                        <div class="card-icon">
                            <el-icon>
                                <Lock />
                            </el-icon>
                        </div>
                    </div>
                </el-card>
            </el-col>

            <el-col :span="6">
                <el-card shadow="hover" class="dashboard-card">
                    <template #header>
                        <div class="card-header">
                            <span>部门总数</span>
                        </div>
                    </template>
                    <div class="card-body">
                        <div class="card-value">{{ stats.departmentCount }}</div>
                        <div class="card-icon">
                            <el-icon>
                                <OfficeBuilding />
                            </el-icon>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
                <el-card shadow="hover">
                    <template #header>
                        <div class="card-header">
                            <span>系统信息</span>
                        </div>
                    </template>
                    <div class="system-info">
                        <el-descriptions :column="1" border>
                            <el-descriptions-item label="系统名称">后台管理系统</el-descriptions-item>
                            <el-descriptions-item label="系统版本">v1.0.0</el-descriptions-item>
                            <el-descriptions-item label="Node版本">v16.x</el-descriptions-item>
                            <el-descriptions-item label="Vue版本">v3.x</el-descriptions-item>
                            <el-descriptions-item label="Element Plus版本">v2.x</el-descriptions-item>
                            <el-descriptions-item label="数据库">MongoDB</el-descriptions-item>
                        </el-descriptions>
                    </div>
                </el-card>
            </el-col>

            <el-col :span="12">
                <el-card shadow="hover">
                    <template #header>
                        <div class="card-header">
                            <span>最近登录记录</span>
                        </div>
                    </template>
                    <el-table :data="loginLogs" style="width: 100%">
                        <el-table-column prop="username" label="用户名" width="120" />
                        <el-table-column prop="ip" label="IP地址" width="120" />
                        <el-table-column prop="time" label="登录时间" />
                        <el-table-column prop="status" label="状态">
                            <template #default="scope">
                                <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
                                    {{ scope.row.status }}
                                </el-tag>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
import { reactive, ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const currentRoute = ref(null);

// 模拟数据
const stats = reactive({
    userCount: 100,
    roleCount: 8,
    permissionCount: 50,
    departmentCount: 12
});

const loginLogs = [
    {
        username: 'admin',
        ip: '***********',
        time: '2023-01-01 12:00:00',
        status: '成功'
    },
    {
        username: 'user1',
        ip: '***********',
        time: '2023-01-01 11:30:00',
        status: '成功'
    },
    {
        username: 'user2',
        ip: '***********',
        time: '2023-01-01 11:00:00',
        status: '失败'
    },
    {
        username: 'user3',
        ip: '***********',
        time: '2023-01-01 10:30:00',
        status: '成功'
    }
];

// 显示当前路由信息
const showRouteInfo = () => {
    currentRoute.value = {
        path: route.path,
        fullPath: route.fullPath,
        name: route.name,
        meta: route.meta,
        params: route.params,
        query: route.query
    };
};

// 导航到用户管理
const navigateToUser = () => {
    router.push('/system/user').then(() => {
        console.log('导航到用户管理成功');
        showRouteInfo();
    }).catch(err => {
        console.error('导航到用户管理失败:', err);
    });
};

// 导航到角色管理
const navigateToRole = () => {
    router.push('/system/role').then(() => {
        console.log('导航到角色管理成功');
        showRouteInfo();
    }).catch(err => {
        console.error('导航到角色管理失败:', err);
    });
};

// 导航到权限管理
const navigateToPermission = () => {
    router.push('/system/permission').then(() => {
        console.log('导航到权限管理成功');
        showRouteInfo();
    }).catch(err => {
        console.error('导航到权限管理失败:', err);
    });
};

// 导航到部门管理
const navigateToDepartment = () => {
    router.push('/system/department').then(() => {
        console.log('导航到部门管理成功');
        showRouteInfo();
    }).catch(err => {
        console.error('导航到部门管理失败:', err);
    });
};
</script>

<style scoped>
.dashboard-container {
    padding: 20px;
}

.debug-section {
    margin-top: 30px;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 4px;
}

.route-info {
    margin-top: 20px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}

.el-button {
    margin-right: 10px;
    margin-bottom: 10px;
}

.dashboard-card {
    height: 180px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.card-value {
    font-size: 36px;
    font-weight: bold;
    color: #303133;
}

.card-icon {
    font-size: 48px;
    color: #409EFF;
    opacity: 0.6;
}

.system-info {
    margin-top: 10px;
}
</style>