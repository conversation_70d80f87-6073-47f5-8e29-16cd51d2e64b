<template>
    <div class="tags-view-container">
        <scroll-pane class="tags-view-wrapper" ref="scrollPane">
            <router-link v-for="tag in visitedViews" :key="tag.path" :class="isActive(tag) ? 'active' : ''"
                :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }" tag="span" class="tags-view-item"
                @click.middle="!isAffix(tag) && closeSelectedTag(tag)" @contextmenu.prevent="openMenu(tag, $event)">
                {{ tag.title }}
                <el-icon v-if="!isAffix(tag)" class="close-icon" @click.prevent.stop="closeSelectedTag(tag)">
                    <Close />
                </el-icon>
            </router-link>
        </scroll-pane>

        <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
            <li @click="refreshSelectedTag(selectedTag)">刷新页面</li>
            <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">关闭当前</li>
            <li @click="closeOthersTags">关闭其他</li>
            <li @click="closeAllTags(selectedTag)">关闭所有</li>
        </ul>
    </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTagsViewStore } from '@/stores/tagsView'
import { Close } from '@element-plus/icons-vue'
import ScrollPane from './ScrollPane.vue'

const route = useRoute()
const router = useRouter()
const tagsViewStore = useTagsViewStore()

// 访问过的视图
const visitedViews = computed(() => tagsViewStore.visitedViews)

// 右键菜单相关
const visible = ref(false)
const top = ref(0)
const left = ref(0)
const selectedTag = ref({})
const scrollPane = ref(null)
const affixTags = ref([])

// 初始化标签
onMounted(() => {
    initTags()
    addTags()
})

// 监听路由变化
watch(route, () => {
    addTags()
    moveToCurrentTag()
})

// 监听菜单可见性
watch(visible, (value) => {
    if (value) {
        document.body.addEventListener('click', closeMenu)
    } else {
        document.body.removeEventListener('click', closeMenu)
    }
})

onBeforeUnmount(() => {
    document.body.removeEventListener('click', closeMenu)
})

// 初始化标签，添加固定标签
const initTags = () => {
    const routes = router.getRoutes()
    const affixRoutes = routes.filter(route => route.meta?.affix)

    affixTags.value = affixRoutes.map(route => {
        return {
            path: route.path,
            name: route.name,
            title: route.meta.title,
            meta: { ...route.meta }
        }
    })

    // 添加固定标签
    for (const tag of affixTags.value) {
        if (tag.name) {
            tagsViewStore.addVisitedView(tag)
        }
    }
}

// 添加当前路由到标签
const addTags = () => {
    const { name } = route
    if (name) {
        tagsViewStore.addVisitedView(route)
        tagsViewStore.addCachedView(route)
    }
}

// 判断标签是否为当前激活标签
const isActive = (tag) => {
    return tag.path === route.path
}

// 判断标签是否为固定标签
const isAffix = (tag) => {
    return tag.meta && tag.meta.affix
}

// 关闭选中的标签
const closeSelectedTag = async (view) => {
    if (isActive(view)) {
        toLastView(visitedViews.value, view)
    }
    tagsViewStore.closeTag(view)
}

// 关闭其他标签
const closeOthersTags = () => {
    router.push(selectedTag.value)
    tagsViewStore.closeOtherTags(selectedTag.value)
    visible.value = false
}

// 关闭所有标签
const closeAllTags = (view) => {
    tagsViewStore.closeAllTags()
    if (affixTags.value.length > 0) {
        router.push(affixTags.value[0])
    } else {
        router.push('/')
    }
    visible.value = false
}

// 刷新选中的标签
const refreshSelectedTag = (view) => {
    tagsViewStore.delCachedView(view)
    const { fullPath } = view
    nextTick(() => {
        router.replace({
            path: '/redirect' + fullPath
        })
    })
    visible.value = false
}

// 打开右键菜单
const openMenu = (tag, e) => {
    const menuMinWidth = 105
    const offsetWidth = document.body.clientWidth
    const maxLeft = offsetWidth - menuMinWidth

    // 获取鼠标点击位置
    const clickX = e.clientX

    // 计算菜单显示位置，确保不超出屏幕右侧边界
    left.value = clickX > maxLeft ? maxLeft : clickX
    top.value = e.clientY + 5

    visible.value = true
    selectedTag.value = tag
}

// 关闭右键菜单
const closeMenu = () => {
    visible.value = false
}

// 移动到当前标签
const moveToCurrentTag = async () => {
    await nextTick()
    if (scrollPane.value) {
        for (const tag of visitedViews.value) {
            if (tag.path === route.path) {
                scrollPane.value.moveToTarget(tag)
                if (tag.path !== route.path) {
                    tagsViewStore.updateVisitedView(route)
                }
                break
            }
        }
    }
}

// 跳转到最后一个标签
const toLastView = (visitedViews, view) => {
    const latestView = visitedViews.slice(-1)[0]
    if (latestView && latestView.path !== view.path) {
        router.push(latestView)
    } else {
        // 如果关闭的是最后一个标签，则跳转到首页或第一个标签
        if (visitedViews.length > 1) {
            router.push(visitedViews[0])
        } else {
            router.push('/')
        }
    }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
    height: 34px;
    width: 100%;
    background: #fff;
    border-bottom: 1px solid #d8dce5;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);

    .tags-view-wrapper {
        .tags-view-item {
            display: inline-block;
            position: relative;
            cursor: pointer;
            height: 26px;
            line-height: 26px;
            border: 1px solid #d8dce5;
            color: #495060;
            background: #fff;
            padding: 0 8px;
            font-size: 12px;
            margin-left: 5px;
            margin-top: 4px;
            border-radius: 3px;

            &:first-of-type {
                margin-left: 15px;
            }

            &.active {
                background-color: var(--el-color-primary);
                color: #fff;
                border-color: var(--el-color-primary);

                &::before {
                    content: '';
                    background: #fff;
                    display: inline-block;
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    position: relative;
                    margin-right: 2px;
                }

                .close-icon {
                    color: #fff;
                }
            }

            .close-icon {
                width: 16px;
                height: 16px;
                vertical-align: -3px;
                margin-left: 2px;
                border-radius: 50%;
                text-align: center;
                transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
                transform-origin: 100% 50%;

                &:hover {
                    background-color: #b4bccc;
                    color: #fff;
                }
            }
        }
    }

    .contextmenu {
        margin: 0;
        background: #fff;
        z-index: 3000;
        position: absolute;
        list-style-type: none;
        padding: 5px 0;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 400;
        color: #333;
        box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);

        li {
            margin: 0;
            padding: 7px 16px;
            cursor: pointer;

            &:hover {
                background: #eee;
            }
        }
    }
}
</style>