import { useUserStore } from "@/stores/user";

/**
 * 权限验证 hook
 * @returns {Object} permission - 权限验证方法
 */
export default function usePermission() {
  const userStore = useUserStore();

  /**
   * 验证用户是否有指定权限
   * @param {string} permission - 权限标识
   * @returns {boolean} - 是否有权限
   */
  const hasPermission = (permission) => {
    // 获取用户权限列表
    const permissions = userStore.permissions || [];

    // 如果用户是超级管理员，直接返回 true
    if (permissions.includes("*:*:*")) {
      return true;
    }

    // 验证是否包含指定权限
    return permissions.includes(permission);
  };

  /**
   * 验证用户是否有指定角色
   * @param {string} role - 角色标识
   * @returns {boolean} - 是否有角色
   */
  const hasRole = (role) => {
    // 获取用户角色列表
    const roles = userStore.roles || [];

    // 如果用户是超级管理员，直接返回 true
    if (roles.includes("admin")) {
      return true;
    }

    // 验证是否包含指定角色
    return roles.includes(role);
  };

  return {
    hasPermission,
    hasRole,
  };
}
