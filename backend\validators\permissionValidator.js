const { body } = require("express-validator");
const Permission = require("../models/Permission");

/**
 * 权限创建验证规则
 */
const createPermissionValidator = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("权限名称不能为空")
    .isLength({ max: 50 })
    .withMessage("权限名称长度不能超过50个字符"),

  body("type")
    .notEmpty()
    .withMessage("权限类型不能为空")
    .isIn(['menu', 'button'])
    .withMessage("权限类型无效"),

  body("parentId").optional({ nullable: true }),

  body("path")
    .optional()
    .isLength({ max: 100 })
    .withMessage("路径长度不能超过100个字符"),

  body("component")
    .optional()
    .isLength({ max: 100 })
    .withMessage("组件长度不能超过100个字符"),

  body("code")
    .optional()
    .isLength({ max: 100 })
    .withMessage("权限标识长度不能超过100个字符")
    .custom(async (value, { req }) => {
      if (!value) return true;
      const permission = await Permission.findOne({ code: value });
      if (permission) {
        throw new Error("权限标识已存在");
      }
      return true;
    }),

  body("icon")
    .optional()
    .isLength({ max: 50 })
    .withMessage("图标长度不能超过50个字符"),

  body("sort").optional().isInt({ min: 0 }).withMessage("排序值必须是非负整数"),

  body("visible").optional().isBoolean().withMessage("可见性必须是布尔值"),

  body("status").optional().isIn([0, 1]).withMessage("状态值无效"),
];

/**
 * 权限更新验证规则
 */
const updatePermissionValidator = [
  body("name")
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage("权限名称长度不能超过50个字符"),

  body("type").optional().isIn([1, 2, 3]).withMessage("权限类型无效"),

  body("parentId").optional({ nullable: true }),

  body("path")
    .optional()
    .isLength({ max: 100 })
    .withMessage("路径长度不能超过100个字符"),

  body("component")
    .optional()
    .isLength({ max: 100 })
    .withMessage("组件长度不能超过100个字符"),

  body("code")
    .optional()
    .isLength({ max: 100 })
    .withMessage("权限标识长度不能超过100个字符")
    .custom(async (value, { req }) => {
      if (!value) return true;
      const permission = await Permission.findOne({
        code: value,
        _id: { $ne: req.params.id },
      });
      if (permission) {
        throw new Error("权限标识已存在");
      }
      return true;
    }),

  body("icon")
    .optional()
    .isLength({ max: 50 })
    .withMessage("图标长度不能超过50个字符"),

  body("sort").optional().isInt({ min: 0 }).withMessage("排序值必须是非负整数"),

  body("visible").optional().isBoolean().withMessage("可见性必须是布尔值"),

  body("status").optional().isIn([0, 1]).withMessage("状态值无效"),
];

/**
 * 权限状态验证规则
 */
const permissionStatusValidator = [
  body("status").isIn([0, 1]).withMessage("状态值必须为0或1"),
];

module.exports = {
  createPermissionValidator,
  updatePermissionValidator,
  permissionStatusValidator,
};
