import { defineStore } from "pinia";
import { ref, computed } from "vue";

export const useAppStore = defineStore("app", () => {
  // 从本地存储获取侧边栏状态，默认为打开
  const sidebarStatus = localStorage.getItem("sidebarStatus");

  // 侧边栏状态
  const sidebar = ref({
    opened: sidebarStatus !== "closed",
    withoutAnimation: false,
  });

  // 设备类型（桌面/移动）
  const device = ref("desktop");

  // 应用设置
  const settings = ref({
    // 是否显示设置面板
    showSettings: false,
    // 是否显示标签视图
    tagsView: true,
    // 是否固定头部
    fixedHeader: true,
    // 是否显示侧边栏Logo
    sidebarLogo: true,
    // 主题色
    theme: "#409EFF",
  });

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebar.value.opened = !sidebar.value.opened;
    sidebar.value.withoutAnimation = false;
    if (sidebar.value.opened) {
      localStorage.setItem("sidebarStatus", "opened");
    } else {
      localStorage.setItem("sidebarStatus", "closed");
    }
  };

  // 关闭侧边栏
  const closeSidebar = (withoutAnimation) => {
    localStorage.setItem("sidebarStatus", "closed");
    sidebar.value.opened = false;
    sidebar.value.withoutAnimation = withoutAnimation;
  };

  // 切换设备
  const toggleDevice = (val) => {
    device.value = val;
  };

  // 更改设置
  const changeSetting = (key, value) => {
    // 确保key存在于settings中
    if (Object.prototype.hasOwnProperty.call(settings.value, key)) {
      settings.value[key] = value;
      // 保存到本地存储
      localStorage.setItem(`app_${key}`, JSON.stringify(value));
    }
  };

  // 初始化设置，从本地存储加载
  const initSettings = () => {
    Object.keys(settings.value).forEach((key) => {
      const storedValue = localStorage.getItem(`app_${key}`);
      if (storedValue !== null) {
        try {
          settings.value[key] = JSON.parse(storedValue);
        } catch (e) {
          console.error(`Error parsing stored setting for ${key}:`, e);
        }
      }
    });
  };

  // 初始化设置
  initSettings();

  return {
    sidebar,
    device,
    settings,
    toggleSidebar,
    closeSidebar,
    toggleDevice,
    changeSetting,
  };
});
