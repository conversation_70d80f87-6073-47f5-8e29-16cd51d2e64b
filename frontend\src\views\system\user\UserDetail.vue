<template>
  <el-drawer
    v-model="visible"
    :title="title"
    direction="rtl"
    size="600px"
    :before-close="handleClose"
  >
    <div class="user-detail" v-loading="loading">
      <!-- 用户基本信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button type="primary" size="small" @click="handleEdit">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </div>
        </template>
        
        <div class="user-info">
          <div class="avatar-section">
            <el-avatar :size="80" :src="userInfo.avatar" class="user-avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="avatar-actions">
              <el-button size="small" @click="handleAvatarUpload">
                更换头像
              </el-button>
            </div>
          </div>
          
          <div class="info-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="用户名">
                {{ userInfo.username }}
              </el-descriptions-item>
              <el-descriptions-item label="昵称">
                {{ userInfo.nickname || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="邮箱">
                {{ userInfo.email || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="手机号">
                {{ userInfo.phone || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="部门">
                {{ userInfo.deptId?.name || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="userInfo.status === 1 ? 'success' : 'danger'">
                  {{ userInfo.status === 1 ? '正常' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间" :span="2">
                {{ formatDate(userInfo.createTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">
                {{ userInfo.remark || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>

      <!-- 角色信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <span>角色信息</span>
        </template>
        
        <div class="roles-section">
          <div v-if="userInfo.roles && userInfo.roles.length > 0" class="role-list">
            <el-tag
              v-for="role in userInfo.roles"
              :key="role._id"
              class="role-tag"
              type="primary"
            >
              {{ role.name }}
            </el-tag>
          </div>
          <el-empty v-else description="暂无角色" :image-size="60" />
        </div>
      </el-card>

      <!-- 操作记录 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <span>操作记录</span>
        </template>
        
        <el-timeline>
          <el-timeline-item
            v-for="log in operationLogs"
            :key="log.id"
            :timestamp="log.timestamp"
            placement="top"
          >
            <el-card>
              <h4>{{ log.action }}</h4>
              <p>{{ log.description }}</p>
              <p class="log-meta">操作人: {{ log.operator }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <!-- 头像上传对话框 -->
    <el-dialog
      v-model="avatarDialogVisible"
      title="更换头像"
      width="400px"
      append-to-body
    >
      <AvatarUpload
        v-model="tempAvatar"
        @success="handleAvatarSuccess"
      />
      <template #footer>
        <el-button @click="avatarDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAvatar" :loading="avatarLoading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, User } from '@element-plus/icons-vue'
import { getUserDetail, updateUserAvatar } from '@/api/user'
import AvatarUpload from '@/components/Upload/AvatarUpload.vue'
import { formatDate } from '@/utils/format'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  userId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit', 'refresh'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const title = ref('用户详情')
const userInfo = reactive({})
const avatarDialogVisible = ref(false)
const tempAvatar = ref('')
const avatarLoading = ref(false)

// 模拟操作记录数据
const operationLogs = ref([
  {
    id: 1,
    action: '创建用户',
    description: '用户账号创建成功',
    operator: 'admin',
    timestamp: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    action: '更新信息',
    description: '更新了用户基本信息',
    operator: 'admin',
    timestamp: '2024-01-16 14:20:00'
  }
])

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.userId) {
    getUserDetailInfo()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取用户详情
const getUserDetailInfo = async () => {
  if (!props.userId) return
  
  loading.value = true
  try {
    const res = await getUserDetail(props.userId)
    Object.assign(userInfo, res.data)
    title.value = `用户详情 - ${userInfo.username}`
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭抽屉
const handleClose = () => {
  visible.value = false
}

// 编辑用户
const handleEdit = () => {
  emit('edit', userInfo)
  handleClose()
}

// 头像上传
const handleAvatarUpload = () => {
  tempAvatar.value = userInfo.avatar || ''
  avatarDialogVisible.value = true
}

// 头像上传成功
const handleAvatarSuccess = (response) => {
  tempAvatar.value = response.data.url
}

// 保存头像
const saveAvatar = async () => {
  if (!tempAvatar.value) {
    ElMessage.warning('请先上传头像')
    return
  }

  avatarLoading.value = true
  try {
    await updateUserAvatar(props.userId, tempAvatar.value)
    userInfo.avatar = tempAvatar.value
    avatarDialogVisible.value = false
    ElMessage.success('头像更新成功')
    emit('refresh')
  } catch (error) {
    console.error('头像更新失败:', error)
    ElMessage.error('头像更新失败')
  } finally {
    avatarLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.user-detail {
  padding: 0 20px 20px;
}

.detail-card {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  gap: 20px;
  
  .avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    
    .user-avatar {
      border: 2px solid #f0f0f0;
    }
  }
  
  .info-section {
    flex: 1;
  }
}

.roles-section {
  .role-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .role-tag {
    margin: 0;
  }
}

.log-meta {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}

:deep(.el-timeline-item__content) {
  padding-bottom: 20px;
}
</style>