const mongoose = require('mongoose');
const User = require('../models/User');
const Role = require('../models/Role');
const Permission = require('../models/Permission');
const Department = require('../models/Department');
const config = require('../config/config');

async function checkData() {
  try {
    console.log('🔍 连接数据库...');
    await mongoose.connect(config.mongodbUri);
    console.log('✅ 数据库连接成功\n');
    
    console.log('📊 数据库数据统计：');
    
    const userCount = await User.countDocuments();
    const roleCount = await Role.countDocuments();
    const permissionCount = await Permission.countDocuments();
    const deptCount = await Department.countDocuments();
    
    console.log(`👥 用户数量: ${userCount}`);
    console.log(`🎭 角色数量: ${roleCount}`);
    console.log(`🔐 权限数量: ${permissionCount}`);
    console.log(`🏢 部门数量: ${deptCount}`);
    
    // 显示管理员用户信息
    const admin = await User.findOne({ username: 'admin' }).populate('roles').populate('deptId');
    if (admin) {
      console.log('\n👤 管理员用户信息：');
      console.log(`   用户名: ${admin.username}`);
      console.log(`   昵称: ${admin.nickname}`);
      console.log(`   邮箱: ${admin.email}`);
      console.log(`   状态: ${admin.status === 1 ? '正常' : '禁用'}`);
      console.log(`   部门: ${admin.deptId?.name || '无'}`);
      console.log(`   角色: ${admin.roles?.map(r => r.name).join(', ') || '无'}`);
    }
    
    // 显示权限树结构
    const permissions = await Permission.find({ parentId: null }).sort({ sort: 1 });
    console.log('\n🌳 权限树结构：');
    for (const perm of permissions) {
      console.log(`   📁 ${perm.name} (${perm.code})`);
      const children = await Permission.find({ parentId: perm._id }).sort({ sort: 1 });
      for (const child of children) {
        console.log(`      📄 ${child.name} (${child.code})`);
        const buttons = await Permission.find({ parentId: child._id }).sort({ sort: 1 });
        for (const btn of buttons) {
          console.log(`         🔘 ${btn.name} (${btn.code})`);
        }
      }
    }
    
    // 显示角色权限信息
    const roles = await Role.find().populate('permissions');
    console.log('\n🎭 角色权限信息：');
    for (const role of roles) {
      console.log(`   角色: ${role.name} (${role.code})`);
      console.log(`   权限数量: ${role.permissions?.length || 0}`);
      console.log(`   状态: ${role.status === 1 ? '正常' : '禁用'}`);
    }
    
    await mongoose.disconnect();
    console.log('\n✅ 数据检查完成');
  } catch (error) {
    console.error('❌ 检查数据失败:', error.message);
    process.exit(1);
  }
}

checkData();
