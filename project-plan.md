# 项目计划：基于 Node.js 和 Vue3 的后台管理系统

## 技术栈

### 后端

- Node.js
- Express.js
- MongoDB
- JWT 认证
- Mongoose ODM

### 前端

- Vue 3
- Vite
- Pinia (状态管理)
- Element Plus (UI 组件库)
- Vue Router
- Axios

## 项目结构

```
ruo/
├── backend/                 # 后端项目
│   ├── config/              # 配置文件
│   ├── controllers/         # 控制器
│   ├── middleware/          # 中间件
│   ├── models/              # 数据模型
│   ├── routes/              # 路由
│   ├── services/            # 业务逻辑
│   ├── utils/               # 工具函数
│   ├── app.js               # 应用入口
│   ├── package.json         # 依赖管理
│   └── .env                 # 环境变量
│
├── frontend/                # 前端项目
│   ├── public/              # 静态资源
│   ├── src/
│   │   ├── api/             # API请求
│   │   ├── assets/          # 资源文件
│   │   ├── components/      # 公共组件
│   │   ├── directives/      # 自定义指令
│   │   ├── hooks/           # 组合式函数
│   │   ├── layout/          # 布局组件
│   │   ├── router/          # 路由配置
│   │   ├── stores/          # Pinia状态管理
│   │   ├── styles/          # 样式文件
│   │   ├── utils/           # 工具函数
│   │   ├── views/           # 页面组件
│   │   ├── App.vue          # 根组件
│   │   ├── main.ts          # 入口文件
│   │   └── permission.ts    # 权限控制
│   ├── .env.development     # 开发环境变量
│   ├── .env.production      # 生产环境变量
│   ├── index.html           # HTML模板
│   ├── package.json         # 依赖管理
│   ├── tsconfig.json        # TypeScript配置
│   └── vite.config.ts       # Vite配置
│
└── README.md                # 项目说明
```

## 数据模型设计

### 用户模型 (User)

```javascript
{
  username: String,       // 用户名
  password: String,       // 密码（加密存储）
  nickname: String,       // 昵称
  email: String,          // 邮箱
  phone: String,          // 手机号
  avatar: String,         // 头像URL
  status: Number,         // 状态：0-禁用，1-正常
  deptId: ObjectId,       // 所属部门ID
  roles: [ObjectId],      // 角色ID数组
  createTime: Date,       // 创建时间
  updateTime: Date,       // 更新时间
  remark: String          // 备注
}
```

### 角色模型 (Role)

```javascript
{
  name: String,           // 角色名称
  code: String,           // 角色编码
  status: Number,         // 状态：0-禁用，1-正常
  permissions: [ObjectId], // 权限ID数组
  createTime: Date,       // 创建时间
  updateTime: Date,       // 更新时间
  remark: String          // 备注
}
```

### 权限模型 (Permission)

```javascript
{
  name: String,           // 权限名称
  type: Number,           // 类型：1-菜单，2-按钮，3-API
  parentId: ObjectId,     // 父级ID
  path: String,           // 路由路径（菜单类型）
  component: String,      // 组件路径（菜单类型）
  perms: String,          // 权限标识
  icon: String,           // 图标（菜单类型）
  sort: Number,           // 排序
  visible: Boolean,       // 是否可见
  status: Number,         // 状态：0-禁用，1-正常
  createTime: Date,       // 创建时间
  updateTime: Date        // 更新时间
}
```

### 部门模型 (Department)

```javascript
{
  name: String,           // 部门名称
  parentId: ObjectId,     // 父级部门ID
  sort: Number,           // 排序
  status: Number,         // 状态：0-禁用，1-正常
  createTime: Date,       // 创建时间
  updateTime: Date,       // 更新时间
  remark: String          // 备注
}
```

## 实施检查清单：

1. [创建项目基本结构, review:true]
2. [设置后端项目并安装依赖, review:true]
3. [设置前端项目并安装依赖, review:true]
4. [实现后端用户认证功能, review:true]
5. [实现后端用户管理功能, review:true]
6. [实现后端角色管理功能, review:true]
7. [实现后端权限管理功能, review:true]
8. [实现后端部门管理功能, review:true]
9. [实现前端登录页面和认证功能, review:true]
10. [实现前端布局和菜单组件, review:true]
11. [实现前端用户管理页面, review:true]
12. [实现前端角色管理页面, review:true]
13. [实现前端权限管理页面, review:true]
14. [实现前端部门管理页面, review:true]
15. [实现前端权限控制功能, review:true]
16. [集成前后端并测试功能, review:true]
17. [优化和完善系统, review:true]
