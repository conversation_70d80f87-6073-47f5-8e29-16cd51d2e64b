<template>
  <div class="request-test">
    <el-card>
      <template #header>
        <h3>请求测试页面</h3>
      </template>

      <el-space direction="vertical" size="large" style="width: 100%">
        <el-alert title="此页面用于调试API请求问题" type="info" show-icon :closable="false" />

        <el-form :model="testForm" label-width="120px">
          <el-form-item label="权限ID">
            <el-input v-model="testForm.permissionId" placeholder="输入权限ID" />
          </el-form-item>

          <el-form-item label="状态">
            <el-radio-group v-model="testForm.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="testUpdateStatus" :loading="loading">
              测试状态更新
            </el-button>
            <el-button @click="testGetPermissions" :loading="loading">
              测试获取权限列表
            </el-button>
            <el-button @click="testAuth" :loading="loading">
              测试认证状态
            </el-button>
          </el-form-item>
        </el-form>

        <el-divider>请求日志</el-divider>

        <el-card>
          <template #header>
            <span>控制台日志</span>
            <el-button size="small" @click="clearLogs" style="float: right">
              清空日志
            </el-button>
          </template>

          <div class="logs">
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span :class="['log-level', `log-${log.level}`]">{{ log.level.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </el-card>

        <el-card>
          <template #header>
            <span>环境信息</span>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="BASE_API">
              {{ envInfo.baseAPI }}
            </el-descriptions-item>
            <el-descriptions-item label="USE_MOCK">
              {{ envInfo.useMock }}
            </el-descriptions-item>
            <el-descriptions-item label="当前URL">
              {{ envInfo.currentURL }}
            </el-descriptions-item>
            <el-descriptions-item label="代理状态">
              {{ envInfo.proxyStatus }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-space>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { updatePermissionStatus, getPermissionTree } from '@/api/permission';
import { getInfo } from '@/api/auth';
import { getToken } from '@/utils/auth';

// 表单数据
const testForm = reactive({
  permissionId: '68635ae95af3638ae7ffe7cf',
  status: 1
});

// 加载状态
const loading = ref(false);

// 日志数据
const logs = ref([]);

// 环境信息
const envInfo = reactive({
  baseAPI: import.meta.env.VITE_APP_BASE_API,
  useMock: import.meta.env.VITE_USE_MOCK,
  currentURL: window.location.href,
  proxyStatus: '检测中...'
});

// 添加日志
const addLog = (level, message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message: typeof message === 'object' ? JSON.stringify(message, null, 2) : message
  });

  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50);
  }
};

// 清空日志
const clearLogs = () => {
  logs.value = [];
};

// 测试状态更新
const testUpdateStatus = async () => {
  if (!testForm.permissionId) {
    ElMessage.warning('请输入权限ID');
    return;
  }

  loading.value = true;
  addLog('info', `开始测试状态更新: ID=${testForm.permissionId}, status=${testForm.status}`);

  try {
    const result = await updatePermissionStatus(testForm.permissionId, testForm.status);
    addLog('success', '状态更新成功');
    addLog('info', result);
    ElMessage.success('状态更新成功');
  } catch (error) {
    addLog('error', '状态更新失败');
    addLog('error', error.message || error);
    ElMessage.error('状态更新失败: ' + (error.message || error));
  } finally {
    loading.value = false;
  }
};

// 测试获取权限列表
const testGetPermissions = async () => {
  loading.value = true;
  addLog('info', '开始测试获取权限列表');

  try {
    const result = await getPermissionTree();
    addLog('success', '获取权限列表成功');
    addLog('info', `获取到 ${result.data?.length || 0} 条权限数据`);
    ElMessage.success('获取权限列表成功');
  } catch (error) {
    addLog('error', '获取权限列表失败');
    addLog('error', error.message || error);
    ElMessage.error('获取权限列表失败: ' + (error.message || error));
  } finally {
    loading.value = false;
  }
};

// 测试认证状态
const testAuth = async () => {
  loading.value = true;
  addLog('info', '开始测试认证状态');

  try {
    // 检查本地token
    const token = getToken();
    addLog('info', `本地Token: ${token ? '存在' : '不存在'}`);

    if (token) {
      addLog('info', `Token长度: ${token.length}`);
      addLog('info', `Token前缀: ${token.substring(0, 20)}...`);
    }

    // 测试获取用户信息
    const result = await getInfo();
    addLog('success', '获取用户信息成功');
    addLog('info', `用户: ${result.data?.username} (${result.data?.nickname})`);
    addLog('info', `权限数量: ${result.data?.permissions?.length || 0}`);

    // 检查是否有权限更新权限
    const hasPermissionUpdate = result.data?.permissions?.includes('system:permission:update');
    addLog('info', `权限更新权限: ${hasPermissionUpdate ? '有' : '无'}`);

    ElMessage.success('认证状态正常');
  } catch (error) {
    addLog('error', '认证状态检查失败');
    addLog('error', error.response?.data || error.message || error);
    ElMessage.error('认证状态异常: ' + (error.response?.data?.message || error.message || error));
  } finally {
    loading.value = false;
  }
};

// 检测代理状态
const checkProxyStatus = async () => {
  try {
    const response = await fetch('/api/v1/test');
    if (response.ok) {
      envInfo.proxyStatus = '代理正常';
    } else {
      envInfo.proxyStatus = `代理异常 (${response.status})`;
    }
  } catch (error) {
    envInfo.proxyStatus = '代理失败';
  }
};

// 重写console方法来捕获日志
const originalConsole = {
  log: console.log,
  warn: console.warn,
  error: console.error,
  info: console.info
};

const interceptConsole = () => {
  console.log = (...args) => {
    addLog('info', args.join(' '));
    originalConsole.log(...args);
  };

  console.warn = (...args) => {
    addLog('warn', args.join(' '));
    originalConsole.warn(...args);
  };

  console.error = (...args) => {
    addLog('error', args.join(' '));
    originalConsole.error(...args);
  };

  console.info = (...args) => {
    addLog('info', args.join(' '));
    originalConsole.info(...args);
  };
};

onMounted(() => {
  interceptConsole();
  checkProxyStatus();
  addLog('info', '请求测试页面已加载');
});
</script>

<style lang="scss" scoped>
.request-test {
  padding: 20px;
}

.logs {
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 4px;
  display: flex;
  gap: 8px;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-info {
  color: #409eff;
}

.log-success {
  color: #67c23a;
}

.log-warn {
  color: #e6a23c;
}

.log-error {
  color: #f56c6c;
}

.log-message {
  flex: 1;
  word-break: break-all;
}
</style>
