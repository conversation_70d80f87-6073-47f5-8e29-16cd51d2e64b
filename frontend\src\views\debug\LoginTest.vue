<template>
  <div class="login-test">
    <el-card>
      <template #header>
        <h3>登录功能测试</h3>
      </template>
      
      <el-space direction="vertical" size="large" style="width: 100%">
        <el-alert
          title="此页面用于测试登录功能"
          type="info"
          show-icon
          :closable="false"
        />
        
        <el-form :model="loginForm" label-width="100px">
          <el-form-item label="用户名">
            <el-input v-model="loginForm.username" placeholder="请输入用户名" />
          </el-form-item>
          
          <el-form-item label="密码">
            <el-input 
              v-model="loginForm.password" 
              type="password" 
              placeholder="请输入密码" 
              show-password 
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="testLogin" :loading="loading">
              测试登录
            </el-button>
            <el-button @click="testGetProfile" :loading="loading">
              测试获取用户信息
            </el-button>
            <el-button @click="clearToken">
              清除Token
            </el-button>
          </el-form-item>
        </el-form>
        
        <el-divider>当前状态</el-divider>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="登录状态">
            <el-tag :type="userStore.isLogin ? 'success' : 'danger'">
              {{ userStore.isLogin ? '已登录' : '未登录' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ userStore.username || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="昵称">
            {{ userStore.nickname || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户ID">
            {{ userStore.userId || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="角色">
            {{ userStore.roles.join(', ') || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="Token">
            <el-text class="token-text" truncated>
              {{ userStore.token || '无' }}
            </el-text>
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider>测试日志</el-divider>
        
        <el-card>
          <template #header>
            <span>操作日志</span>
            <el-button size="small" @click="clearLogs" style="float: right">
              清空日志
            </el-button>
          </template>
          
          <div class="logs">
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span :class="['log-level', `log-${log.level}`]">{{ log.level.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </el-card>
      </el-space>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores/user';
import { login, getInfo } from '@/api/auth';
import { removeToken } from '@/utils/auth';

const userStore = useUserStore();

// 表单数据
const loginForm = reactive({
  username: 'admin',
  password: '123456'
});

// 加载状态
const loading = ref(false);

// 日志数据
const logs = ref([]);

// 添加日志
const addLog = (level, message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message: typeof message === 'object' ? JSON.stringify(message, null, 2) : message
  });
  
  // 限制日志数量
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20);
  }
};

// 清空日志
const clearLogs = () => {
  logs.value = [];
};

// 测试登录
const testLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    ElMessage.warning('请输入用户名和密码');
    return;
  }
  
  loading.value = true;
  addLog('info', `开始测试登录: ${loginForm.username}`);
  
  try {
    // 直接调用API
    const result = await login(loginForm);
    addLog('success', '登录API调用成功');
    addLog('info', result);
    
    // 使用store登录
    await userStore.loginAction(loginForm);
    addLog('success', 'Store登录成功');
    
    ElMessage.success('登录成功');
  } catch (error) {
    addLog('error', '登录失败');
    addLog('error', error.message || error);
    ElMessage.error('登录失败: ' + (error.message || error));
  } finally {
    loading.value = false;
  }
};

// 测试获取用户信息
const testGetProfile = async () => {
  loading.value = true;
  addLog('info', '开始测试获取用户信息');
  
  try {
    // 直接调用API
    const result = await getInfo();
    addLog('success', '获取用户信息API调用成功');
    addLog('info', result);
    
    // 使用store获取用户信息
    await userStore.getUserInfo();
    addLog('success', 'Store获取用户信息成功');
    
    ElMessage.success('获取用户信息成功');
  } catch (error) {
    addLog('error', '获取用户信息失败');
    addLog('error', error.message || error);
    ElMessage.error('获取用户信息失败: ' + (error.message || error));
  } finally {
    loading.value = false;
  }
};

// 清除Token
const clearToken = () => {
  removeToken();
  userStore.resetState();
  addLog('info', '已清除Token和用户状态');
  ElMessage.success('已清除Token');
};
</script>

<style lang="scss" scoped>
.login-test {
  padding: 20px;
}

.token-text {
  max-width: 200px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.logs {
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 4px;
  display: flex;
  gap: 8px;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-info {
  color: #409eff;
}

.log-success {
  color: #67c23a;
}

.log-warn {
  color: #e6a23c;
}

.log-error {
  color: #f56c6c;
}

.log-message {
  flex: 1;
  word-break: break-all;
}
</style>
