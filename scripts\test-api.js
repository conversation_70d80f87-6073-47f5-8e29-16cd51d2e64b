#!/usr/bin/env node

/**
 * API 测试脚本
 * 测试用户状态更新等关键功能
 */

const http = require("http");

// 测试配置
const API_BASE = "http://localhost:3000/api/v1";
const TEST_TOKEN = "your-test-token"; // 需要替换为实际的token

/**
 * 发送HTTP请求
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = "";

      res.on("data", (chunk) => {
        responseData += chunk;
      });

      res.on("end", () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsed,
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData,
          });
        }
      });
    });

    req.on("error", (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

/**
 * 测试获取用户列表
 */
async function testGetUsers() {
  console.log("🧪 测试获取用户列表...");

  const options = {
    hostname: "localhost",
    port: 3000,
    path: "/api/v1/users?page=1&limit=10",
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${TEST_TOKEN}`,
    },
  };

  try {
    const result = await makeRequest(options);

    if (result.statusCode === 200 && result.data.success) {
      console.log("✅ 获取用户列表成功");
      console.log(`   用户数量: ${result.data.data.length}`);

      // 返回第一个用户用于后续测试
      if (result.data.data.length > 0) {
        const firstUser = result.data.data[0];
        console.log(`   第一个用户ID: ${firstUser._id}`);
        return firstUser;
      }
    } else {
      console.log("❌ 获取用户列表失败");
      console.log(`   状态码: ${result.statusCode}`);
      console.log(`   响应: ${JSON.stringify(result.data, null, 2)}`);
    }
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
  }

  return null;
}

/**
 * 测试更新用户状态
 */
async function testUpdateUserStatus(userId, status) {
  console.log(`🧪 测试更新用户状态 (ID: ${userId}, 状态: ${status})...`);

  const options = {
    hostname: "localhost",
    port: 3000,
    path: `/api/v1/users/${userId}/status`,
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${TEST_TOKEN}`,
    },
  };

  try {
    const result = await makeRequest(options, { status });

    if (result.statusCode === 200 && result.data.success) {
      console.log("✅ 更新用户状态成功");
      console.log(`   响应消息: ${result.data.message}`);
    } else {
      console.log("❌ 更新用户状态失败");
      console.log(`   状态码: ${result.statusCode}`);
      console.log(`   响应: ${JSON.stringify(result.data, null, 2)}`);
    }
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log("🚀 开始API测试...\n");

  // 测试后端连接
  console.log("🔗 测试后端连接...");
  try {
    const result = await makeRequest({
      hostname: "localhost",
      port: 3000,
      path: "/api/v1/test",
      method: "GET",
    });

    if (result.statusCode === 200) {
      console.log("✅ 后端连接正常\n");
    } else {
      console.log("❌ 后端连接失败");
      console.log("请确保后端服务已启动");
      return;
    }
  } catch (error) {
    console.log("❌ 无法连接到后端服务");
    console.log("请确保后端服务已启动在 http://localhost:3000");
    return;
  }

  // 如果没有token，跳过需要认证的测试
  if (!TEST_TOKEN || TEST_TOKEN === "your-test-token") {
    console.log("⚠️  未配置测试token，跳过需要认证的API测试");
    console.log("请在脚本中设置有效的TEST_TOKEN");
  } else {
    // 测试获取用户列表
    const user = await testGetUsers();

    if (user) {
      // 测试更新用户状态
      await testUpdateUserStatus(user._id, user.status === 1 ? 0 : 1);
    }
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}
