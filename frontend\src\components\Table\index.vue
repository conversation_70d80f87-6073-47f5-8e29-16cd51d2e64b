<template>
  <div class="table-container">
    <!-- 表格工具栏 -->
    <div v-if="showToolbar" class="table-toolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <el-button v-if="showAdd" type="primary" :icon="Plus" @click="handleAdd">
            新增
          </el-button>
          <el-button v-if="showBatchDelete && selectedRows.length > 0" type="danger" :icon="Delete"
            @click="handleBatchDelete">
            批量删除
          </el-button>
        </slot>
      </div>
      <div class="toolbar-right">
        <slot name="toolbar-right">
          <el-button :icon="Refresh" @click="handleRefresh" />
        </slot>
      </div>
    </div>

    <!-- 表格 -->
    <el-table ref="tableRef" v-loading="loading" :data="data" :height="height" :max-height="maxHeight" :stripe="stripe"
      :border="border" :size="size" :row-key="rowKey" @selection-change="handleSelectionChange"
      @sort-change="handleSortChange" v-bind="$attrs">
      <!-- 多选列 -->
      <el-table-column v-if="showSelection" type="selection" width="55" :reserve-selection="reserveSelection" />

      <!-- 序号列 -->
      <el-table-column v-if="showIndex" type="index" label="序号" width="60" :index="indexMethod" />

      <!-- 动态列 -->
      <template v-for="column in columns" :key="column.prop">
        <el-table-column :prop="column.prop" :label="column.label" :width="column.width" :min-width="column.minWidth"
          :fixed="column.fixed" :sortable="column.sortable" :align="column.align || 'left'"
          :show-overflow-tooltip="column.showOverflowTooltip !== false">
          <template #default="scope">
            <!-- 自定义插槽 -->
            <slot v-if="column.slot" :name="column.slot" :row="scope.row" :column="column" :$index="scope.$index" />
            <!-- 状态标签 -->
            <el-tag v-else-if="column.type === 'status'" :type="getStatusType(scope.row[column.prop])">
              {{ getStatusText(scope.row[column.prop], column.statusMap) }}
            </el-tag>
            <!-- 日期格式化 -->
            <span v-else-if="column.type === 'date'">
              {{ formatDate(scope.row[column.prop], column.format) }}
            </span>
            <!-- 默认显示 -->
            <span v-else>{{ scope.row[column.prop] }}</span>
          </template>
        </el-table-column>
      </template>

      <!-- 操作列 -->
      <el-table-column v-if="showActions" label="操作" :width="actionWidth" :fixed="actionFixed" align="center">
        <template #default="scope">
          <slot name="actions" :row="scope.row" :$index="scope.$index">
            <el-button v-if="showEdit" type="primary" size="small" link @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button v-if="showDelete" type="danger" size="small" link @click="handleDelete(scope.row)">
              删除
            </el-button>
          </slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div v-if="showPagination" class="table-pagination">
      <el-pagination :current-page="currentPage" :page-size="pageSize" :total="total" :page-sizes="pageSizes"
        :layout="paginationLayout" :background="true" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Refresh } from '@element-plus/icons-vue';
import { formatDate } from '@/utils/format';

// Props
const props = defineProps({
  // 表格数据
  data: {
    type: Array,
    default: () => [],
  },
  // 表格列配置
  columns: {
    type: Array,
    default: () => [],
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false,
  },
  // 表格高度
  height: {
    type: [String, Number],
    default: undefined,
  },
  // 表格最大高度
  maxHeight: {
    type: [String, Number],
    default: undefined,
  },
  // 是否显示斑马纹
  stripe: {
    type: Boolean,
    default: true,
  },
  // 是否显示边框
  border: {
    type: Boolean,
    default: true,
  },
  // 表格尺寸
  size: {
    type: String,
    default: 'default',
  },
  // 行数据的Key
  rowKey: {
    type: String,
    default: 'id',
  },
  // 是否显示工具栏
  showToolbar: {
    type: Boolean,
    default: true,
  },
  // 是否显示多选
  showSelection: {
    type: Boolean,
    default: false,
  },
  // 是否显示序号
  showIndex: {
    type: Boolean,
    default: false,
  },
  // 是否显示操作列
  showActions: {
    type: Boolean,
    default: true,
  },
  // 操作列宽度
  actionWidth: {
    type: [String, Number],
    default: 150,
  },
  // 操作列是否固定
  actionFixed: {
    type: String,
    default: 'right',
  },
  // 是否显示新增按钮
  showAdd: {
    type: Boolean,
    default: true,
  },
  // 是否显示编辑按钮
  showEdit: {
    type: Boolean,
    default: true,
  },
  // 是否显示删除按钮
  showDelete: {
    type: Boolean,
    default: true,
  },
  // 是否显示批量删除按钮
  showBatchDelete: {
    type: Boolean,
    default: false,
  },
  // 是否保留选择
  reserveSelection: {
    type: Boolean,
    default: false,
  },
  // 是否显示分页
  showPagination: {
    type: Boolean,
    default: true,
  },
  // 当前页
  currentPage: {
    type: Number,
    default: 1,
  },
  // 每页条数
  pageSize: {
    type: Number,
    default: 10,
  },
  // 总条数
  total: {
    type: Number,
    default: 0,
  },
  // 每页条数选项
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100],
  },
  // 分页布局
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper',
  },
});

// Emits
const emit = defineEmits([
  'add',
  'edit',
  'delete',
  'batch-delete',
  'refresh',
  'selection-change',
  'sort-change',
  'size-change',
  'current-change',
]);

// Refs
const tableRef = ref();
const selectedRows = ref([]);

// 序号方法
const indexMethod = (index) => {
  return (props.currentPage - 1) * props.pageSize + index + 1;
};

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    0: 'danger',
    1: 'success',
  };
  return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status, statusMap) => {
  if (statusMap) {
    return statusMap[status] || '未知';
  }
  const defaultMap = {
    0: '禁用',
    1: '正常',
  };
  return defaultMap[status] || '未知';
};

// 事件处理
const handleAdd = () => {
  emit('add');
};

const handleEdit = (row) => {
  emit('edit', row);
};

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    emit('delete', row);
  });
};

const handleBatchDelete = () => {
  ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条记录吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    emit('batch-delete', selectedRows.value);
  });
};

const handleRefresh = () => {
  emit('refresh');
};

const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
  emit('selection-change', selection);
};

const handleSortChange = (sort) => {
  emit('sort-change', sort);
};

const handleSizeChange = (size) => {
  emit('size-change', size);
};

const handleCurrentChange = (page) => {
  emit('current-change', page);
};

// 暴露方法
defineExpose({
  tableRef,
  selectedRows,
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row, selected) => tableRef.value?.toggleRowSelection(row, selected),
});
</script>

<style lang="scss" scoped>
.table-container {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .toolbar-left {
      display: flex;
      gap: 8px;
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }

  .table-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }
}
</style>
