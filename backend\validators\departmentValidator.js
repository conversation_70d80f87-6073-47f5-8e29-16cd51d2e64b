const { body } = require("express-validator");
const Department = require("../models/Department");

/**
 * 部门创建验证规则
 */
const createDepartmentValidator = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("部门名称不能为空")
    .isLength({ max: 50 })
    .withMessage("部门名称长度不能超过50个字符"),

  body("parentId").optional({ nullable: true }),

  body("leader")
    .optional()
    .isLength({ max: 50 })
    .withMessage("负责人名称长度不能超过50个字符"),

  body("phone")
    .optional()
    .isLength({ max: 20 })
    .withMessage("联系电话长度不能超过20个字符")
    .isMobilePhone("zh-CN")
    .withMessage("联系电话格式不正确"),

  body("email")
    .optional({ checkFalsy: true })
    .isEmail()
    .withMessage("邮箱格式不正确"),

  body("status").optional().isIn([0, 1]).withMessage("状态值无效"),

  body("sort").optional().isInt({ min: 0 }).withMessage("排序值必须是非负整数"),
];

/**
 * 部门更新验证规则
 */
const updateDepartmentValidator = [
  body("name")
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage("部门名称长度不能超过50个字符"),

  body("parentId")
    .optional({ nullable: true })
    .custom(async (value, { req }) => {
      if (!value) return true;
      // 不能将部门的父级设置为自己
      if (value.toString() === req.params.id) {
        throw new Error("父级部门不能是自己");
      }
      return true;
    }),

  body("leader")
    .optional()
    .isLength({ max: 50 })
    .withMessage("负责人名称长度不能超过50个字符"),

  body("phone")
    .optional()
    .isLength({ max: 20 })
    .withMessage("联系电话长度不能超过20个字符")
    .isMobilePhone("zh-CN")
    .withMessage("联系电话格式不正确"),

  body("email")
    .optional({ checkFalsy: true })
    .isEmail()
    .withMessage("邮箱格式不正确"),

  body("status").optional().isIn([0, 1]).withMessage("状态值无效"),

  body("sort").optional().isInt({ min: 0 }).withMessage("排序值必须是非负整数"),
];

/**
 * 部门状态验证规则
 */
const departmentStatusValidator = [
  body("status").isIn([0, 1]).withMessage("状态值必须为0或1"),
];

module.exports = {
  createDepartmentValidator,
  updateDepartmentValidator,
  departmentStatusValidator,
};
