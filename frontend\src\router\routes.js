// 使用一个函数来获取布局组件
const getLayout = () => import("@/layout/index.vue");

// 定义常量路由数组
export const constantRoutes = [
  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register/index.vue"),
    hidden: true,
  },
  {
    path: "/redirect/:path(.*)",
    component: () => import("@/views/redirect/index.vue"),
    hidden: true,
  },
  {
    path: "/user",
    component: getLayout,
    children: [
      {
        path: "profile",
        name: "UserProfile",
        component: () => import("@/views/user/profile/index.vue"),
        meta: {
          title: "个人中心",
          icon: "User",
        },
      },
    ],
    hidden: true,
  },
  {
    path: "/error-page",
    component: getLayout,
    hidden: true,
    children: [
      {
        path: "401",
        name: "Error401",
        component: () => import("@/views/error-page/401.vue"),
        meta: { title: "401", icon: "Warning" },
      },
      {
        path: "404",
        name: "Error404",
        component: () => import("@/views/error-page/404.vue"),
        meta: { title: "404", icon: "WarningFilled" },
      },
    ],
  },
  {
    path: "/debug",
    component: getLayout,
    meta: { title: "调试", icon: "Tools" },
    children: [
      {
        path: "LoginTest",
        name: "LoginTest",
        component: () => import("@/views/debug/LoginTest.vue"),
        meta: { title: "登录测试", icon: "User" },
      },
      {
        path: "RequestTest",
        name: "RequestTest",
        component: () => import("@/views/debug/RequestTest.vue"),
        meta: { title: "请求测试", icon: "Document" },
      },
    ],
    hidden: true,
  },
  {
    path: "/",
    component: getLayout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        name: "Dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        meta: {
          title: "仪表盘",
          icon: "Odometer",
          affix: true,
        },
      },
    ],
  },
];

// 异步路由，需要根据用户角色动态加载的路由
export const asyncRoutes = [
  {
    path: "/system",
    component: getLayout,
    redirect: "/system/user",
    alwaysShow: true,
    name: "System",
    meta: {
      title: "系统管理",
      icon: "Setting",
      roles: ["admin"],
    },
    children: [
      {
        path: "user",
        name: "User",
        component: () => import("@/views/system/user/index.vue"),
        meta: {
          title: "用户管理",
          icon: "User",
          roles: ["admin"],
        },
      },
      {
        path: "role",
        name: "Role",
        component: () => import("@/views/system/role/index.vue"),
        meta: {
          title: "角色管理",
          icon: "UserFilled",
          roles: ["admin"],
        },
      },
      {
        path: "permission",
        name: "Permission",
        component: () => import("@/views/system/permission/index.vue"),
        meta: {
          title: "权限管理",
          icon: "Lock",
          roles: ["admin"],
        },
      },
      {
        path: "department",
        name: "Department",
        component: () => import("@/views/system/department/index.vue"),
        meta: {
          title: "部门管理",
          icon: "OfficeBuilding",
          roles: ["admin"],
        },
      },
      {
        path: "menu",
        name: "Menu",
        component: () => import("@/views/system/menu/index.vue"),
        meta: {
          title: "菜单管理",
          icon: "Menu",
          roles: ["admin"],
        },
      },
      {
        path: "monitor",
        name: "Monitor",
        component: () => import("@/views/system/monitor.vue"),
        meta: {
          title: "系统监控",
          icon: "Monitor",
          roles: ["admin"],
        },
      },
    ],
  },
];
