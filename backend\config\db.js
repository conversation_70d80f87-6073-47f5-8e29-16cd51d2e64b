const mongoose = require("mongoose");
const config = require("./config");

// 连接MongoDB数据库
const connectDB = async () => {
  try {
    // 移除已弃用的选项
    const conn = await mongoose.connect(config.mongodbUri);

    console.log(`MongoDB 连接成功: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error(`MongoDB 连接错误: ${error.message}`);
    console.log("继续启动应用程序，但数据库功能将不可用");
    // 不结束进程，允许应用继续运行
    // process.exit(1);
  }
};

module.exports = connectDB;
