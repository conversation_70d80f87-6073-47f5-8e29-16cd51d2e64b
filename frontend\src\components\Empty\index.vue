<template>
  <div class="empty-container">
    <div class="empty-content">
      <!-- 图标 -->
      <div class="empty-icon">
        <slot name="icon">
          <el-icon :size="iconSize">
            <component :is="icon" />
          </el-icon>
        </slot>
      </div>
      
      <!-- 描述文本 -->
      <div class="empty-description">
        <slot name="description">
          {{ description }}
        </slot>
      </div>
      
      <!-- 操作按钮 -->
      <div v-if="$slots.default || showAction" class="empty-actions">
        <slot>
          <el-button v-if="showAction" type="primary" @click="handleAction">
            {{ actionText }}
          </el-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Document, Box, Warning } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  // 图标
  icon: {
    type: [String, Object],
    default: () => Document,
  },
  // 图标大小
  iconSize: {
    type: [String, Number],
    default: 64,
  },
  // 描述文本
  description: {
    type: String,
    default: '暂无数据',
  },
  // 是否显示操作按钮
  showAction: {
    type: Boolean,
    default: false,
  },
  // 操作按钮文本
  actionText: {
    type: String,
    default: '重新加载',
  },
});

// Emits
const emit = defineEmits(['action']);

// 处理操作按钮点击
const handleAction = () => {
  emit('action');
};
</script>

<style lang="scss" scoped>
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 40px 20px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-description {
  margin-bottom: 16px;
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
}

.empty-actions {
  .el-button + .el-button {
    margin-left: 12px;
  }
}
</style>
