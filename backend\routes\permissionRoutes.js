const express = require("express");
const router = express.Router();
const {
  getPermissionTree,
  getMenuTree,
  getPermissionById,
  createPermission,
  updatePermission,
  deletePermission,
  updatePermissionStatus,
} = require("../controllers/permissionController");
const { authenticate, authorize } = require("../middleware/auth");
const { validate } = require("../middleware/validator");
const {
  createPermissionValidator,
  updatePermissionValidator,
  permissionStatusValidator,
} = require("../validators/permissionValidator");

// 所有权限路由都需要认证
router.use(authenticate);

// 获取权限树
router.get("/tree", authorize("system:permission:list"), getPermissionTree);

// 获取当前用户的菜单树
router.get("/menu-tree", getMenuTree);

// 获取单个权限
router.get("/:id", authorize("system:permission:query"), getPermissionById);

// 创建权限
router.post(
  "/",
  authorize("system:permission:create"),
  createPermissionValidator,
  validate,
  createPermission
);

// 更新权限
router.put(
  "/:id",
  authorize("system:permission:update"),
  updatePermissionValidator,
  validate,
  updatePermission
);

// 删除权限
router.delete("/:id", authorize("system:permission:delete"), deletePermission);

// 更新权限状态
router.patch(
  "/:id/status",
  authorize("system:permission:update"),
  permissionStatusValidator,
  validate,
  updatePermissionStatus
);

module.exports = router;
