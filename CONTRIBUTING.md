# 贡献指南

感谢您对本项目的关注和贡献！本文档将帮助您了解如何参与项目开发。

## 🤝 如何贡献

### 报告问题

如果您发现了 bug 或有功能建议，请：

1. 检查 [Issues](../../issues) 确认问题未被报告
2. 创建新的 Issue，详细描述问题
3. 提供复现步骤和环境信息
4. 如果可能，提供解决方案建议

### 提交代码

1. **Fork 项目**

   ```bash
   # 点击页面右上角的 Fork 按钮
   ```

2. **克隆到本地**

   ```bash
   git clone https://github.com/your-username/ruo-admin.git
   cd ruo-admin
   ```

3. **创建功能分支**

   ```bash
   git checkout -b feature/your-feature-name
   # 或者修复bug
   git checkout -b fix/your-bug-fix
   ```

4. **安装依赖**

   ```bash
   npm run setup
   ```

5. **进行开发**

   - 遵循代码规范
   - 添加必要的测试
   - 更新相关文档

6. **提交更改**

   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

7. **推送分支**

   ```bash
   git push origin feature/your-feature-name
   ```

8. **创建 Pull Request**
   - 详细描述更改内容
   - 关联相关的 Issue
   - 等待代码审查

## 📝 代码规范

### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明：**

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例：**

```
feat(auth): add password reset functionality
fix(api): resolve user creation validation error
docs: update installation guide
```

### JavaScript/Node.js 规范

- 使用 ES6+ 语法
- 使用 2 空格缩进
- 使用分号结尾
- 使用 camelCase 命名变量和函数
- 使用 PascalCase 命名类和构造函数
- 使用 UPPER_SNAKE_CASE 命名常量

```javascript
// ✅ 好的示例
const userName = "admin";
const API_BASE_URL = "http://localhost:3000";

class UserService {
  async createUser(userData) {
    // 实现逻辑
  }
}

// ❌ 不好的示例
const user_name = "admin";
const apiBaseUrl = "http://localhost:3000";

class userService {
  async create_user(user_data) {
    // 实现逻辑
  }
}
```

### Vue.js 规范

- 使用 Composition API
- 组件名使用 PascalCase
- Props 使用 camelCase
- 事件名使用 kebab-case

```vue
<!-- ✅ 好的示例 -->
<template>
  <div class="user-form">
    <el-form @submit="handleSubmit">
      <!-- 表单内容 -->
    </el-form>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  userId: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(["user-created"]);

const handleSubmit = () => {
  emit("user-created", userData);
};
</script>
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
npm test

# 运行后端测试
cd backend && npm test

# 运行前端测试
cd frontend && npm test
```

### 编写测试

- 为新功能编写单元测试
- 为 API 端点编写集成测试
- 确保测试覆盖率不低于 80%

```javascript
// 示例：用户服务测试
describe("UserService", () => {
  test("should create user successfully", async () => {
    const userData = {
      username: "testuser",
      email: "<EMAIL>",
      password: "password123",
    };

    const user = await UserService.create(userData);

    expect(user).toBeDefined();
    expect(user.username).toBe(userData.username);
    expect(user.email).toBe(userData.email);
  });
});
```

## 📚 文档

### 更新文档

- 新功能需要更新相关文档
- API 变更需要更新 API 文档
- 配置变更需要更新配置说明

### 文档结构

```
docs/
├── api/              # API文档
├── deployment/       # 部署文档
├── development/      # 开发文档
└── user-guide/       # 用户指南
```

## 🔍 代码审查

### 审查清单

**功能性：**

- [ ] 功能是否按预期工作
- [ ] 是否处理了边界情况
- [ ] 是否有适当的错误处理

**代码质量：**

- [ ] 代码是否清晰易读
- [ ] 是否遵循项目规范
- [ ] 是否有重复代码

**性能：**

- [ ] 是否有性能问题
- [ ] 数据库查询是否优化
- [ ] 是否有内存泄漏

**安全性：**

- [ ] 是否有安全漏洞
- [ ] 输入是否经过验证
- [ ] 敏感信息是否泄露

## 🚀 发布流程

### 版本号规范

使用 [Semantic Versioning](https://semver.org/)：

- `MAJOR.MINOR.PATCH`
- `1.0.0` → `1.0.1` (补丁版本)
- `1.0.0` → `1.1.0` (次要版本)
- `1.0.0` → `2.0.0` (主要版本)

### 发布步骤

1. 更新版本号
2. 更新 CHANGELOG.md
3. 创建 Git 标签
4. 发布到生产环境

## 💬 社区

### 沟通渠道

- **Issues**: 报告 bug 和功能请求
- **Discussions**: 一般讨论和问答
- **Pull Requests**: 代码审查和讨论

### 行为准则

- 尊重他人，保持友善
- 建设性地提供反馈
- 专注于技术讨论
- 避免人身攻击和歧视

## 🙏 致谢

感谢所有贡献者的努力！您的贡献让这个项目变得更好。

### 贡献者列表

<!-- 这里会自动生成贡献者列表 -->

## 📞 联系我们

如果您有任何问题或建议，请通过以下方式联系我们：

- 创建 [Issue](../../issues/new)
- 发起 [Discussion](../../discussions)
- 发送邮件至：[<EMAIL>]

---

再次感谢您的贡献！🎉
