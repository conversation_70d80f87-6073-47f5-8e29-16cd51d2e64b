<template>
  <div v-if="visible" class="loading-overlay" :class="{ 'full-screen': fullscreen }">
    <div class="loading-content">
      <el-icon class="loading-icon" :size="size">
        <Loading />
      </el-icon>
      <div v-if="text" class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<script setup>
import { Loading } from '@element-plus/icons-vue';

// Props
defineProps({
  // 是否显示
  visible: {
    type: Boolean,
    default: false,
  },
  // 加载文本
  text: {
    type: String,
    default: '加载中...',
  },
  // 图标大小
  size: {
    type: [String, Number],
    default: 40,
  },
  // 是否全屏
  fullscreen: {
    type: Boolean,
    default: false,
  },
});
</script>

<style lang="scss" scoped>
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  &.full-screen {
    position: fixed;
    z-index: 9999;
  }
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-icon {
  animation: rotate 2s linear infinite;
  color: #409eff;
}

.loading-text {
  color: #606266;
  font-size: 14px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
