#!/usr/bin/env node

/**
 * CORS测试脚本
 * 测试跨域请求配置
 */

const http = require('http');

/**
 * 发送HTTP请求
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    console.log('发送请求:', {
      hostname: options.hostname,
      port: options.port,
      path: options.path,
      method: options.method,
      headers: options.headers
    });
    
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsed
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * 测试CORS预检请求
 */
async function testCorsPreflightRequest() {
  console.log('🧪 测试CORS预检请求...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/v1/auth/login',
    method: 'OPTIONS',
    headers: {
      'Origin': 'http://localhost:5173',
      'Access-Control-Request-Method': 'POST',
      'Access-Control-Request-Headers': 'Content-Type,Authorization'
    }
  };

  try {
    const result = await makeRequest(options);
    
    console.log(`   状态码: ${result.statusCode}`);
    console.log(`   CORS头信息:`);
    console.log(`     Access-Control-Allow-Origin: ${result.headers['access-control-allow-origin']}`);
    console.log(`     Access-Control-Allow-Methods: ${result.headers['access-control-allow-methods']}`);
    console.log(`     Access-Control-Allow-Headers: ${result.headers['access-control-allow-headers']}`);
    
    if (result.statusCode === 200 || result.statusCode === 204) {
      console.log('✅ CORS预检请求成功');
    } else {
      console.log('❌ CORS预检请求失败');
    }
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
  }
  
  console.log('');
}

/**
 * 测试实际的登录请求
 */
async function testLoginRequest() {
  console.log('🧪 测试登录请求...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/v1/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Origin': 'http://localhost:5173'
    }
  };

  try {
    const result = await makeRequest(options, {
      username: 'admin',
      password: '123456'
    });
    
    console.log(`   状态码: ${result.statusCode}`);
    
    if (result.statusCode === 200) {
      console.log('✅ 登录请求成功');
      console.log(`   响应: ${result.data.message}`);
    } else {
      console.log('❌ 登录请求失败');
      console.log(`   错误: ${result.data.message || result.data}`);
    }
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
  }
  
  console.log('');
}

/**
 * 测试不同的Origin
 */
async function testDifferentOrigins() {
  console.log('🧪 测试不同的Origin...');
  
  const origins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:3000',
    'http://example.com', // 应该被拒绝
  ];
  
  for (const origin of origins) {
    console.log(`   测试Origin: ${origin}`);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': origin
      }
    };

    try {
      const result = await makeRequest(options, {
        username: 'admin',
        password: '123456'
      });
      
      if (result.statusCode === 200) {
        console.log(`     ✅ 允许访问`);
      } else {
        console.log(`     ❌ 拒绝访问 (${result.statusCode}): ${result.data.message || result.data}`);
      }
    } catch (error) {
      console.log(`     ❌ 请求失败: ${error.message}`);
    }
  }
  
  console.log('');
}

/**
 * 测试PATCH方法
 */
async function testPatchMethod() {
  console.log('🧪 测试PATCH方法...');
  
  // 先登录获取token
  const loginOptions = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/v1/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Origin': 'http://localhost:5173'
    }
  };

  try {
    const loginResult = await makeRequest(loginOptions, {
      username: 'admin',
      password: '123456'
    });
    
    if (loginResult.statusCode !== 200) {
      console.log('❌ 无法获取token，跳过PATCH测试');
      return;
    }
    
    const token = loginResult.data.data.token;
    
    // 测试PATCH请求
    const patchOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/permissions/test-id/status',
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:5173',
        'Authorization': `Bearer ${token}`
      }
    };

    const patchResult = await makeRequest(patchOptions, { status: 1 });
    
    console.log(`   PATCH请求状态码: ${patchResult.statusCode}`);
    
    if (patchResult.statusCode === 400 || patchResult.statusCode === 404) {
      console.log('✅ PATCH方法被允许（业务逻辑错误是正常的）');
    } else if (patchResult.statusCode === 405) {
      console.log('❌ PATCH方法不被允许');
    } else {
      console.log(`   响应: ${patchResult.data.message || patchResult.data}`);
    }
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
  }
  
  console.log('');
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始CORS测试...\n');
  
  // 检查后端服务
  try {
    const result = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/test',
      method: 'GET'
    });
    
    if (result.statusCode === 200) {
      console.log('✅ 后端服务正常运行\n');
    } else {
      console.log('❌ 后端服务异常');
      return;
    }
  } catch (error) {
    console.log('❌ 无法连接到后端服务');
    console.log('请确保后端服务已启动在 http://localhost:3000');
    return;
  }
  
  // 运行CORS测试
  await testCorsPreflightRequest();
  await testLoginRequest();
  await testDifferentOrigins();
  await testPatchMethod();
  
  console.log('🎉 CORS测试完成！');
  console.log('\n💡 建议：');
  console.log('1. 如果所有测试都通过，CORS配置正常');
  console.log('2. 如果有测试失败，请检查后端CORS配置');
  console.log('3. 重启后端服务以使配置更改生效');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}
