<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :fullscreen="fullscreen"
    :top="top"
    :modal="modal"
    :modal-class="modalClass"
    :append-to-body="appendToBody"
    :lock-scroll="lockScroll"
    :custom-class="customClass"
    :open-delay="openDelay"
    :close-delay="closeDelay"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :before-close="handleBeforeClose"
    :center="center"
    :align-center="alignCenter"
    :destroy-on-close="destroyOnClose"
    @open="handleOpen"
    @opened="handleOpened"
    @close="handleClose"
    @closed="handleClosed"
  >
    <!-- 头部插槽 -->
    <template v-if="$slots.header" #header>
      <slot name="header" />
    </template>

    <!-- 内容区域 -->
    <div class="dialog-content" :style="contentStyle">
      <slot />
    </div>

    <!-- 底部操作按钮 -->
    <template v-if="showFooter" #footer>
      <slot name="footer">
        <div class="dialog-footer">
          <el-button @click="handleCancel">
            {{ cancelText }}
          </el-button>
          <el-button
            type="primary"
            :loading="confirmLoading"
            @click="handleConfirm"
          >
            {{ confirmText }}
          </el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// Props
const props = defineProps({
  // 是否显示对话框
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 对话框标题
  title: {
    type: String,
    default: '',
  },
  // 对话框宽度
  width: {
    type: [String, Number],
    default: '50%',
  },
  // 是否全屏
  fullscreen: {
    type: Boolean,
    default: false,
  },
  // 对话框距离顶部的距离
  top: {
    type: String,
    default: '15vh',
  },
  // 是否需要遮罩层
  modal: {
    type: Boolean,
    default: true,
  },
  // 遮罩层的自定义类名
  modalClass: {
    type: String,
    default: '',
  },
  // 是否插入至 body 元素上
  appendToBody: {
    type: Boolean,
    default: false,
  },
  // 是否在 Dialog 出现时将 body 滚动锁定
  lockScroll: {
    type: Boolean,
    default: true,
  },
  // Dialog 的自定义类名
  customClass: {
    type: String,
    default: '',
  },
  // Dialog 打开的延时时间，单位毫秒
  openDelay: {
    type: Number,
    default: 0,
  },
  // Dialog 关闭的延时时间，单位毫秒
  closeDelay: {
    type: Number,
    default: 0,
  },
  // 是否可以通过点击 modal 关闭 Dialog
  closeOnClickModal: {
    type: Boolean,
    default: true,
  },
  // 是否可以通过按下 ESC 关闭 Dialog
  closeOnPressEscape: {
    type: Boolean,
    default: true,
  },
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: true,
  },
  // 关闭前的回调
  beforeClose: {
    type: Function,
    default: null,
  },
  // 是否对头部和底部采用居中布局
  center: {
    type: Boolean,
    default: false,
  },
  // 是否水平垂直对齐对话框
  alignCenter: {
    type: Boolean,
    default: false,
  },
  // 关闭时销毁 Dialog 中的元素
  destroyOnClose: {
    type: Boolean,
    default: false,
  },
  // 是否显示底部
  showFooter: {
    type: Boolean,
    default: true,
  },
  // 确认按钮文本
  confirmText: {
    type: String,
    default: '确定',
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '取消',
  },
  // 确认按钮加载状态
  confirmLoading: {
    type: Boolean,
    default: false,
  },
  // 内容区域最大高度
  maxHeight: {
    type: [String, Number],
    default: null,
  },
  // 内容区域最小高度
  minHeight: {
    type: [String, Number],
    default: null,
  },
});

// Emits
const emit = defineEmits([
  'update:modelValue',
  'open',
  'opened',
  'close',
  'closed',
  'confirm',
  'cancel',
]);

// 对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

// 内容区域样式
const contentStyle = computed(() => {
  const style = {};
  
  if (props.maxHeight) {
    style.maxHeight = typeof props.maxHeight === 'number' 
      ? `${props.maxHeight}px` 
      : props.maxHeight;
    style.overflowY = 'auto';
  }
  
  if (props.minHeight) {
    style.minHeight = typeof props.minHeight === 'number' 
      ? `${props.minHeight}px` 
      : props.minHeight;
  }
  
  return style;
});

// 关闭前处理
const handleBeforeClose = (done) => {
  if (props.beforeClose) {
    props.beforeClose(done);
  } else {
    done();
  }
};

// 事件处理
const handleOpen = () => {
  emit('open');
};

const handleOpened = () => {
  emit('opened');
};

const handleClose = () => {
  emit('close');
};

const handleClosed = () => {
  emit('closed');
};

const handleConfirm = () => {
  emit('confirm');
};

const handleCancel = () => {
  visible.value = false;
  emit('cancel');
};

// 暴露方法
defineExpose({
  visible,
  open: () => {
    visible.value = true;
  },
  close: () => {
    visible.value = false;
  },
});
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 0;
}

.dialog-footer {
  text-align: right;
  
  .el-button + .el-button {
    margin-left: 10px;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
