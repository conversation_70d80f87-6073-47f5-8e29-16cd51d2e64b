import axios from "axios";
import { ElMessage } from "element-plus";
import { getToken } from "@/utils/auth";
import { useUserStore } from "@/stores/user";

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API || "/api/v1",
  timeout: 10000,
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 调试日志 - 只记录状态更新请求
    if (config.url && config.url.includes("/status")) {
      console.log("请求拦截器 - 状态更新请求:", {
        url: config.url,
        method: config.method,
        data: config.data,
      });
    }

    const token = getToken();
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error("请求错误:", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data;
    return res;
  },
  (error) => {
    const userStore = useUserStore();

    if (error.response) {
      const { status } = error.response;

      switch (status) {
        case 401:
          ElMessage.error("登录已过期,请重新登录");
          userStore.logoutAction();
          break;
        case 403:
          ElMessage.error("没有权限访问该资源");
          break;
        case 404:
          ElMessage.error("请求的资源不存在");
          break;
        case 500:
          ElMessage.error("服务器错误");
          break;
        default:
          ElMessage.error(error.response.data?.message || "请求失败");
      }
    } else {
      ElMessage.error("网络错误,请稍后重试");
    }

    return Promise.reject(error);
  }
);

export default service;
