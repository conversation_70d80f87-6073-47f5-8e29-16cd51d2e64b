#!/usr/bin/env node

/**
 * 开发环境启动脚本
 * 同时启动前端和后端服务
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// 日志函数
function log(message, color = 'reset') {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

// 检查目录是否存在
function checkDirectory(dir, name) {
  if (!fs.existsSync(dir)) {
    log(`❌ ${name}目录不存在: ${dir}`, 'red');
    return false;
  }
  return true;
}

// 检查package.json是否存在
function checkPackageJson(dir, name) {
  const packagePath = path.join(dir, 'package.json');
  if (!fs.existsSync(packagePath)) {
    log(`❌ ${name}的package.json不存在: ${packagePath}`, 'red');
    return false;
  }
  return true;
}

// 启动服务
function startService(name, command, args, cwd, color) {
  return new Promise((resolve, reject) => {
    log(`🚀 启动${name}服务...`, color);
    
    const child = spawn(command, args, {
      cwd,
      stdio: 'pipe',
      shell: true
    });

    // 处理输出
    child.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        log(`[${name}] ${output}`, color);
      }
    });

    child.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        log(`[${name}] ${output}`, 'red');
      }
    });

    child.on('close', (code) => {
      if (code === 0) {
        log(`✅ ${name}服务启动成功`, 'green');
        resolve();
      } else {
        log(`❌ ${name}服务启动失败，退出码: ${code}`, 'red');
        reject(new Error(`${name}服务启动失败`));
      }
    });

    child.on('error', (err) => {
      log(`❌ ${name}服务启动错误: ${err.message}`, 'red');
      reject(err);
    });

    // 返回子进程，以便后续管理
    return child;
  });
}

// 等待服务启动
function waitForService(url, timeout = 30000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    function check() {
      const http = require('http');
      const urlParts = new URL(url);
      
      const req = http.get({
        hostname: urlParts.hostname,
        port: urlParts.port,
        path: urlParts.pathname,
        timeout: 2000
      }, (res) => {
        if (res.statusCode === 200) {
          resolve();
        } else {
          setTimeout(check, 1000);
        }
      });
      
      req.on('error', () => {
        if (Date.now() - startTime > timeout) {
          reject(new Error('服务启动超时'));
        } else {
          setTimeout(check, 1000);
        }
      });
      
      req.on('timeout', () => {
        req.destroy();
        if (Date.now() - startTime > timeout) {
          reject(new Error('服务启动超时'));
        } else {
          setTimeout(check, 1000);
        }
      });
    }
    
    check();
  });
}

// 主函数
async function main() {
  log('🎯 开始启动开发环境...', 'bright');
  
  const rootDir = path.join(__dirname, '..');
  const backendDir = path.join(rootDir, 'backend');
  const frontendDir = path.join(rootDir, 'frontend');
  
  // 检查目录
  if (!checkDirectory(backendDir, '后端') || !checkDirectory(frontendDir, '前端')) {
    process.exit(1);
  }
  
  // 检查package.json
  if (!checkPackageJson(backendDir, '后端') || !checkPackageJson(frontendDir, '前端')) {
    process.exit(1);
  }
  
  const processes = [];
  
  try {
    // 启动后端服务
    log('📦 启动后端服务...', 'blue');
    const backendProcess = spawn('npm', ['run', 'dev'], {
      cwd: backendDir,
      stdio: 'pipe',
      shell: true
    });
    
    backendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        log(`[后端] ${output}`, 'blue');
      }
    });
    
    backendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        log(`[后端] ${output}`, 'red');
      }
    });
    
    processes.push(backendProcess);
    
    // 等待后端服务启动
    log('⏳ 等待后端服务启动...', 'yellow');
    await waitForService('http://localhost:3000/api/v1/test');
    log('✅ 后端服务启动成功', 'green');
    
    // 启动前端服务
    log('🎨 启动前端服务...', 'cyan');
    const frontendProcess = spawn('npm', ['run', 'dev'], {
      cwd: frontendDir,
      stdio: 'pipe',
      shell: true
    });
    
    frontendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        log(`[前端] ${output}`, 'cyan');
      }
    });
    
    frontendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        log(`[前端] ${output}`, 'red');
      }
    });
    
    processes.push(frontendProcess);
    
    // 等待前端服务启动
    log('⏳ 等待前端服务启动...', 'yellow');
    await waitForService('http://localhost:5173');
    log('✅ 前端服务启动成功', 'green');
    
    log('🎉 开发环境启动完成！', 'bright');
    log('📱 前端地址: http://localhost:5173', 'green');
    log('🔧 后端地址: http://localhost:3000', 'green');
    log('📚 API文档: http://localhost:3000/api-docs', 'green');
    log('', 'reset');
    log('按 Ctrl+C 停止所有服务', 'yellow');
    
  } catch (error) {
    log(`❌ 启动失败: ${error.message}`, 'red');
    
    // 清理进程
    processes.forEach(proc => {
      if (proc && !proc.killed) {
        proc.kill();
      }
    });
    
    process.exit(1);
  }
  
  // 处理退出信号
  process.on('SIGINT', () => {
    log('\n🛑 正在停止所有服务...', 'yellow');
    
    processes.forEach(proc => {
      if (proc && !proc.killed) {
        proc.kill();
      }
    });
    
    log('👋 所有服务已停止', 'green');
    process.exit(0);
  });
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}
