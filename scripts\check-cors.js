#!/usr/bin/env node

/**
 * 跨域问题诊断脚本
 */

const http = require('http');
const https = require('https');
const url = require('url');

// 测试配置
const tests = [
  {
    name: '后端健康检查',
    url: 'http://localhost:3000/api/v1/test',
    method: 'GET'
  },
  {
    name: '后端CORS预检请求',
    url: 'http://localhost:3000/api/v1/test',
    method: 'OPTIONS',
    headers: {
      'Origin': 'http://localhost:5173',
      'Access-Control-Request-Method': 'GET',
      'Access-Control-Request-Headers': 'Content-Type,Authorization'
    }
  },
  {
    name: '后端API文档',
    url: 'http://localhost:3000/api-docs',
    method: 'GET'
  }
];

/**
 * 发送HTTP请求
 */
function makeRequest(testConfig) {
  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(testConfig.url);
    const isHttps = parsedUrl.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (isHttps ? 443 : 80),
      path: parsedUrl.path,
      method: testConfig.method || 'GET',
      headers: {
        'User-Agent': 'CORS-Checker/1.0',
        ...testConfig.headers
      },
      timeout: 5000
    };

    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data.substring(0, 200) // 只保留前200个字符
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.end();
  });
}

/**
 * 检查CORS头
 */
function checkCorsHeaders(headers, origin = 'http://localhost:5173') {
  const corsHeaders = {
    'access-control-allow-origin': headers['access-control-allow-origin'],
    'access-control-allow-methods': headers['access-control-allow-methods'],
    'access-control-allow-headers': headers['access-control-allow-headers'],
    'access-control-allow-credentials': headers['access-control-allow-credentials'],
    'access-control-max-age': headers['access-control-max-age']
  };

  const issues = [];
  
  // 检查 Access-Control-Allow-Origin
  if (!corsHeaders['access-control-allow-origin']) {
    issues.push('缺少 Access-Control-Allow-Origin 头');
  } else if (corsHeaders['access-control-allow-origin'] !== '*' && 
             corsHeaders['access-control-allow-origin'] !== origin) {
    issues.push(`Access-Control-Allow-Origin 不匹配: ${corsHeaders['access-control-allow-origin']} vs ${origin}`);
  }

  // 检查 Access-Control-Allow-Methods
  if (!corsHeaders['access-control-allow-methods']) {
    issues.push('缺少 Access-Control-Allow-Methods 头');
  }

  // 检查 Access-Control-Allow-Headers
  if (!corsHeaders['access-control-allow-headers']) {
    issues.push('缺少 Access-Control-Allow-Headers 头');
  }

  return { corsHeaders, issues };
}

/**
 * 运行单个测试
 */
async function runTest(testConfig) {
  console.log(`\n🧪 测试: ${testConfig.name}`);
  console.log(`   URL: ${testConfig.url}`);
  console.log(`   方法: ${testConfig.method}`);
  
  if (testConfig.headers) {
    console.log(`   请求头:`, testConfig.headers);
  }

  try {
    const result = await makeRequest(testConfig);
    
    console.log(`   ✅ 状态码: ${result.statusCode}`);
    
    // 检查CORS头（仅对OPTIONS请求）
    if (testConfig.method === 'OPTIONS') {
      const { corsHeaders, issues } = checkCorsHeaders(result.headers, testConfig.headers?.Origin);
      
      console.log(`   CORS头:`);
      Object.entries(corsHeaders).forEach(([key, value]) => {
        if (value) {
          console.log(`     ${key}: ${value}`);
        }
      });
      
      if (issues.length > 0) {
        console.log(`   ⚠️  CORS问题:`);
        issues.forEach(issue => console.log(`     - ${issue}`));
      } else {
        console.log(`   ✅ CORS配置正常`);
      }
    }
    
    // 显示响应数据（如果有）
    if (result.data && result.data.trim()) {
      console.log(`   响应数据: ${result.data.substring(0, 100)}...`);
    }
    
    return { success: true, result };
    
  } catch (error) {
    console.log(`   ❌ 错误: ${error.message}`);
    return { success: false, error };
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🔍 开始跨域问题诊断...\n');
  
  let successCount = 0;
  let failCount = 0;
  
  for (const test of tests) {
    const result = await runTest(test);
    if (result.success) {
      successCount++;
    } else {
      failCount++;
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 诊断结果总结:');
  console.log(`   成功: ${successCount}`);
  console.log(`   失败: ${failCount}`);
  console.log(`   总计: ${tests.length}`);
  
  if (failCount > 0) {
    console.log('\n💡 解决建议:');
    console.log('   1. 确保后端服务已启动 (npm run dev)');
    console.log('   2. 检查后端CORS配置');
    console.log('   3. 确认前端代理配置正确');
    console.log('   4. 检查防火墙设置');
  } else {
    console.log('\n✅ 所有测试通过！跨域配置正常。');
  }
}

// 运行诊断
if (require.main === module) {
  main().catch(console.error);
}
