const express = require("express");
const router = express.Router();

// 导入各个模块的路由
const authRoutes = require("./authRoutes");
const userRoutes = require("./userRoutes");
const roleRoutes = require("./roleRoutes");
const permissionRoutes = require("./permissionRoutes");
const departmentRoutes = require("./departmentRoutes");
const systemRoutes = require("./system");
const uploadRoutes = require("./upload");

// API 路由前缀
const API_PREFIX = "/api/v1";

// 注册各个模块的路由
router.use(`${API_PREFIX}/auth`, authRoutes);
router.use(`${API_PREFIX}/users`, userRoutes);
router.use(`${API_PREFIX}/roles`, roleRoutes);
router.use(`${API_PREFIX}/permissions`, permissionRoutes);
router.use(`${API_PREFIX}/departments`, departmentRoutes);
router.use(`${API_PREFIX}/system`, systemRoutes);
router.use(`${API_PREFIX}/upload`, uploadRoutes);

// 处理 404 路由
router.use("*", (req, res) => {
  res.status(404).json({
    code: 404,
    success: false,
    message: "请求的资源不存在",
    data: null,
  });
});

module.exports = router;
