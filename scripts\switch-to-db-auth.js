#!/usr/bin/env node

/**
 * 切换到数据库认证脚本
 * 禁用测试用户，启用真实数据库认证
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 切换到数据库认证模式...\n');

// 修改后端认证控制器
const authControllerPath = path.join(__dirname, '../backend/controllers/authController.js');

try {
  let authContent = fs.readFileSync(authControllerPath, 'utf8');
  
  // 注释掉测试用户登录逻辑
  authContent = authContent.replace(
    /\/\/ 测试用户 - 不需要数据库[\s\S]*?return success\(res, "登录成功", \{[\s\S]*?\}\);/,
    `// 测试用户已禁用 - 使用数据库认证
    // if (username === "admin" && password === "123456") {
    //   // 测试用户逻辑已禁用
    // }`
  );
  
  // 注释掉测试用户信息获取逻辑
  authContent = authContent.replace(
    /\/\/ 测试用户处理[\s\S]*?return success\(res, "获取用户信息成功", testUser\);/,
    `// 测试用户已禁用 - 使用数据库认证
    // if (req.user && req.user._id === "test-user-id") {
    //   // 测试用户逻辑已禁用
    // }`
  );
  
  fs.writeFileSync(authControllerPath, authContent);
  console.log('✅ 已修改认证控制器，禁用测试用户');
} catch (error) {
  console.log('❌ 修改认证控制器失败:', error.message);
}

// 修改认证中间件
const authMiddlewarePath = path.join(__dirname, '../backend/middleware/auth.js');

try {
  let middlewareContent = fs.readFileSync(authMiddlewarePath, 'utf8');
  
  // 注释掉测试用户处理逻辑
  middlewareContent = middlewareContent.replace(
    /\/\/ 测试用户处理[\s\S]*?return next\(\);/,
    `// 测试用户已禁用 - 使用数据库认证
    // if (decoded.id === "test-user-id") {
    //   // 测试用户逻辑已禁用
    // }`
  );
  
  middlewareContent = middlewareContent.replace(
    /\/\/ 测试用户处理 - 超级管理员拥有所有权限[\s\S]*?return next\(\);/,
    `// 测试用户已禁用 - 使用数据库认证
    // if (req.user._id === "test-user-id") {
    //   // 测试用户逻辑已禁用
    // }`
  );
  
  fs.writeFileSync(authMiddlewarePath, middlewareContent);
  console.log('✅ 已修改认证中间件，禁用测试用户');
} catch (error) {
  console.log('❌ 修改认证中间件失败:', error.message);
}

console.log('\n📋 切换完成！现在系统将使用数据库认证。');
console.log('\n🔑 请使用以下账号登录：');
console.log('用户名: admin');
console.log('密码: 123456');
console.log('\n⚠️  注意：');
console.log('1. 确保数据库中有对应的用户数据');
console.log('2. 如果没有用户数据，请运行: npm run init-db');
console.log('3. 重启后端服务以使更改生效');

console.log('\n🔄 如需恢复测试用户模式，请手动修改相关文件或重新部署。');
