<template>
    <div class="error-page">
        <div class="error-content">
            <h1 class="error-code">401</h1>
            <h2 class="error-title">未授权访问</h2>
            <div class="error-desc">抱歉，您没有权限访问此页面</div>
            <el-button type="primary" @click="goHome">返回首页</el-button>
        </div>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
    router.push('/');
};
</script>

<style scoped>
.error-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    z-index: 9999;
}

.error-content {
    text-align: center;
    padding: 30px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 90%;
}

.error-code {
    font-size: 120px;
    color: #f56c6c;
    margin: 0;
    line-height: 1.2;
}

.error-title {
    font-size: 28px;
    color: #303133;
    margin: 20px 0;
}

.error-desc {
    font-size: 16px;
    color: #606266;
    margin-bottom: 30px;
}
</style>