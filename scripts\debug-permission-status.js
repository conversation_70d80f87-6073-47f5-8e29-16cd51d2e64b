#!/usr/bin/env node

/**
 * 权限状态更新调试脚本
 * 用于调试权限状态更新的数据格式问题
 */

const http = require("http");

/**
 * 发送HTTP请求
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    console.log("发送请求:", {
      hostname: options.hostname,
      port: options.port,
      path: options.path,
      method: options.method,
      headers: options.headers,
      data: data,
    });

    const req = http.request(options, (res) => {
      let responseData = "";

      res.on("data", (chunk) => {
        responseData += chunk;
      });

      res.on("end", () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsed,
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData,
          });
        }
      });
    });

    req.on("error", (err) => {
      reject(err);
    });

    if (data) {
      const jsonData = JSON.stringify(data);
      console.log("发送的JSON数据:", jsonData);
      req.write(jsonData);
    }

    req.end();
  });
}

/**
 * 获取认证token
 */
async function getAuthToken() {
  console.log("🔐 获取认证token...");

  const options = {
    hostname: "localhost",
    port: 3000,
    path: "/api/v1/auth/login",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
  };

  try {
    const result = await makeRequest(options, {
      username: "admin",
      password: "123456",
    });

    if (result.statusCode === 200 && result.data.success) {
      console.log("✅ 登录成功");
      return result.data.data.token;
    } else {
      console.log("❌ 登录失败");
      return null;
    }
  } catch (error) {
    console.log(`❌ 登录请求失败: ${error.message}`);
    return null;
  }
}

/**
 * 测试不同的数据格式
 */
async function testDifferentDataFormats(token, permissionId) {
  console.log(`\n🧪 测试不同的数据格式 (权限ID: ${permissionId})...`);

  const testCases = [
    {
      name: "正确格式 - 数字1",
      data: { status: 1 },
    },
    {
      name: "正确格式 - 数字0",
      data: { status: 0 },
    },
    {
      name: '错误格式 - 字符串"1"',
      data: { status: "1" },
    },
    {
      name: '错误格式 - 字符串"0"',
      data: { status: "0" },
    },
    {
      name: "错误格式 - 嵌套对象",
      data: { status: { status: 1 } },
    },
    {
      name: "错误格式 - 嵌套字符串",
      data: { status: { status: "1" } },
    },
  ];

  for (const testCase of testCases) {
    console.log(`\n   测试: ${testCase.name}`);

    const options = {
      hostname: "localhost",
      port: 3000,
      path: `/api/v1/permissions/${permissionId}/status`,
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    try {
      const result = await makeRequest(options, testCase.data);

      if (result.statusCode === 200) {
        console.log(`     ✅ 成功 - ${result.data.message}`);
      } else {
        console.log(
          `     ❌ 失败 (${result.statusCode}) - ${result.data.message}`
        );
        if (result.data.errors) {
          console.log(`     错误详情:`, result.data.errors);
        }
      }
    } catch (error) {
      console.log(`     ❌ 请求失败: ${error.message}`);
    }
  }
}

/**
 * 测试前端代理
 */
async function testFrontendProxy(permissionId) {
  console.log(`\n🧪 测试前端代理 (权限ID: ${permissionId})...`);

  const options = {
    hostname: "localhost",
    port: 5173, // 前端端口
    path: `/api/v1/permissions/${permissionId}/status`,
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      Origin: "http://localhost:5173",
    },
  };

  try {
    const result = await makeRequest(options, { status: 1 });

    if (result.statusCode === 200) {
      console.log(`   ✅ 前端代理成功`);
    } else {
      console.log(`   ❌ 前端代理失败 (${result.statusCode})`);
      console.log(`   响应:`, result.data);
    }
  } catch (error) {
    console.log(`   ❌ 前端代理请求失败: ${error.message}`);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log("🚀 开始权限状态更新调试...\n");

  // 获取认证token
  const token = await getAuthToken();
  if (!token) {
    console.log("❌ 无法获取认证token，测试终止");
    return;
  }

  // 使用一个已知的权限ID进行测试
  const permissionId = "68635ae95af3638ae7ffe7bb";

  // 测试不同的数据格式
  await testDifferentDataFormats(token, permissionId);

  // 测试前端代理
  await testFrontendProxy(permissionId);

  console.log("\n🎉 调试完成！");
  console.log("\n💡 分析结果：");
  console.log("1. 检查哪种数据格式能成功");
  console.log("2. 对比前端实际发送的数据格式");
  console.log("3. 确认数据类型转换是否正确");
}

// 运行调试
if (require.main === module) {
  main().catch(console.error);
}
