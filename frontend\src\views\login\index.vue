<template>
    <div class="login-container">
        <div class="login-box">
            <div class="login-form-container">
                <div class="title-container">
                    <h2 class="title">后台管理系统</h2>
                    <h3 class="subtitle">欢迎回来！请登录您的账号</h3>
                </div>

                <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
                    <el-form-item prop="username">
                        <el-input v-model="loginForm.username" placeholder="请输入用户名" :prefix-icon="User" size="large"
                            clearable />
                    </el-form-item>

                    <el-form-item prop="password">
                        <el-input v-model="loginForm.password" :type="passwordVisible ? 'text' : 'password'"
                            placeholder="请输入密码" :prefix-icon="Lock" size="large" clearable show-password
                            @keyup.enter="handleLogin" />
                    </el-form-item>

                    <div class="remember-forgot">
                        <el-checkbox v-model="rememberMe">记住我</el-checkbox>
                        <a href="javascript:;" class="forgot-link">忘记密码？</a>
                    </div>

                    <el-button :loading="loading" type="primary" class="login-button" size="large" @click="handleLogin">
                        {{ loading ? '登录中...' : '登录' }}
                    </el-button>

                    <div class="demo-account">
                        <p class="demo-title">演示账号</p>
                        <div class="account-info">
                            <span>用户名：admin</span>
                            <span>密码：123456</span>
                        </div>
                    </div>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { User, Lock } from '@element-plus/icons-vue';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

const loginFormRef = ref(null);
const loading = ref(false);
const rememberMe = ref(false);
const passwordVisible = ref(false);

const loginForm = reactive({
    username: 'admin',
    password: '123456'
});

const loginRules = {
    username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, message: '用户名长度不能小于3位', trigger: 'blur' }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
    ]
};

const handleLogin = async () => {
    try {
        await loginFormRef.value.validate();

        loading.value = true;
        await userStore.loginAction(loginForm);

        ElMessage.success('登录成功');

        const redirect = route.query.redirect || '/dashboard';
        router.replace(redirect);
    } catch (error) {
        console.error('登录失败:', error);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.login-container {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;

    .login-box {
        width: 460px;
        padding: 40px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

        .login-form-container {
            .title-container {
                text-align: center;
                margin-bottom: 40px;

                .title {
                    font-size: 28px;
                    color: #333;
                    margin-bottom: 10px;
                    font-weight: 600;
                }

                .subtitle {
                    font-size: 16px;
                    color: #666;
                    font-weight: normal;
                }
            }

            .login-form {
                :deep(.el-input) {
                    .el-input__wrapper {
                        background-color: #f5f7fa;
                        border: 1px solid #e4e7ed;
                        border-radius: 8px;
                        padding: 0 15px;
                        height: 48px;
                        transition: all 0.3s;

                        &:hover,
                        &.is-focus {
                            border-color: #409eff;
                            background-color: #fff;
                        }
                    }

                    .el-input__inner {
                        height: 48px;
                        font-size: 16px;
                    }
                }

                .remember-forgot {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 20px 0;

                    .forgot-link {
                        color: #409eff;
                        text-decoration: none;
                        font-size: 14px;

                        &:hover {
                            color: #66b1ff;
                        }
                    }
                }

                .login-button {
                    width: 100%;
                    height: 48px;
                    font-size: 16px;
                    font-weight: 500;
                    margin-bottom: 20px;
                    border-radius: 8px;
                    background: linear-gradient(to right, #409eff, #66b1ff);
                    border: none;

                    &:hover {
                        background: linear-gradient(to right, #66b1ff, #409eff);
                    }
                }

                .demo-account {
                    text-align: center;
                    padding: 20px;
                    background-color: #f5f7fa;
                    border-radius: 8px;
                    margin-top: 20px;

                    .demo-title {
                        color: #666;
                        font-size: 14px;
                        margin-bottom: 10px;
                    }

                    .account-info {
                        display: flex;
                        justify-content: space-around;
                        color: #409eff;
                        font-size: 14px;
                    }
                }
            }
        }
    }
}

// 响应式设计
@media screen and (max-width: 576px) {
    .login-container {
        .login-box {
            width: 90%;
            padding: 20px;
        }
    }
}
</style>