import request from "@/utils/request";

// 获取部门列表
export function getDepartmentList(params) {
  return request({
    url: "/departments",
    method: "get",
    params,
  });
}

// 获取部门树结构
export function getDepartmentTree() {
  return request({
    url: "/departments/tree",
    method: "get",
  });
}

// 获取单个部门
export function getDepartmentById(id) {
  return request({
    url: `/departments/${id}`,
    method: "get",
  });
}

// 创建部门
export function createDepartment(data) {
  return request({
    url: "/departments",
    method: "post",
    data,
  });
}

// 更新部门
export function updateDepartment(id, data) {
  return request({
    url: `/departments/${id}`,
    method: "put",
    data,
  });
}

// 删除部门
export function deleteDepartment(id) {
  return request({
    url: `/departments/${id}`,
    method: "delete",
  });
}

// 批量删除部门
export function batchDeleteDepartments(ids) {
  return request({
    url: "/departments/batch",
    method: "delete",
    data: { ids },
  });
}

// 更新部门状态
export function updateDepartmentStatus(id, status) {
  return request({
    url: `/departments/${id}/status`,
    method: "patch",
    data: { status },
  });
}
