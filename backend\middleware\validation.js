const { body, param, query, validationResult } = require("express-validator");

// 验证结果处理中间件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      code: 400,
      success: false,
      message: "数据验证失败",
      errors: errors.array().map((error) => ({
        field: error.path,
        message: error.msg,
        value: error.value,
      })),
    });
  }
  next();
};

// 用户验证规则
const userValidation = {
  create: [
    body("username")
      .isLength({ min: 3, max: 20 })
      .withMessage("用户名长度必须在3-20个字符之间")
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage("用户名只能包含字母、数字和下划线"),
    body("password")
      .isLength({ min: 6, max: 20 })
      .withMessage("密码长度必须在6-20个字符之间")
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/)
      .withMessage("密码必须包含至少一个大写字母、一个小写字母和一个数字"),
    body("email").optional().isEmail().withMessage("邮箱格式不正确"),
    body("phone")
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage("手机号格式不正确"),
    body("nickname")
      .optional()
      .isLength({ min: 1, max: 50 })
      .withMessage("昵称长度不能超过50个字符"),
  ],

  update: [
    param("id").isMongoId().withMessage("用户ID格式不正确"),
    body("username")
      .optional()
      .isLength({ min: 3, max: 20 })
      .withMessage("用户名长度必须在3-20个字符之间")
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage("用户名只能包含字母、数字和下划线"),
    body("email").optional().isEmail().withMessage("邮箱格式不正确"),
    body("phone")
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage("手机号格式不正确"),
    body("nickname")
      .optional()
      .isLength({ min: 1, max: 50 })
      .withMessage("昵称长度不能超过50个字符"),
  ],

  changePassword: [
    body("oldPassword").notEmpty().withMessage("原密码不能为空"),
    body("newPassword")
      .isLength({ min: 6, max: 20 })
      .withMessage("新密码长度必须在6-20个字符之间")
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/)
      .withMessage("新密码必须包含至少一个大写字母、一个小写字母和一个数字"),
    body("confirmPassword").custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error("确认密码与新密码不匹配");
      }
      return true;
    }),
  ],
};

// 角色验证规则
const roleValidation = {
  create: [
    body("name")
      .isLength({ min: 2, max: 50 })
      .withMessage("角色名称长度必须在2-50个字符之间"),
    body("code")
      .isLength({ min: 2, max: 50 })
      .withMessage("角色编码长度必须在2-50个字符之间")
      .matches(/^[a-zA-Z0-9_:]+$/)
      .withMessage("角色编码只能包含字母、数字、下划线和冒号"),
    body("permissions").optional().isArray().withMessage("权限必须是数组格式"),
    body("permissions.*")
      .optional()
      .isMongoId()
      .withMessage("权限ID格式不正确"),
  ],

  update: [
    param("id").isMongoId().withMessage("角色ID格式不正确"),
    body("name")
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage("角色名称长度必须在2-50个字符之间"),
    body("code")
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage("角色编码长度必须在2-50个字符之间")
      .matches(/^[a-zA-Z0-9_:]+$/)
      .withMessage("角色编码只能包含字母、数字、下划线和冒号"),
    body("permissions").optional().isArray().withMessage("权限必须是数组格式"),
    body("permissions.*")
      .optional()
      .isMongoId()
      .withMessage("权限ID格式不正确"),
  ],
};

// 部门验证规则
const departmentValidation = {
  create: [
    body("name")
      .isLength({ min: 2, max: 50 })
      .withMessage("部门名称长度必须在2-50个字符之间"),
    body("parentId").optional().isMongoId().withMessage("父部门ID格式不正确"),
    body("leader")
      .optional()
      .isLength({ min: 1, max: 20 })
      .withMessage("负责人姓名长度不能超过20个字符"),
    body("phone")
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage("联系电话格式不正确"),
    body("email").optional().isEmail().withMessage("邮箱格式不正确"),
  ],

  update: [
    param("id").isMongoId().withMessage("部门ID格式不正确"),
    body("name")
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage("部门名称长度必须在2-50个字符之间"),
    body("parentId").optional().isMongoId().withMessage("父部门ID格式不正确"),
    body("leader")
      .optional()
      .isLength({ min: 1, max: 20 })
      .withMessage("负责人姓名长度不能超过20个字符"),
    body("phone")
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage("联系电话格式不正确"),
    body("email").optional().isEmail().withMessage("邮箱格式不正确"),
  ],
};

// 权限验证规则
const permissionValidation = {
  create: [
    body("name")
      .isLength({ min: 2, max: 50 })
      .withMessage("权限名称长度必须在2-50个字符之间"),
    body("code")
      .isLength({ min: 2, max: 100 })
      .withMessage("权限编码长度必须在2-100个字符之间")
      .matches(/^[a-zA-Z0-9_:]+$/)
      .withMessage("权限编码只能包含字母、数字、下划线和冒号"),
    body("type")
      .isIn([1, 2, 3])
      .withMessage("权限类型必须是1(菜单)、2(按钮)或3(API)"),
    body("parentId").optional().isMongoId().withMessage("父权限ID格式不正确"),
  ],

  update: [
    param("id").isMongoId().withMessage("权限ID格式不正确"),
    body("name")
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage("权限名称长度必须在2-50个字符之间"),
    body("code")
      .optional()
      .isLength({ min: 2, max: 100 })
      .withMessage("权限编码长度必须在2-100个字符之间")
      .matches(/^[a-zA-Z0-9_:]+$/)
      .withMessage("权限编码只能包含字母、数字、下划线和冒号"),
    body("type")
      .optional()
      .isIn([1, 2, 3])
      .withMessage("权限类型必须是1(菜单)、2(按钮)或3(API)"),
    body("parentId").optional().isMongoId().withMessage("父权限ID格式不正确"),
  ],
};

// 登录验证规则
const loginValidation = [
  body("username")
    .notEmpty()
    .withMessage("用户名不能为空")
    .isLength({ min: 3, max: 20 })
    .withMessage("用户名长度必须在3-20个字符之间"),
  body("password")
    .notEmpty()
    .withMessage("密码不能为空")
    .isLength({ min: 6, max: 20 })
    .withMessage("密码长度必须在6-20个字符之间"),
];

// 分页验证规则
const paginationValidation = [
  query("page")
    .optional()
    .isInt({ min: 1 })
    .withMessage("页码必须是大于0的整数"),
  query("limit")
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage("每页数量必须是1-100之间的整数"),
];

// ID验证规则
const idValidation = [param("id").isMongoId().withMessage("ID格式不正确")];

// 状态验证规则
const statusValidation = [
  body("status").isIn([0, 1]).withMessage("状态值必须是0(禁用)或1(启用)"),
];

module.exports = {
  handleValidationErrors,
  userValidation,
  roleValidation,
  departmentValidation,
  permissionValidation,
  loginValidation,
  paginationValidation,
  idValidation,
  statusValidation,
};
