<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <RuoForm v-model="queryParams" :form-items="searchFormItems" :show-actions="true" :gutter="20" :default-span="6"
        label-width="80px" @submit="handleQuery" @reset="resetQuery">
        <!-- 自定义日期范围选择器 -->
        <template #dateRange="{ item, formData }">
          <el-date-picker v-model="formData[item.prop]" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :clearable="true" />
        </template>

        <!-- 自定义用户状态选择器 -->
        <template #userStatus="{ item, formData }">
          <el-select v-model="formData[item.prop]" placeholder="请选择用户状态" clearable multiple collapse-tags
            style="width: 100%">
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
            <el-option label="待审核" :value="2" />
          </el-select>
        </template>

        <!-- 自定义操作按钮 -->
        <template #actions>
          <el-button type="primary" :icon="Search" @click="handleQuery">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="resetQuery">
            重置
          </el-button>
          <el-button type="success" @click="handleExport" :loading="exportLoading">
            <el-icon>
              <Download />
            </el-icon>
            导出
          </el-button>
        </template>
      </RuoForm>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="18">
        <!-- 表格区域 -->
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <span>用户列表</span>
              <div class="header-actions">
                <el-button type="success" size="small" @click="handleExport" :loading="exportLoading">
                  <el-icon>
                    <Download />
                  </el-icon>
                  导出
                </el-button>
                <el-dropdown @command="handleBatchAction" v-if="selectedIds.length > 0">
                  <el-button type="warning" size="small">
                    批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="enable">批量启用</el-dropdown-item>
                      <el-dropdown-item command="disable">批量禁用</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>批量删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </template>

          <!-- 使用原生el-table，直接写列定义 -->
          <el-table :data="userList" :loading="loading" row-key="_id" border stripe>
            <el-table-column type="selection" width="55" />

            <template v-for="column in tableColumns" :key="column.prop">
              <el-table-column :prop="column.slot || column.type ? undefined : column.prop" :label="column.label"
                :width="column.width" :min-width="column.minWidth" :align="column.align || 'left'"
                :show-overflow-tooltip="column.showOverflowTooltip !== false">
                <template #default="scope" v-if="column.slot || column.type">
                  <!-- 部门列 -->
                  <span v-if="column.slot === 'dept'">{{ scope.row.deptId?.name || "-" }}</span>
                  <!-- 角色列 -->
                  <div v-else-if="column.slot === 'roles'" class="role-tags">
                    <el-tag v-for="role in scope.row.roles" :key="role._id" class="role-tag">
                      {{ role.name }}
                    </el-tag>
                    <span v-if="!scope.row.roles || scope.row.roles.length === 0">-</span>
                  </div>
                  <!-- 状态列 -->
                  <div v-else-if="column.slot === 'status'">
                    {{ scope.row.status }}
                  </div>

                  <!-- 日期列 -->
                  <span v-else-if="column.type === 'date'">
                    {{ new Date(scope.row[column.prop]).toLocaleString() }}
                  </span>
                </template>
              </el-table-column>
            </template>

            <!-- 操作列 -->
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="info" size="small" link @click="handleViewDetail(row)">详情</el-button>
                <el-button type="primary" size="small" link @click="handleEdit(row)">编辑</el-button>
                <el-button type="warning" size="small" link @click="handleResetPwd(row)">重置密码</el-button>
                <el-button type="danger" size="small" link @click="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页区域 -->
          <div class="pagination-container">
            <el-pagination :current-page="queryParams.page" :page-size="queryParams.pageSize"
              :page-sizes="[10, 20, 50, 100]" :total="total" :background="true"
              layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </el-card>
      </el-col>

      <!-- 统计面板 -->
      <el-col :span="6">
        <UserStats />
      </el-col>
    </el-row>

    <!-- 用户详情抽屉 -->
    <UserDetail v-model="detailVisible" :user-id="currentUserId" @edit="handleEditFromDetail" @refresh="getList" />

    <!-- 添加/编辑用户对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="600px" append-to-body destroy-on-close>
      <el-form ref="userFormRef" :model="userForm" :rules="userFormRules" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" :disabled="userForm._id" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!userForm._id">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" show-password />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="userForm.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="部门" prop="deptId">
          <el-select v-model="userForm.deptId" placeholder="请选择部门" style="width: 100%">
            <el-option v-for="dept in deptOptions" :key="dept._id" :label="dept.name" :value="dept._id" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色" prop="roles">
          <el-select v-model="userForm.roles" multiple placeholder="请选择角色" style="width: 100%">
            <el-option v-for="role in roleOptions" :key="role._id" :label="role.name" :value="role._id" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="userForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog title="重置密码" v-model="resetPwdDialogVisible" width="500px" append-to-body destroy-on-close>
      <el-form ref="resetPwdFormRef" :model="resetPwdForm" :rules="resetPwdFormRules" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="resetPwdForm.username" disabled />
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input v-model="resetPwdForm.password" type="password" placeholder="请输入新密码" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="resetPwdForm.confirmPassword" type="password" placeholder="请再次输入新密码" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetPwdDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitResetPwd">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Refresh, Download, ArrowDown } from "@element-plus/icons-vue";
import {
  getUserList,
  createUser,
  updateUser,
  deleteUser,
  batchDeleteUsers,
  updateUserStatus,
  resetPassword,
  exportUserData,
  batchUpdateUserStatus,
} from "@/api/user";
import { getDepartmentList } from "@/api/department";
import { getAllRoles } from "@/api/role";
import UserDetail from "./UserDetail.vue";
import UserStats from "./UserStats.vue";
import RuoTable from "@/components/Table/index.vue";
import RuoForm from "@/components/Form/index.vue";

// 查询参数
const queryParams = reactive({
  page: 1,
  pageSize: 10,
  username: "",
  status: "",
  deptId: "",
});

// 搜索表单配置
const searchFormItems = computed(() => [
  {
    prop: "username",
    label: "用户名",
    type: "input",
    placeholder: "请输入用户名",
    clearable: true,
  },
  {
    prop: "status",
    label: "状态",
    type: "select",
    placeholder: "请选择状态",
    clearable: true,
    options: [
      { label: "正常", value: 1 },
      { label: "禁用", value: 0 },
    ],
  },
  {
    prop: "deptId",
    label: "部门",
    type: "select",
    placeholder: "请选择部门",
    clearable: true,
    options: deptOptions.value.map((dept) => ({
      label: dept.name,
      value: dept._id,
    })),
  },
  // 自定义日期范围选择器 - 使用slot
  {
    prop: "dateRange",
    label: "创建时间",
    type: "slot",
    slot: "dateRange", // 指定slot名称
    span: 8, // 占用8个栅格
  },
  // 自定义用户状态选择器 - 使用slot
  {
    prop: "userStatusMultiple",
    label: "用户状态",
    type: "slot",
    slot: "userStatus", // 指定slot名称
    span: 6, // 占用6个栅格
  }
]);

// 表格列配置
const tableColumns = [
  {
    prop: "username",
    label: "用户名",
    minWidth: 120,
    showOverflowTooltip: true,
  },
  {
    prop: "nickname",
    label: "昵称",
    minWidth: 120,
    showOverflowTooltip: true,
  },
  {
    prop: "email",
    label: "邮箱",
    minWidth: 150,
    showOverflowTooltip: true,
  },
  {
    prop: "phone",
    label: "手机号",
    minWidth: 120,
    showOverflowTooltip: true,
  },
  {
    prop: "deptId",
    label: "部门",
    minWidth: 120,
    slot: "dept",
    showOverflowTooltip: true,
  },
  {
    prop: "roles",
    label: "角色",
    minWidth: 150,
    slot: "roles",
    showOverflowTooltip: true,
  },
  {
    prop: "status",
    label: "状态",
    width: 100,
    align: "center",
    slot: "status",
  },
  {
    prop: "createTime",
    label: "创建时间",
    minWidth: 160,
    type: "date",
    format: "YYYY-MM-DD HH:mm:ss",
    showOverflowTooltip: true,
  },
];

// 表格数据
const userList = ref([]);
const total = ref(0);
const loading = ref(false);
const selectedIds = ref([]);
const exportLoading = ref(false);

// 用户详情
const detailVisible = ref(false);
const currentUserId = ref("");

// 部门和角色选项
const deptOptions = ref([]);
const roleOptions = ref([]);

// 用户表单
const dialogVisible = ref(false);
const dialogTitle = ref("");
const userFormRef = ref(null);
const userForm = reactive({
  username: "",
  password: "",
  nickname: "",
  email: "",
  phone: "",
  status: 1,
  deptId: "",
  roles: [],
  remark: "",
});

// 表单验证规则
const userFormRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      min: 3,
      max: 50,
      message: "用户名长度在 3 到 50 个字符",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度不能少于 6 个字符", trigger: "blur" },
  ],
  nickname: [
    { required: true, message: "请输入昵称", trigger: "blur" },
    { max: 50, message: "昵称长度不能超过 50 个字符", trigger: "blur" },
  ],
  deptId: [{ required: true, message: "请选择部门", trigger: "change" }],
  roles: [
    { required: true, message: "请选择至少一个角色", trigger: "change" },
    { type: "array", min: 1, message: "请选择至少一个角色", trigger: "change" },
  ],
  email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
};

// 重置密码表单
const resetPwdDialogVisible = ref(false);
const resetPwdFormRef = ref(null);
const resetPwdForm = reactive({
  userId: "",
  username: "",
  password: "",
  confirmPassword: "",
});

// 重置密码表单验证规则
const resetPwdFormRules = {
  password: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, message: "密码长度不能少于 6 个字符", trigger: "blur" },
  ],
  confirmPassword: [
    { required: true, message: "请再次输入新密码", trigger: "blur" },
    {
      validator: (_, value, callback) => {
        if (value !== resetPwdForm.password) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

// 初始化
onMounted(() => {
  getList();
  getDeptOptions();
  getRoleOptions();
});

// 获取用户列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await getUserList(queryParams);
    userList.value = res.data;
    total.value = res.total;
  } catch (error) {
    console.error("获取用户列表失败:", error);
    ElMessage.error("获取用户列表失败");
  } finally {
    loading.value = false;
  }
};

// 获取部门选项
const getDeptOptions = async () => {
  try {
    const res = await getDepartmentList({ status: 1 });
    deptOptions.value = res.data;
  } catch (error) {
    console.error("获取部门列表失败:", error);
  }
};

// 获取角色选项
const getRoleOptions = async () => {
  try {
    const res = await getAllRoles();
    roleOptions.value = res.data;
  } catch (error) {
    console.error("获取角色列表失败:", error);
  }
};

// 搜索
const handleQuery = () => {
  queryParams.page = 1;
  getList();
};

// 重置搜索
const resetQuery = () => {
  queryParams.username = "";
  queryParams.status = "";
  queryParams.deptId = "";
  handleQuery();
};

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map((item) => item._id);
};

// 分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size;
  queryParams.page = 1;
  getList();
};

// 页码变化
const handleCurrentChange = (page) => {
  queryParams.page = page;
  getList();
};

// 添加用户
const handleAdd = () => {
  resetForm();
  dialogTitle.value = "添加用户";
  dialogVisible.value = true;
};

// 编辑用户
const handleEdit = (row) => {
  resetForm();
  dialogTitle.value = "编辑用户";
  Object.assign(userForm, { ...row, roles: row.roles.map((role) => role._id) });
  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  Object.assign(userForm, {
    _id: "",
    username: "",
    password: "",
    nickname: "",
    email: "",
    phone: "",
    status: 1,
    deptId: "",
    roles: [],
    remark: "",
  });
  if (userFormRef.value) {
    userFormRef.value.resetFields();
  }
};

// 提交表单
const submitForm = () => {
  userFormRef.value.validate(async (valid) => {
    if (!valid) return;

    try {
      if (userForm._id) {
        // 编辑用户
        await updateUser(userForm._id, userForm);
        ElMessage.success("更新用户成功");
      } else {
        // 添加用户
        await createUser(userForm);
        ElMessage.success("添加用户成功");
      }
      dialogVisible.value = false;
      getList();
    } catch (error) {
      console.error("操作失败:", error);
      ElMessage.error("操作失败: " + (error.message || "未知错误"));
    }
  });
};

// 删除用户
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗？`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        await deleteUser(row._id);
        ElMessage.success("删除成功");
        getList();
      } catch (error) {
        console.error("删除失败:", error);
        ElMessage.error("删除失败: " + (error.message || "未知错误"));
      }
    })
    .catch(() => { });
};

// 批量删除用户
const handleBatchDelete = () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning("请选择要删除的用户");
    return;
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedIds.value.length} 个用户吗？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(async () => {
      try {
        await batchDeleteUsers(selectedIds.value);
        ElMessage.success("批量删除成功");
        getList();
      } catch (error) {
        console.error("批量删除失败:", error);
        ElMessage.error("批量删除失败: " + (error.message || "未知错误"));
      }
    })
    .catch(() => { });
};

// 状态变更
const handleStatusChange = async (row, status) => {
  console.log("🔍 状态变更调试信息:");
  console.log("row对象:", row);
  console.log("row._id:", row._id);
  console.log("row.id:", row.id);
  console.log("status:", status);
  console.log("Object.keys(row):", Object.keys(row));

  // 尝试多种可能的ID字段
  const userId = row._id || row.id || row.userId;

  if (!userId) {
    console.error("❌ 无法获取用户ID");
    ElMessage.error("无法获取用户ID，请刷新页面重试");
    return;
  }

  try {
    console.log("📤 发送状态更新请求，用户ID:", userId);
    await updateUserStatus(userId, status);
    ElMessage.success(`用户状态已${status === 1 ? "启用" : "禁用"}`);
    getList();
  } catch (error) {
    console.error("更新状态失败:", error);
    ElMessage.error("更新状态失败: " + (error.message || "未知错误"));
    row.status = row.status === 1 ? 0 : 1; // 恢复原状态
  }
};

// 重置密码
const handleResetPwd = (row) => {
  resetPwdForm.userId = row._id;
  resetPwdForm.username = row.username;
  resetPwdForm.password = "";
  resetPwdForm.confirmPassword = "";
  resetPwdDialogVisible.value = true;
};

// 提交重置密码
const submitResetPwd = () => {
  resetPwdFormRef.value.validate(async (valid) => {
    if (!valid) return;

    try {
      await resetPassword(resetPwdForm.userId, {
        password: resetPwdForm.password,
      });
      ElMessage.success("密码重置成功");
      resetPwdDialogVisible.value = false;
    } catch (error) {
      console.error("密码重置失败:", error);
      ElMessage.error("密码重置失败: " + (error.message || "未知错误"));
    }
  });
};

// 查看用户详情
const handleViewDetail = (row) => {
  currentUserId.value = row._id;
  detailVisible.value = true;
};

// 从详情页编辑用户
const handleEditFromDetail = (userInfo) => {
  handleEdit(userInfo);
};

// 导出用户数据
const handleExport = async () => {
  exportLoading.value = true;
  try {
    const res = await exportUserData();

    // 创建CSV内容
    const csvContent = convertToCSV(res.data);

    // 创建下载链接
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `用户数据_${new Date().toISOString().slice(0, 10)}.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success("导出成功");
  } catch (error) {
    console.error("导出失败:", error);
    ElMessage.error("导出失败: " + (error.message || "未知错误"));
  } finally {
    exportLoading.value = false;
  }
};

// 批量操作
const handleBatchAction = async (command) => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning("请选择要操作的用户");
    return;
  }

  let confirmText = "";
  let successText = "";

  switch (command) {
    case "enable":
      confirmText = `确定要启用选中的 ${selectedIds.value.length} 个用户吗？`;
      successText = "批量启用成功";
      break;
    case "disable":
      confirmText = `确定要禁用选中的 ${selectedIds.value.length} 个用户吗？`;
      successText = "批量禁用成功";
      break;
    case "delete":
      confirmText = `确定要删除选中的 ${selectedIds.value.length} 个用户吗？`;
      successText = "批量删除成功";
      break;
  }

  try {
    await ElMessageBox.confirm(confirmText, "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    if (command === "delete") {
      await batchDeleteUsers(selectedIds.value);
    } else {
      const status = command === "enable" ? 1 : 0;
      await batchUpdateUserStatus(selectedIds.value, status);
    }

    ElMessage.success(successText);
    getList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("批量操作失败:", error);
      ElMessage.error("批量操作失败: " + (error.message || "未知错误"));
    }
  }
};

// 转换为CSV格式
const convertToCSV = (data) => {
  if (!data || data.length === 0) return "";

  const headers = Object.keys(data[0]);
  const csvHeaders = headers.join(",");

  const csvRows = data.map((row) => {
    return headers
      .map((header) => {
        const value = row[header] || "";
        // 处理包含逗号或引号的值
        if (
          typeof value === "string" &&
          (value.includes(",") || value.includes('"'))
        ) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      })
      .join(",");
  });

  return [csvHeaders, ...csvRows].join("\n");
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-buttons {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.ellipsis-text {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.role-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 100%;

  .role-tag {
    max-width: calc(100% - 8px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
