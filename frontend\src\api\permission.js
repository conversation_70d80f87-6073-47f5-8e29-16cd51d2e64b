import request from "@/utils/request";

// 获取权限列表
export function getPermissionList(params) {
  return request({
    url: "/permissions",
    method: "get",
    params,
  });
}

// 获取单个权限
export function getPermissionById(id) {
  return request({
    url: `/permissions/${id}`,
    method: "get",
  });
}

// 创建权限
export function createPermission(data) {
  return request({
    url: "/permissions",
    method: "post",
    data,
  });
}

// 更新权限
export function updatePermission(id, data) {
  return request({
    url: `/permissions/${id}`,
    method: "put",
    data,
  });
}

// 删除权限
export function deletePermission(id) {
  return request({
    url: `/permissions/${id}`,
    method: "delete",
  });
}

// 批量删除权限
export function batchDeletePermissions(ids) {
  return request({
    url: "/permissions/batch",
    method: "delete",
    data: { ids },
  });
}

// 更新权限状态
export function updatePermissionStatus(id, status) {
  console.log("API调用 - 原始数据:", {
    id,
    status,
    statusType: typeof status,
    statusValue: status,
    isArray: Array.isArray(status),
    statusString: String(status),
  });

  // 处理可能的复杂数据结构
  let actualStatus = status;

  // 如果status是对象，尝试提取实际值
  if (typeof status === "object" && status !== null) {
    console.log("检测到对象类型的status:", status);
    if (status.hasOwnProperty("status")) {
      actualStatus = status.status;
      console.log("从对象中提取status:", actualStatus);
    } else if (status.hasOwnProperty("value")) {
      actualStatus = status.value;
      console.log("从对象中提取value:", actualStatus);
    }
  }

  // 确保status是数字类型，防止数据格式错误
  let numericStatus;

  if (typeof actualStatus === "number") {
    numericStatus = actualStatus;
  } else if (typeof actualStatus === "string") {
    numericStatus =
      actualStatus === "true"
        ? 1
        : actualStatus === "false"
        ? 0
        : Number(actualStatus);
  } else if (typeof actualStatus === "boolean") {
    numericStatus = actualStatus ? 1 : 0;
  } else {
    numericStatus = Number(actualStatus);
  }

  console.log("数据转换结果:", {
    originalStatus: status,
    actualStatus,
    numericStatus,
    numericType: typeof numericStatus,
    isNaN: isNaN(numericStatus),
  });

  // 验证状态值 - 临时放宽验证，只确保是有效数字
  if (isNaN(numericStatus)) {
    console.error("无效的状态值 - 不是数字:", {
      original: status,
      actual: actualStatus,
      numeric: numericStatus,
    });
    return Promise.reject(
      new Error(`状态值必须是数字，当前值: ${numericStatus}`)
    );
  }

  // 强制转换为0或1
  numericStatus = numericStatus ? 1 : 0;

  console.log("最终状态值:", numericStatus);

  const requestData = { status: numericStatus };

  console.log("API调用 - updatePermissionStatus:", {
    id,
    requestData,
  });

  return request({
    url: `/permissions/${id}/status`,
    method: "patch",
    data: requestData,
  });
}

// 获取权限树结构
export function getPermissionTree() {
  return request({
    url: "/permissions/tree",
    method: "get",
  });
}
