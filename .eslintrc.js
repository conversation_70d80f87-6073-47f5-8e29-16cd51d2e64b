module.exports = {
  root: true,
  env: {
    node: true,
    es2022: true,
    browser: true,
  },
  extends: ["eslint:recommended"],
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: "module",
  },
  rules: {
    // 基础规则
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-unused-vars": [
      "error",
      {
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_",
      },
    ],

    // 代码风格
    indent: ["error", 2],
    quotes: ["error", "single"],
    semi: ["error", "always"],
    "comma-dangle": ["error", "never"],
    "no-trailing-spaces": "error",
    "eol-last": "error",

    // 最佳实践
    eqeqeq: ["error", "always"],
    "no-var": "error",
    "prefer-const": "error",
    "prefer-arrow-callback": "error",
    "arrow-spacing": "error",
    "object-shorthand": "error",

    // 安全相关
    "no-eval": "error",
    "no-implied-eval": "error",
    "no-new-func": "error",
    "no-script-url": "error",
  },
  overrides: [
    // 后端Node.js文件
    {
      files: ["backend/**/*.js"],
      env: {
        node: true,
        browser: false,
      },
      rules: {
        "no-console": "off", // 后端允许使用console
      },
    },

    // 前端Vue文件
    {
      files: ["frontend/**/*.js", "frontend/**/*.vue"],
      env: {
        browser: true,
        node: false,
      },
      extends: ["plugin:vue/vue3-essential"],
      rules: {
        // Vue特定规则
        "vue/multi-word-component-names": "off",
        "vue/no-unused-vars": "error",
      },
    },

    // 配置文件
    {
      files: ["*.config.js", ".eslintrc.js"],
      env: {
        node: true,
      },
    },
  ],
};
