#!/usr/bin/env node

/**
 * 实时权限状态测试脚本
 * 测试当前权限状态更新问题
 */

const http = require('http');

/**
 * 发送HTTP请求
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsed
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * 获取认证token
 */
async function getAuthToken() {
  console.log('🔐 获取认证token...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/v1/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  try {
    const result = await makeRequest(options, {
      username: 'admin',
      password: '123456'
    });
    
    if (result.statusCode === 200 && result.data.success) {
      console.log('✅ 登录成功');
      return result.data.data.token;
    } else {
      console.log('❌ 登录失败:', result.data);
      return null;
    }
  } catch (error) {
    console.log(`❌ 登录请求失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取权限列表
 */
async function getPermissions(token) {
  console.log('\n📋 获取权限列表...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/v1/permissions/tree',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  };

  try {
    const result = await makeRequest(options);
    
    if (result.statusCode === 200 && result.data.success) {
      console.log(`✅ 获取权限列表成功，共 ${result.data.data.length} 个权限`);
      return result.data.data;
    } else {
      console.log('❌ 获取权限列表失败:', result.data);
      return [];
    }
  } catch (error) {
    console.log(`❌ 获取权限列表失败: ${error.message}`);
    return [];
  }
}

/**
 * 测试权限状态更新
 */
async function testPermissionStatusUpdate(token, permission) {
  const newStatus = permission.status === 1 ? 0 : 1;
  
  console.log(`\n🧪 测试权限状态更新:`);
  console.log(`   权限: ${permission.name} (${permission.code})`);
  console.log(`   ID: ${permission._id}`);
  console.log(`   当前状态: ${permission.status}`);
  console.log(`   目标状态: ${newStatus}`);
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: `/api/v1/permissions/${permission._id}/status`,
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  };

  try {
    const result = await makeRequest(options, { status: newStatus });
    
    if (result.statusCode === 200) {
      console.log(`   ✅ 状态更新成功: ${result.data.message}`);
      return true;
    } else {
      console.log(`   ❌ 状态更新失败 (${result.statusCode}): ${result.data.message}`);
      if (result.data.errors) {
        console.log(`   错误详情:`, result.data.errors);
      }
      return false;
    }
  } catch (error) {
    console.log(`   ❌ 请求失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试前端代理
 */
async function testFrontendProxy(permission) {
  console.log(`\n🌐 测试前端代理...`);
  
  const options = {
    hostname: 'localhost',
    port: 5173,
    path: `/api/v1/permissions/${permission._id}/status`,
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Origin': 'http://localhost:5173'
    }
  };

  try {
    const result = await makeRequest(options, { status: 1 });
    
    if (result.statusCode === 200) {
      console.log(`   ✅ 前端代理成功`);
    } else if (result.statusCode === 401) {
      console.log(`   ⚠️  前端代理需要认证 (正常)`);
    } else {
      console.log(`   ❌ 前端代理失败 (${result.statusCode}): ${result.data.message || result.data}`);
    }
  } catch (error) {
    console.log(`   ❌ 前端代理请求失败: ${error.message}`);
  }
}

/**
 * 检查权限数据完整性
 */
function checkPermissionData(permissions) {
  console.log('\n🔍 检查权限数据完整性...');
  
  let validCount = 0;
  let invalidCount = 0;
  
  permissions.forEach(permission => {
    if (permission._id && permission.name && permission.code !== undefined) {
      validCount++;
    } else {
      invalidCount++;
      console.log(`   ❌ 无效权限数据:`, {
        id: permission._id,
        name: permission.name,
        code: permission.code,
        status: permission.status
      });
    }
  });
  
  console.log(`   ✅ 有效权限: ${validCount}`);
  console.log(`   ❌ 无效权限: ${invalidCount}`);
  
  return validCount > 0;
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始实时权限状态测试...\n');
  
  // 检查后端服务
  try {
    const result = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/test',
      method: 'GET'
    });
    
    if (result.statusCode === 200) {
      console.log('✅ 后端服务正常运行');
    } else {
      console.log('❌ 后端服务异常');
      return;
    }
  } catch (error) {
    console.log('❌ 无法连接到后端服务');
    return;
  }
  
  // 获取认证token
  const token = await getAuthToken();
  if (!token) {
    console.log('❌ 无法获取认证token，测试终止');
    return;
  }
  
  // 获取权限列表
  const permissions = await getPermissions(token);
  if (permissions.length === 0) {
    console.log('❌ 无法获取权限列表，测试终止');
    return;
  }
  
  // 检查权限数据完整性
  const hasValidData = checkPermissionData(permissions);
  if (!hasValidData) {
    console.log('❌ 权限数据不完整，测试终止');
    return;
  }
  
  // 测试第一个权限的状态更新
  const firstPermission = permissions[0];
  const success = await testPermissionStatusUpdate(token, firstPermission);
  
  // 测试前端代理
  await testFrontendProxy(firstPermission);
  
  console.log('\n📊 测试总结:');
  console.log(`   权限数量: ${permissions.length}`);
  console.log(`   状态更新: ${success ? '成功' : '失败'}`);
  console.log(`   认证状态: 正常`);
  
  if (!success) {
    console.log('\n🔧 建议的修复步骤:');
    console.log('1. 检查后端权限验证逻辑');
    console.log('2. 确认用户权限配置');
    console.log('3. 检查数据库连接状态');
    console.log('4. 查看后端服务器日志');
  }
  
  console.log('\n🎉 测试完成！');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}
