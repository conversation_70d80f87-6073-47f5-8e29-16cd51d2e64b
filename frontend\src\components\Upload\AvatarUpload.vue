<template>
  <div class="avatar-upload">
    <el-upload
      :action="uploadUrl"
      :headers="uploadHeaders"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      class="avatar-uploader"
    >
      <div class="avatar-container">
        <img v-if="avatarUrl" :src="avatarUrl" class="avatar" alt="头像" />
        <div v-else class="avatar-placeholder">
          <el-icon class="avatar-icon"><Plus /></el-icon>
          <div class="avatar-text">上传头像</div>
        </div>
        
        <!-- 上传遮罩 -->
        <div class="avatar-overlay">
          <el-icon class="overlay-icon"><Camera /></el-icon>
          <div class="overlay-text">更换头像</div>
        </div>
      </div>
    </el-upload>
    
    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress 
        :percentage="uploadProgress" 
        :status="uploadStatus"
        :stroke-width="4"
        :show-text="false"
      />
      <div class="progress-text">{{ uploadProgressText }}</div>
    </div>
    
    <!-- 提示信息 -->
    <div class="upload-tip" v-if="tip">
      {{ tip }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Camera } from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'

// Props
const props = defineProps({
  // 头像URL
  modelValue: {
    type: String,
    default: ''
  },
  // 上传地址
  action: {
    type: String,
    default: '/api/v1/upload/avatar'
  },
  // 文件大小限制(MB)
  maxSize: {
    type: Number,
    default: 2
  },
  // 提示文字
  tip: {
    type: String,
    default: '支持 JPG、PNG 格式，文件大小不超过 2MB'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success', 'error'])

// 响应式数据
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const uploadProgressText = ref('')

// 计算属性
const avatarUrl = computed(() => {
  return props.modelValue
})

const uploadUrl = computed(() => {
  return import.meta.env.VITE_APP_BASE_API + props.action
})

const uploadHeaders = computed(() => {
  const token = getToken()
  return token ? { Authorization: `Bearer ${token}` } : {}
})

// 上传前检查
const beforeUpload = (file) => {
  // 检查文件类型
  const isImage = /^image\/(jpeg|jpg|png|webp)$/.test(file.type)
  if (!isImage) {
    ElMessage.error('头像只支持 JPG、PNG、WebP 格式')
    return false
  }

  // 检查文件大小
  const isLt2M = file.size / 1024 / 1024 < props.maxSize
  if (!isLt2M) {
    ElMessage.error(`头像大小不能超过 ${props.maxSize}MB`)
    return false
  }

  // 开始上传
  uploading.value = true
  uploadProgress.value = 0
  uploadStatus.value = ''
  uploadProgressText.value = '上传中...'

  return true
}

// 上传成功
const handleSuccess = (response, file) => {
  uploading.value = false
  uploadProgress.value = 100
  uploadStatus.value = 'success'
  uploadProgressText.value = '上传成功'

  if (response.success) {
    emit('update:modelValue', response.data.url)
    emit('success', response, file)
    ElMessage.success('头像上传成功')
  } else {
    uploadStatus.value = 'exception'
    uploadProgressText.value = '上传失败'
    ElMessage.error(response.message || '头像上传失败')
  }

  // 3秒后隐藏进度条
  setTimeout(() => {
    uploading.value = false
  }, 3000)
}

// 上传失败
const handleError = (error, file) => {
  uploading.value = false
  uploadStatus.value = 'exception'
  uploadProgressText.value = '上传失败'
  
  console.error('头像上传失败:', error)
  ElMessage.error('头像上传失败')
  emit('error', error, file)
}
</script>

<style scoped>
.avatar-upload {
  display: inline-block;
}

.avatar-uploader {
  display: inline-block;
}

.avatar-container {
  position: relative;
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 50%;
  cursor: pointer;
  overflow: hidden;
  transition: border-color 0.3s;
}

.avatar-container:hover {
  border-color: #409eff;
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #fafafa;
}

.avatar-icon {
  font-size: 28px;
  color: #c0c4cc;
  margin-bottom: 8px;
}

.avatar-text {
  color: #c0c4cc;
  font-size: 12px;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 50%;
}

.overlay-icon {
  font-size: 24px;
  color: white;
  margin-bottom: 4px;
}

.overlay-text {
  color: white;
  font-size: 12px;
}

.upload-progress {
  margin-top: 12px;
  width: 120px;
}

.progress-text {
  text-align: center;
  color: #606266;
  font-size: 12px;
  margin-top: 4px;
}

.upload-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
  text-align: center;
  width: 120px;
}
</style>