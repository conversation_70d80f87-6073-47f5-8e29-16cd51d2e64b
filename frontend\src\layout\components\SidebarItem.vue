<template>
  <div v-if="!item.hidden">
    <template
      v-if="
        hasOneShowingChild(item.children, item) &&
        (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
        !item.alwaysShow
      "
    >
      <app-link
        v-if="onlyOneChild.meta"
        :to="resolvePath(onlyOneChild.path, onlyOneChild.query)"
      >
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{ 'submenu-title-noDropdown': !isNest }"
        >
          <!-- <svg-icon :icon-class="onlyOneChild.meta.icon || (item.meta && item.meta.icon)" /> -->
          <el-icon v-if="onlyOneChild.meta && onlyOneChild.meta.icon">
            <component :is="onlyOneChild.meta.icon" />
          </el-icon>
          <template #title
            ><span class="menu-title" :title="onlyOneChild.meta.title">{{
              onlyOneChild.meta.title
            }}</span></template
          >
        </el-menu-item>
      </app-link>
    </template>

    <el-sub-menu
      v-else
      ref="subMenu"
      :index="resolvePath(item.path)"
      teleported
    >
      <template v-if="item.meta" #title>
        <!-- <svg-icon :icon-class="item.meta && item.meta.icon" /> -->
        <el-icon v-if="item.meta && item.meta.icon">
          <component :is="item.meta.icon" />
        </el-icon>
        <span class="menu-title" :title="item.meta.title">{{
          item.meta.title
        }}</span>
      </template>

      <sidebar-item
        v-for="(child, index) in item.children"
        :key="child.path + index"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-sub-menu>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import AppLink from "./Link.vue";

const isNest = ref(false);
const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  basePath: {
    type: String,
    default: "",
  },
});

const router = useRouter();
const route = useRoute();
const onlyOneChild = ref(null);

// 判断是否只有一个显示的子菜单
const hasOneShowingChild = (children = [], parent) => {
  if (!children) {
    children = [];
  }

  const showingChildren = children.filter((item) => {
    if (item.hidden) {
      return false;
    }
    return true;
  });

  // 当只有一个子路由时，默认展示该子路由
  if (showingChildren.length === 1) {
    onlyOneChild.value = showingChildren[0];
    return true;
  }

  // 没有子路由则显示父路由
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: "", noShowingChildren: true };
    return true;
  }

  return false;
};

function resolvePath(routePath, routeQuery) {
  // 检查路由路径是否为外部路径
  if (isExternal(routePath)) {
    return routePath;
  }
  // 检查基础路径是否为外部路径
  if (isExternal(props.basePath)) {
    return props.basePath;
  }
  // 如果存在查询参数，解析并返回路径对象
  if (routeQuery) {
    let query = JSON.parse(routeQuery);
    return {
      path: getNormalPath(props.basePath + "/" + routePath),
      query: query,
    };
  }
  // 返回解析后的路径
  return getNormalPath(props.basePath + "/" + routePath);
}

function isExternal(path) {
  // 使用正则表达式检查路径是否以'http://'、'https://'、'mailto:'或'tel:'开头
  return /^(https?:|mailto:|tel:)/.test(path);
}

function getNormalPath(p) {
  if (p.length === 0 || !p || p == "undefined") {
    return p;
  }
  let res = p.replace("//", "/");
  if (res[res.length - 1] === "/") {
    return res.slice(0, res.length - 1);
  }
  return res;
}
function hasTitle(title) {
  if (title.length > 5) {
    return title;
  } else {
    return "";
  }
}
</script>

<style scoped>
.menu-title {
  display: inline-block;
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  overflow: hidden;
}
</style>
