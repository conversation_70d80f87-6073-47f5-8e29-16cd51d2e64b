import { defineStore } from "pinia";
import { constantRoutes, asyncRoutes } from "@/router/routes";

/**
 * 使用meta.roles判断当前用户是否有权限
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some((role) => route.meta.roles.includes(role));
  }
  return true;
}

/**
 * 递归过滤异步路由表
 * @param routes asyncRoutes
 * @param roles
 */
function filterAsyncRoutes(routes, roles) {
  const res = [];

  routes.forEach((route) => {
    const tmp = { ...route };
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles);
      }
      res.push(tmp);
    }
  });

  return res;
}

export const usePermissionStore = defineStore("permission", {
  state: () => ({
    routes: [],
    addRoutes: [],
  }),

  getters: {
    // 获取所有路由（包括固定路由和动态路由）
    allRoutes: (state) => state.routes,
    // 获取动态添加的路由
    dynamicRoutes: (state) => state.addRoutes,
  },

  actions: {
    // 生成路由
    generateRoutes(roles) {
      return new Promise((resolve) => {
        let accessedRoutes;
        if (roles.includes("admin")) {
          // 如果是管理员，可以访问所有路由
          accessedRoutes = asyncRoutes || [];
        } else {
          // 否则根据角色过滤路由
          accessedRoutes = filterAsyncRoutes(asyncRoutes, roles);
        }
        this.addRoutes = accessedRoutes;
        this.routes = constantRoutes.concat(accessedRoutes);
        resolve(accessedRoutes);
      });
    },

    // 重置路由
    resetRoutes() {
      this.routes = [];
      this.addRoutes = [];
    },
  },
});
