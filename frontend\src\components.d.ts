/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AvatarUpload: typeof import('./components/Upload/AvatarUpload.vue')['default']
    Dialog: typeof import('./components/Dialog/index.vue')['default']
    Empty: typeof import('./components/Empty/index.vue')['default']
    ErrorPage: typeof import('./components/ErrorPage/index.vue')['default']
    FileUpload: typeof import('./components/Upload/FileUpload.vue')['default']
    Form: typeof import('./components/Form/index.vue')['default']
    Loading: typeof import('./components/Loading/index.vue')['default']
    PermissionForm: typeof import('./components/Permission/PermissionForm.vue')['default']
    PermissionTable: typeof import('./components/Permission/PermissionTable.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Table: typeof import('./components/Table/index.vue')['default']
    UserTable: typeof import('./components/User/UserTable.vue')['default']
  }
}
