const User = require("../models/User");
const { success, error, paginate } = require("../utils/response");

/**
 * 获取用户列表（分页）
 * @route GET /api/users
 * @access Private
 */
const getUsers = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const query = {};

    // 用户名搜索（非空字符串才添加条件）
    if (req.query.username && req.query.username.trim() !== "") {
      query.username = { $regex: req.query.username.trim(), $options: "i" };
    }

    // 状态筛选（只有当值为0或1时才添加条件）
    if (req.query.status !== undefined && req.query.status !== "") {
      const statusNum = parseInt(req.query.status);
      if (!isNaN(statusNum) && (statusNum === 0 || statusNum === 1)) {
        query.status = statusNum;
      }
    }

    // 部门筛选（非空字符串才添加条件）
    if (req.query.deptId && req.query.deptId.trim() !== "") {
      query.deptId = req.query.deptId.trim();
    }

    console.log("查询条件:", query); // 添加日志

    // 查询用户总数
    const total = await User.countDocuments(query);

    // 查询用户列表
    const users = await User.find(query)
      .select("-password")
      .populate({
        path: "roles",
        select: "name code",
        match: { status: 1 },
      })
      .populate({
        path: "deptId",
        select: "name",
        match: { status: 1 },
      })
      .sort({ createTime: -1 })
      .skip(skip)
      .limit(limit);

    return paginate(res, "获取用户列表成功", users, total, page, limit);
  } catch (err) {
    console.error("获取用户列表错误:", err.message);
    return error(res, "获取用户列表失败: " + err.message, 500);
  }
};

/**
 * 获取单个用户信息
 * @route GET /api/users/:id
 * @access Private
 */
const getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select("-password")
      .populate({
        path: "roles",
        select: "name code",
        match: { status: 1 },
      })
      .populate({
        path: "deptId",
        select: "name",
        match: { status: 1 },
      });

    if (!user) {
      return error(res, "用户不存在", 404);
    }

    return success(res, "获取用户信息成功", user);
  } catch (err) {
    console.error("获取用户信息错误:", err.message);
    return error(res, "获取用户信息失败: " + err.message, 500);
  }
};

/**
 * 创建用户
 * @route POST /api/users
 * @access Private
 */
const createUser = async (req, res) => {
  try {
    const {
      username,
      password,
      nickname,
      email,
      phone,
      status,
      deptId,
      roles,
      remark,
    } = req.body;

    // 创建新用户
    const user = new User({
      username,
      password,
      nickname,
      email,
      phone,
      status,
      deptId,
      roles,
      remark,
    });

    // 保存用户
    await user.save();

    // 返回用户信息（不包含密码）
    const savedUser = await User.findById(user._id)
      .select("-password")
      .populate({
        path: "roles",
        select: "name code",
      })
      .populate({
        path: "deptId",
        select: "name",
      });

    return success(res, "创建用户成功", savedUser, 201);
  } catch (err) {
    console.error("创建用户错误:", err.message);
    return error(res, "创建用户失败: " + err.message, 500);
  }
};

/**
 * 更新用户
 * @route PUT /api/users/:id
 * @access Private
 */
const updateUser = async (req, res) => {
  try {
    const { nickname, email, phone, status, deptId, roles, remark, password } =
      req.body;

    // 查找用户
    const user = await User.findById(req.params.id);
    if (!user) {
      return error(res, "用户不存在", 404);
    }

    // 更新用户信息
    if (nickname !== undefined) user.nickname = nickname;
    if (email !== undefined) user.email = email;
    if (phone !== undefined) user.phone = phone;
    if (status !== undefined) user.status = status;
    if (deptId !== undefined) user.deptId = deptId;
    if (roles !== undefined) user.roles = roles;
    if (remark !== undefined) user.remark = remark;
    if (password) user.password = password;

    // 保存更新
    await user.save();

    // 返回更新后的用户信息（不包含密码）
    const updatedUser = await User.findById(user._id)
      .select("-password")
      .populate({
        path: "roles",
        select: "name code",
      })
      .populate({
        path: "deptId",
        select: "name",
      });

    return success(res, "更新用户成功", updatedUser);
  } catch (err) {
    console.error("更新用户错误:", err.message);
    return error(res, "更新用户失败: " + err.message, 500);
  }
};

/**
 * 删除用户
 * @route DELETE /api/users/:id
 * @access Private
 */
const deleteUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      return error(res, "用户不存在", 404);
    }

    // 不允许删除自己
    if (req.user._id.toString() === req.params.id) {
      return error(res, "不能删除当前登录用户", 400);
    }

    await user.remove();

    return success(res, "删除用户成功");
  } catch (err) {
    console.error("删除用户错误:", err.message);
    return error(res, "删除用户失败: " + err.message, 500);
  }
};

/**
 * 批量删除用户
 * @route DELETE /api/users/batch
 * @access Private
 */
const batchDeleteUsers = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return error(res, "请提供有效的用户ID数组", 400);
    }

    // 检查是否包含当前用户
    if (ids.includes(req.user._id.toString())) {
      return error(res, "不能删除当前登录用户", 400);
    }

    const result = await User.deleteMany({ _id: { $in: ids } });

    return success(res, `成功删除 ${result.deletedCount} 个用户`);
  } catch (err) {
    console.error("批量删除用户错误:", err.message);
    return error(res, "批量删除用户失败: " + err.message, 500);
  }
};

/**
 * 更新用户状态
 * @route PATCH /api/users/:id/status
 * @access Private
 */
const updateUserStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (status === undefined || ![0, 1].includes(status)) {
      return error(res, "请提供有效的状态值", 400);
    }

    const user = await User.findById(req.params.id);
    if (!user) {
      return error(res, "用户不存在", 404);
    }

    // 不允许禁用自己
    if (req.user._id.toString() === req.params.id && status === 0) {
      return error(res, "不能禁用当前登录用户", 400);
    }

    user.status = status;
    await user.save();

    return success(res, `用户状态已${status === 1 ? "启用" : "禁用"}`);
  } catch (err) {
    console.error("更新用户状态错误:", err.message);
    return error(res, "更新用户状态失败: " + err.message, 500);
  }
};

/**
 * 重置用户密码
 * @route PATCH /api/users/:id/reset-password
 * @access Private
 */
const resetPassword = async (req, res) => {
  try {
    const { password } = req.body;

    if (!password || password.length < 6) {
      return error(res, "请提供有效的密码，长度不少于6个字符", 400);
    }

    const user = await User.findById(req.params.id);
    if (!user) {
      return error(res, "用户不存在", 404);
    }

    user.password = password;
    await user.save();

    return success(res, "密码重置成功");
  } catch (err) {
    console.error("重置密码错误:", err.message);
    return error(res, "重置密码失败: " + err.message, 500);
  }
};

/**
 * 修改用户头像
 * @route PATCH /api/users/:id/avatar
 * @access Private
 */
const updateAvatar = async (req, res) => {
  try {
    const { avatar } = req.body;

    if (!avatar) {
      return error(res, "请提供头像URL", 400);
    }

    const user = await User.findById(req.params.id);
    if (!user) {
      return error(res, "用户不存在", 404);
    }

    user.avatar = avatar;
    await user.save();

    return success(res, "头像更新成功", { avatar });
  } catch (err) {
    console.error("更新头像错误:", err.message);
    return error(res, "更新头像失败: " + err.message, 500);
  }
};

/**
 * 获取用户统计信息
 * @route GET /api/users/stats
 * @access Private
 */
const getUserStats = async (req, res) => {
  try {
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ status: 1 });
    const inactiveUsers = await User.countDocuments({ status: 0 });

    // 按部门统计
    const usersByDept = await User.aggregate([
      {
        $lookup: {
          from: "departments",
          localField: "deptId",
          foreignField: "_id",
          as: "department",
        },
      },
      {
        $group: {
          _id: "$deptId",
          count: { $sum: 1 },
          deptName: { $first: "$department.name" },
        },
      },
      {
        $project: {
          deptId: "$_id",
          count: 1,
          deptName: { $arrayElemAt: ["$deptName", 0] },
        },
      },
    ]);

    // 最近注册用户
    const recentUsers = await User.find()
      .select("username nickname createTime")
      .sort({ createTime: -1 })
      .limit(5);

    const stats = {
      total: totalUsers,
      active: activeUsers,
      inactive: inactiveUsers,
      byDepartment: usersByDept,
      recent: recentUsers,
    };

    return success(res, "获取用户统计成功", stats);
  } catch (err) {
    console.error("获取用户统计错误:", err.message);
    return error(res, "获取用户统计失败: " + err.message, 500);
  }
};

/**
 * 导出用户数据
 * @route GET /api/users/export
 * @access Private
 */
const exportUsers = async (req, res) => {
  try {
    const users = await User.find()
      .select("-password")
      .populate("roles", "name")
      .populate("deptId", "name")
      .sort({ createTime: -1 });

    const exportData = users.map((user) => ({
      用户名: user.username,
      昵称: user.nickname || "",
      邮箱: user.email || "",
      手机号: user.phone || "",
      部门: user.deptId?.name || "",
      角色: user.roles?.map((role) => role.name).join(", ") || "",
      状态: user.status === 1 ? "正常" : "禁用",
      创建时间: user.createTime?.toLocaleString("zh-CN") || "",
      备注: user.remark || "",
    }));

    return success(res, "导出用户数据成功", exportData);
  } catch (err) {
    console.error("导出用户数据错误:", err.message);
    return error(res, "导出用户数据失败: " + err.message, 500);
  }
};

/**
 * 批量更新用户状态
 * @route PATCH /api/users/batch/status
 * @access Private
 */
const batchUpdateStatus = async (req, res) => {
  try {
    const { ids, status } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return error(res, "请提供有效的用户ID数组", 400);
    }

    if (![0, 1].includes(status)) {
      return error(res, "请提供有效的状态值", 400);
    }

    // 检查是否包含当前用户且要禁用
    if (status === 0 && ids.includes(req.user._id.toString())) {
      return error(res, "不能禁用当前登录用户", 400);
    }

    const result = await User.updateMany({ _id: { $in: ids } }, { status });

    return success(res, `成功更新 ${result.modifiedCount} 个用户状态`);
  } catch (err) {
    console.error("批量更新用户状态错误:", err.message);
    return error(res, "批量更新用户状态失败: " + err.message, 500);
  }
};

module.exports = {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  batchDeleteUsers,
  updateUserStatus,
  resetPassword,
  updateAvatar,
  getUserStats,
  exportUsers,
  batchUpdateStatus,
};
