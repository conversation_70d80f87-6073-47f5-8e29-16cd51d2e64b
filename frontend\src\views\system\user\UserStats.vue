<template>
  <el-card class="stats-card" shadow="never">
    <template #header>
      <div class="card-header">
        <span>用户统计</span>
        <el-button type="primary" size="small" @click="refreshStats">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </template>

    <div class="stats-content" v-loading="loading">
      <!-- 总体统计 -->
      <div class="overview-stats">
        <div class="stat-item">
          <div class="stat-icon total">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.total || 0 }}</div>
            <div class="stat-label">总用户数</div>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon active">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.active || 0 }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon inactive">
            <el-icon><Close /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.inactive || 0 }}</div>
            <div class="stat-label">禁用用户</div>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon rate">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ activeRate }}%</div>
            <div class="stat-label">活跃率</div>
          </div>
        </div>
      </div>

      <!-- 部门分布 -->
      <div class="department-stats">
        <h4>部门分布</h4>
        <div class="dept-list">
          <div
            v-for="dept in stats.byDepartment"
            :key="dept.deptId"
            class="dept-item"
          >
            <div class="dept-info">
              <span class="dept-name">{{ dept.deptName || '未分配' }}</span>
              <span class="dept-count">{{ dept.count }}人</span>
            </div>
            <el-progress
              :percentage="getDeptPercentage(dept.count)"
              :stroke-width="8"
              :show-text="false"
            />
          </div>
        </div>
      </div>

      <!-- 最近注册 -->
      <div class="recent-users">
        <h4>最近注册</h4>
        <div class="user-list">
          <div
            v-for="user in stats.recent"
            :key="user._id"
            class="user-item"
          >
            <el-avatar :size="32" class="user-avatar">
              {{ user.nickname?.charAt(0) || user.username?.charAt(0) }}
            </el-avatar>
            <div class="user-info">
              <div class="user-name">{{ user.nickname || user.username }}</div>
              <div class="user-time">{{ formatDate(user.createTime) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh, User, Check, Close, TrendCharts 
} from '@element-plus/icons-vue'
import { getUserStats } from '@/api/user'
import { formatDate } from '@/utils/format'

// 响应式数据
const loading = ref(false)
const stats = ref({
  total: 0,
  active: 0,
  inactive: 0,
  byDepartment: [],
  recent: []
})

// 计算属性
const activeRate = computed(() => {
  if (stats.value.total === 0) return 0
  return Math.round((stats.value.active / stats.value.total) * 100)
})

// 获取部门百分比
const getDeptPercentage = (count) => {
  if (stats.value.total === 0) return 0
  return Math.round((count / stats.value.total) * 100)
}

// 获取统计数据
const getStatsData = async () => {
  loading.value = true
  try {
    const res = await getUserStats()
    stats.value = res.data
  } catch (error) {
    console.error('获取用户统计失败:', error)
    ElMessage.error('获取用户统计失败')
  } finally {
    loading.value = false
  }
}

// 刷新统计
const refreshStats = () => {
  getStatsData()
}

// 初始化
onMounted(() => {
  getStatsData()
})
</script>

<style lang="scss" scoped>
.stats-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-content {
  .overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;

    .stat-item {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      gap: 15px;

      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.active {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.inactive {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.rate {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }

      .stat-info {
        .stat-value {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          line-height: 1;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 5px;
        }
      }
    }
  }

  .department-stats,
  .recent-users {
    margin-bottom: 30px;

    h4 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .dept-list {
    .dept-item {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }

      .dept-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .dept-name {
          font-size: 14px;
          color: #303133;
        }

        .dept-count {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .user-list {
    .user-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
      gap: 12px;

      &:last-child {
        border-bottom: none;
      }

      .user-avatar {
        background: #409eff;
        color: white;
        font-size: 14px;
      }

      .user-info {
        flex: 1;

        .user-name {
          font-size: 14px;
          color: #303133;
          margin-bottom: 2px;
        }

        .user-time {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}
</style>