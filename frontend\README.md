# 后台管理系统前端

这是基于 Vue 3、Vite、Element Plus 的后台管理系统前端项目。

## 技术栈

- Vue 3
- Vite
- Pinia (状态管理)
- Element Plus (UI 组件库)
- Vue Router
- Axios

## 项目结构

```
frontend/
├── public/              # 静态资源
├── src/
│   ├── api/             # API请求
│   ├── assets/          # 资源文件
│   ├── components/      # 公共组件
│   ├── directives/      # 自定义指令
│   ├── hooks/           # 组合式函数
│   ├── layout/          # 布局组件
│   ├── router/          # 路由配置
│   ├── stores/          # Pinia状态管理
│   ├── styles/          # 样式文件
│   ├── utils/           # 工具函数
│   ├── views/           # 页面组件
│   ├── App.vue          # 根组件
│   ├── main.js          # 入口文件
│   └── permission.js    # 权限控制
├── .env.development     # 开发环境变量
├── .env.production      # 生产环境变量
├── index.html           # HTML模板
├── package.json         # 依赖管理
└── vite.config.js       # Vite配置
```

## 开发指南

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产构建
npm run preview
```

## 功能特性

- 用户认证与授权
- 用户管理
- 角色管理
- 权限管理
- 部门管理
- 响应式布局
- 主题定制
