#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

console.log("🚀 开始项目初始化...\n");

// 检查Node.js版本
function checkNodeVersion() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split(".")[0]);

  if (majorVersion < 16) {
    console.error("❌ Node.js版本过低，请升级到16.0.0或更高版本");
    process.exit(1);
  }

  console.log(`✅ Node.js版本检查通过: ${nodeVersion}`);
}

// 检查MongoDB连接
function checkMongoDB() {
  try {
    execSync("mongosh --version", { stdio: "ignore" });
    console.log("✅ MongoDB客户端已安装");
  } catch (error) {
    console.log("⚠️  MongoDB客户端未安装，请手动安装MongoDB");
  }
}

// 创建必要的目录
function createDirectories() {
  const dirs = [
    "backend/logs",
    "backend/uploads",
    "backend/uploads/images",
    "backend/uploads/documents",
    "backend/uploads/avatars",
    "backend/uploads/temp",
  ];

  dirs.forEach((dir) => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 创建目录: ${dir}`);
    }
  });
}

// 复制环境变量文件
function setupEnvFiles() {
  const envFiles = [
    { src: "backend/.env.example", dest: "backend/.env" },
    { src: "frontend/.env.example", dest: "frontend/.env.development" },
  ];

  envFiles.forEach(({ src, dest }) => {
    if (!fs.existsSync(dest) && fs.existsSync(src)) {
      fs.copyFileSync(src, dest);
      console.log(`📄 创建环境变量文件: ${dest}`);
    }
  });
}

// 安装依赖
function installDependencies() {
  console.log("\n📦 安装项目依赖...");

  try {
    // 安装根目录依赖
    console.log("安装根目录依赖...");
    execSync("npm install", { stdio: "inherit" });

    // 安装后端依赖
    console.log("安装后端依赖...");
    execSync("npm run install:backend", { stdio: "inherit" });

    // 安装前端依赖
    console.log("安装前端依赖...");
    execSync("npm run install:frontend", { stdio: "inherit" });

    console.log("✅ 依赖安装完成");
  } catch (error) {
    console.error("❌ 依赖安装失败:", error.message);
    process.exit(1);
  }
}

// 初始化数据库
function initDatabase() {
  console.log("\n🗄️  初始化数据库...");

  try {
    execSync("npm run init-db", { stdio: "inherit" });
    console.log("✅ 数据库初始化完成");
  } catch (error) {
    console.error("❌ 数据库初始化失败:", error.message);
    console.log("请确保MongoDB服务已启动，然后手动运行: npm run init-db");
  }
}

// 生成安全密钥
function generateSecrets() {
  const envPath = "backend/.env";
  if (fs.existsSync(envPath)) {
    let envContent = fs.readFileSync(envPath, "utf8");

    // 生成JWT密钥
    const jwtSecret = require("crypto").randomBytes(64).toString("hex");
    envContent = envContent.replace(
      "JWT_SECRET=your_jwt_secret_key_here_please_change_in_production",
      `JWT_SECRET=${jwtSecret}`
    );

    fs.writeFileSync(envPath, envContent);
    console.log("🔐 已生成安全的JWT密钥");
  }
}

// 显示完成信息
function showCompletionInfo() {
  console.log("\n🎉 项目初始化完成！\n");
  console.log("📋 接下来的步骤:");
  console.log("1. 启动MongoDB服务");
  console.log("2. 配置环境变量 (backend/.env 和 frontend/.env.development)");
  console.log("3. 运行开发服务器:");
  console.log("   npm run dev          # 同时启动前后端");
  console.log("   npm run dev:backend  # 仅启动后端");
  console.log("   npm run dev:frontend # 仅启动前端");
  console.log("\n🌐 访问地址:");
  console.log("   前端: http://localhost:5173");
  console.log("   后端: http://localhost:3000");
  console.log("   API文档: http://localhost:3000/api-docs");
  console.log("\n👤 默认管理员账号:");
  console.log("   用户名: admin");
  console.log("   密码: 123456");
  console.log("\n📚 更多信息请查看 README.md");
}

// 主函数
async function main() {
  try {
    checkNodeVersion();
    checkMongoDB();
    createDirectories();
    setupEnvFiles();
    generateSecrets();
    installDependencies();
    initDatabase();
    showCompletionInfo();
  } catch (error) {
    console.error("❌ 初始化失败:", error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
