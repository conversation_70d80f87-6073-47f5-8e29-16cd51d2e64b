const express = require("express");
const router = express.Router();
const {
  register,
  login,
  getProfile,
  changePassword,
  logout,
} = require("../controllers/authController");
const { authenticate } = require("../middleware/auth");
const { validate } = require("../middleware/validator");
const {
  registerValidator,
  loginValidator,
  changePasswordValidator,
} = require("../validators/userValidator");

// 用户注册
router.post("/register", registerValidator, validate, register);

// 用户登录
router.post("/login", loginValidator, validate, login);

// 获取当前用户信息
router.get("/profile", authenticate, getProfile);

// 修改密码
router.put(
  "/change-password",
  authenticate,
  changePasswordValidator,
  validate,
  changePassword
);

// 退出登录
router.post("/logout", authenticate, logout);

module.exports = router;
