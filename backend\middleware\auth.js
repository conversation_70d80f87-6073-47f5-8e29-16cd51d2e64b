const { verifyToken } = require("../utils/jwt");
const { error } = require("../utils/response");
const User = require("../models/User");

/**
 * 验证用户是否已登录
 */
const authenticate = async (req, res, next) => {
  try {
    // 从请求头获取token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      console.log(
        "Authentication failed: Authorization header missing or invalid format"
      );
      return error(res, "未授权访问，请先登录", 401);
    }

    // 提取token
    const token = authHeader.split(" ")[1];
    if (!token) {
      console.log("Authentication failed: Token is empty");
      return error(res, "无效的令牌", 401);
    }

    // 验证token
    const decoded = verifyToken(token);
    if (!decoded) {
      console.log("Authentication failed: Token verification failed");
      return error(res, "令牌已过期或无效", 401);
    }

    console.log("Token decoded successfully:", decoded);

    // 测试用户处理
    if (decoded.id === "test-user-id") {
      req.user = {
        _id: "test-user-id",
        username: "admin",
        email: "<EMAIL>",
        phone: "13800138000",
        nickname: "管理员",
        status: 1,
        createTime: new Date().toISOString(),
      };
      return next();
    }

    try {
      // 查找用户
      const user = await User.findById(decoded.id).select("-password");
      if (!user) {
        return error(res, "用户不存在", 401);
      }

      // 检查用户状态
      if (user.status !== 1) {
        return error(res, "用户已被禁用", 403);
      }

      // 将用户信息添加到请求对象
      req.user = user;
      next();
    } catch (dbErr) {
      console.error("数据库查询错误:", dbErr.message);
      return error(res, "授权验证失败: 数据库连接问题", 401);
    }
  } catch (err) {
    return error(res, "授权验证失败", 401);
  }
};

/**
 * 验证用户是否具有指定权限
 * @param {String} permission - 所需权限
 */
const authorize = (permission) => {
  return async (req, res, next) => {
    try {
      // 确保用户已通过身份验证
      if (!req.user) {
        return error(res, "未授权访问，请先登录", 401);
      }

      // 测试用户处理 - 超级管理员拥有所有权限
      if (req.user._id === "test-user-id") {
        return next();
      }

      try {
        // 查找用户并填充角色和权限
        const user = await User.findById(req.user._id).populate({
          path: "roles",
          match: { status: 1 },
          populate: {
            path: "permissions",
            match: { status: 1 },
          },
        });

        // 检查用户是否有有效角色
        if (!user.roles || user.roles.length === 0) {
          return error(res, "您没有任何角色，无法访问此资源", 403);
        }

        // 收集用户所有角色的所有权限
        const userPermissions = new Set();
        user.roles.forEach((role) => {
          if (role.permissions && role.permissions.length > 0) {
            role.permissions.forEach((perm) => {
              if (perm.perms) {
                userPermissions.add(perm.perms);
              }
            });
          }
        });

        // 检查用户是否具有所需权限
        if (!userPermissions.has(permission)) {
          return error(res, "您没有权限执行此操作", 403);
        }

        next();
      } catch (dbErr) {
        console.error("数据库查询错误:", dbErr.message);
        return error(res, "权限验证失败: 数据库连接问题", 500);
      }
    } catch (err) {
      return error(res, "权限验证失败", 500);
    }
  };
};

module.exports = {
  authenticate,
  authorize,
};
