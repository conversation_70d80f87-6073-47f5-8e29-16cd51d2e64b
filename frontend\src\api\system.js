import request from "@/utils/request";

// 获取系统信息
export function getSystemInfo() {
  return request({
    url: "/system/info",
    method: "get",
  });
}

// 获取应用统计信息
export function getAppStats() {
  return request({
    url: "/system/stats",
    method: "get",
  });
}

// 健康检查
export function healthCheck() {
  return request({
    url: "/system/health",
    method: "get",
  });
}

// 清理系统缓存
export function clearSystemCache() {
  return request({
    url: "/system/cache/clear",
    method: "post",
  });
}

// 获取日志文件列表
export function getLogFiles() {
  return request({
    url: "/system/logs",
    method: "get",
  });
}

// 下载日志文件
export function downloadLogFile(filename) {
  return request({
    url: `/system/logs/${filename}`,
    method: "get",
    responseType: "blob",
  });
}
