{"name": "ruo-admin-backend", "version": "1.0.0", "description": "若依管理系统后端API", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "init-db": "node scripts/init-db.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "mongodb", "mongoose", "jwt", "admin"], "author": "", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.8", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"nodemon": "^3.0.2"}}