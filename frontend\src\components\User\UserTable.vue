<template>
  <div class="user-table">
    <el-table
      v-loading="loading"
      :data="userList"
      row-key="_id"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column
        label="用户名"
        prop="username"
        min-width="120"
        show-overflow-tooltip
      />

      <el-table-column
        label="昵称"
        prop="nickname"
        min-width="120"
        show-overflow-tooltip
      />

      <el-table-column
        label="邮箱"
        prop="email"
        min-width="150"
        show-overflow-tooltip
      />

      <el-table-column
        label="手机号"
        prop="phone"
        min-width="120"
        show-overflow-tooltip
      />

      <el-table-column label="部门" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.deptId?.name || "-" }}</span>
        </template>
      </el-table-column>

      <el-table-column label="角色" min-width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="role-tags">
            <el-tag
              v-for="role in row.roles"
              :key="role._id"
              class="role-tag"
              size="small"
            >
              {{ role.name }}
            </el-tag>
            <span v-if="!row.roles || row.roles.length === 0">-</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-switch
            :model-value="row.status"
            :active-value="1"
            :inactive-value="0"
            @change="(val) => handleStatusChange(row, val)"
          />
        </template>
      </el-table-column>

      <el-table-column label="创建时间" min-width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <el-button
            type="info"
            size="small"
            link
            @click="handleViewDetail(row)"
          >
            详情
          </el-button>
          <el-button type="primary" size="small" link @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button
            type="warning"
            size="small"
            link
            @click="handleResetPwd(row)"
          >
            重置密码
          </el-button>
          <el-button type="danger" size="small" link @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { updateUserStatus } from "@/api/user";
import { formatDate } from "@/utils/format";

// Props
const props = defineProps({
  userList: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pagination: {
    type: Object,
    default: () => ({
      page: 1,
      pageSize: 10,
      total: 0,
    }),
  },
});

// Emits
const emit = defineEmits([
  "selection-change",
  "size-change",
  "current-change",
  "view-detail",
  "edit",
  "reset-password",
  "delete",
  "refresh",
]);

// 选择变化
const handleSelectionChange = (selection) => {
  emit("selection-change", selection);
};

// 分页大小变化
const handleSizeChange = (size) => {
  emit("size-change", size);
};

// 页码变化
const handleCurrentChange = (page) => {
  emit("current-change", page);
};

// 查看详情
const handleViewDetail = (row) => {
  emit("view-detail", row);
};

// 编辑用户
const handleEdit = (row) => {
  emit("edit", row);
};

// 重置密码
const handleResetPwd = (row) => {
  emit("reset-password", row);
};

// 删除用户
const handleDelete = (row) => {
  emit("delete", row);
};

// 状态变更 - 修复ID undefined问题
const handleStatusChange = async (row, status) => {
  console.log("🔍 用户状态变更调试信息:");
  console.log("row对象:", row);
  console.log("row._id:", row._id);
  console.log("row.id:", row.id);
  console.log("status:", status);
  console.log("Object.keys(row):", Object.keys(row));

  // 尝试多种可能的ID字段
  const userId = row._id || row.id || row.userId;

  if (!userId) {
    console.error("❌ 无法获取用户ID");
    ElMessage.error("无法获取用户ID，请刷新页面重试");
    return;
  }

  try {
    console.log("📤 发送状态更新请求，用户ID:", userId);
    await updateUserStatus(userId, status);
    ElMessage.success(`用户状态已${status === 1 ? "启用" : "禁用"}`);

    // 更新本地状态
    row.status = status;

    // 通知父组件刷新数据
    emit("refresh");
  } catch (error) {
    console.error("更新状态失败:", error);
    ElMessage.error("更新状态失败: " + (error.message || "未知错误"));

    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1;
  }
};
</script>

<style lang="scss" scoped>
.user-table {
  .role-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    max-width: 100%;

    .role-tag {
      max-width: calc(100% - 8px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
