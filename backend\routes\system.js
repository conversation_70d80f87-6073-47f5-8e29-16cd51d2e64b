const express = require("express");
const router = express.Router();
const systemController = require("../controllers/systemController");
const { authenticate } = require("../middleware/auth");
const { checkPermission } = require("../middleware/permission");

// 系统信息路由（需要管理员权限）
router.get(
  "/info",
  authenticate,
  checkPermission("system:monitor:info"),
  systemController.getSystemInfo
);

// 健康检查路由（公开访问）
router.get("/health", systemController.healthCheck);

// 应用统计信息路由
router.get(
  "/stats",
  authenticate,
  checkPermission("system:monitor:stats"),
  systemController.getAppStats
);

// 清理缓存路由
router.post(
  "/cache/clear",
  authenticate,
  checkPermission("system:monitor:cache"),
  systemController.clearCache
);

// 日志管理路由
router.get(
  "/logs",
  authenticate,
  checkPermission("system:monitor:logs"),
  systemController.getLogFiles
);

router.get(
  "/logs/:filename",
  authenticate,
  checkPermission("system:monitor:logs"),
  systemController.downloadLogFile
);

module.exports = router;
