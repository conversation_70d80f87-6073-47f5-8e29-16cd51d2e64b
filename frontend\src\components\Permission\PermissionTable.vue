<template>
  <div class="permission-table">
    <el-table
      v-loading="loading"
      :data="permissionList"
      row-key="_id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :expand-row-keys="expandedKeys"
    >
      <el-table-column label="权限名称" prop="name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="图标" align="center" width="100">
        <template #default="{ row }">
          <el-icon v-if="row.icon">
            <component :is="row.icon" />
          </el-icon>
        </template>
      </el-table-column>

      <el-table-column label="权限标识" prop="code" show-overflow-tooltip />

      <el-table-column label="路由地址" prop="path" show-overflow-tooltip />

      <el-table-column
        label="组件路径"
        prop="component"
        show-overflow-tooltip
      />

      <el-table-column label="权限类型" align="center" width="100">
        <template #default="{ row }">
          <el-tag :type="row.type === 'menu' ? 'primary' : 'success'">
            {{ row.type === "menu" ? "菜单" : "按钮" }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="显示顺序"
        prop="sort"
        width="100"
        align="center"
      />

      <el-table-column label="状态" align="center" width="100">
        <template #default="{ row }">
          <el-switch
            :model-value="row.status"
            :active-value="1"
            :inactive-value="0"
            @change="(val) => handleStatusChange(row, val)"
          />
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleAdd(row)"
            >新增</el-button
          >
          <el-button type="primary" link @click="handleEdit(row)"
            >编辑</el-button
          >
          <el-button type="danger" link @click="handleDelete(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { updatePermissionStatus } from "@/api/permission";

// Props
const props = defineProps({
  permissionList: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  expandedKeys: {
    type: Array,
    default: () => [],
  },
});

// Emits
const emit = defineEmits(["add", "edit", "delete", "refresh"]);

// 新增权限
const handleAdd = (row) => {
  emit("add", row);
};

// 编辑权限
const handleEdit = (row) => {
  emit("edit", row);
};

// 删除权限
const handleDelete = (row) => {
  emit("delete", row);
};

// 状态变更
const handleStatusChange = async (row, status) => {
  try {
    const numericStatus = Number(status);
    console.log("权限状态更新 - 开始:", {
      id: row._id,
      name: row.name,
      originalStatus: status,
      numericStatus,
    });

    await updatePermissionStatus(row._id, numericStatus);
    console.log("权限状态更新 - 成功");

    ElMessage.success(`${numericStatus === 1 ? "启用" : "禁用"}成功`);

    // 更新本地数据
    row.status = numericStatus;

    // 通知父组件刷新数据
    emit("refresh");
  } catch (error) {
    console.error("权限状态更新 - 失败:", error);
    ElMessage.error(
      "状态更新失败: " +
        (error.response?.data?.message || error.message || "未知错误")
    );

    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1;
  }
};
</script>

<style lang="scss" scoped>
.permission-table {
  // 可以添加表格特定样式
}
</style>
