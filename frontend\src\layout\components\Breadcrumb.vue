<template>
    <div class="breadcrumb-container">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index" :to="item.path">
                {{ item.meta.title }}
            </el-breadcrumb-item>
        </el-breadcrumb>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const breadcrumbs = ref([])
const currentTitle = ref('')

// 根据路由生成面包屑
const getBreadcrumbs = () => {
    // 获取当前路由的匹配记录
    const matched = route.matched.filter(item => item.meta && item.meta.title)

    // 设置当前页面标题
    currentTitle.value = matched.length > 0
        ? matched[matched.length - 1].meta.title
        : '页面'

    // 过滤掉首页，因为已经在模板中固定添加了
    breadcrumbs.value = matched.filter(item => item.path !== '/')

    console.log('生成的面包屑:', breadcrumbs.value)
}

// 监听路由变化，更新面包屑
watch(
    () => route.path,
    () => getBreadcrumbs(),
    { immediate: true }
)
</script>

<style lang="scss" scoped>
.breadcrumb-container {
    display: flex;
    align-items: center;
    height: 100%;

    .el-breadcrumb {
        font-size: 14px;

        :deep(.el-breadcrumb__inner) {
            color: #606266;

            &.is-link {
                color: #409EFF;
                font-weight: normal;

                &:hover {
                    color: #66b1ff;
                }
            }
        }

        :deep(.el-breadcrumb__separator) {
            margin: 0 8px;
            color: #909399;
        }
    }
}
</style>