{"name": "ruo-admin", "version": "1.0.0", "description": "基于 Node.js 和 Vue3 的后台管理系统", "scripts": {"setup": "node scripts/setup.js", "dev": "node scripts/dev.js", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "install:all": "npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "init-db": "cd backend && npm run init-db", "add-sample-data": "cd backend && node scripts/add-sample-data.js", "check-data": "cd backend && node scripts/check-data.js", "check-cors": "node scripts/check-cors.js", "test-cors": "node scripts/test-cors.js", "check-vue": "node scripts/check-vue-issues.js", "test-api": "node scripts/test-api.js", "test-status": "node scripts/test-status-apis.js", "debug-request": "node scripts/debug-request.js", "debug-permission": "node scripts/debug-permission-status.js", "test-permission-live": "node scripts/test-permission-status-live.js", "test-status-validation": "node scripts/test-status-validation.js", "cleanup-empty-dirs": "node scripts/cleanup-empty-dirs.js", "switch-to-db-auth": "node scripts/switch-to-db-auth.js", "tools": "node scripts/dev-tools.js", "tools:check": "node scripts/dev-tools.js check", "tools:clean": "node scripts/dev-tools.js clean", "tools:reset": "node scripts/dev-tools.js reset", "tools:backup": "node scripts/dev-tools.js backup", "tools:restore": "node scripts/dev-tools.js restore", "tools:logs": "node scripts/dev-tools.js logs", "deploy:prod": "./scripts/deploy.sh prod", "deploy:dev": "./scripts/deploy.sh dev", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "keywords": ["admin", "vue3", "nodejs", "express", "mongodb", "element-plus"], "author": "Ruo Admin Team", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "workspaces": ["backend", "frontend"]}