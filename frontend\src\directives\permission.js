import usePermission from "@/hooks/usePermission";

export default {
  mounted(el, binding) {
    const { hasPermission, hasRole } = usePermission();
    const { value } = binding;

    if (value) {
      // 判断是否为角色权限
      const isRole = value.startsWith("role:");

      // 获取权限标识
      const permission = isRole ? value.slice(5) : value;

      // 验证权限
      const hasAuth = isRole ? hasRole(permission) : hasPermission(permission);

      if (!hasAuth) {
        // 如果没有权限，则移除元素
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      throw new Error("需要指定权限标识");
    }
  },
};
