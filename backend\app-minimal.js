const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");

// 加载环境变量
dotenv.config();

// 创建Express应用
const app = express();

// 基础中间件
app.use(cors());
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: false, limit: "10mb" }));

// 静态文件服务
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// 基础路由
app.get("/", (req, res) => {
  res.json({
    success: true,
    message: "服务器运行正常",
    timestamp: new Date().toISOString(),
  });
});

app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

app.get("/api/v1/test", (req, res) => {
  res.json({
    success: true,
    message: "API测试成功",
    data: {
      time: new Date().toISOString(),
    },
  });
});

// 404处理
app.use("*", (req, res) => {
  res.status(404).json({
    code: 404,
    success: false,
    message: "接口不存在",
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error("服务器错误:", error);
  res.status(500).json({
    code: 500,
    success: false,
    message: "服务器内部错误",
  });
});

// 启动服务器
const PORT = process.env.PORT || 3000;

console.log("正在启动服务器...");

const server = app.listen(PORT, () => {
  console.log(`✅ 服务器成功启动在端口 ${PORT}`);
  console.log(`🌐 访问地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`🧪 API测试: http://localhost:${PORT}/api/v1/test`);
});

// 优雅关闭
process.on("SIGTERM", () => {
  console.log("收到SIGTERM信号，正在关闭服务器...");
  server.close(() => {
    console.log("服务器已关闭");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  console.log("收到SIGINT信号，正在关闭服务器...");
  server.close(() => {
    console.log("服务器已关闭");
    process.exit(0);
  });
});

module.exports = app;
