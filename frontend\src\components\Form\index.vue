<template>
  <el-form ref="formRef" :model="formData" :rules="formRules" :label-width="labelWidth" :label-position="labelPosition"
    :size="size" :disabled="disabled" v-bind="$attrs">
    <el-row :gutter="gutter">
      <template v-for="item in formItems" :key="item.prop">
        <el-col :span="item.span || defaultSpan">
          <el-form-item :prop="item.prop" :label="item.label" :required="item.required" :rules="item.rules">
            <!-- 输入框 -->
            <el-input v-if="item.type === 'input'" v-model="formData[item.prop]"
              :placeholder="item.placeholder || `请输入${item.label}`" :disabled="item.disabled"
              :clearable="item.clearable !== false" :show-password="item.showPassword" :maxlength="item.maxlength"
              :show-word-limit="item.showWordLimit" v-bind="item.attrs" />

            <!-- 文本域 -->
            <el-input v-else-if="item.type === 'textarea'" v-model="formData[item.prop]" type="textarea"
              :placeholder="item.placeholder || `请输入${item.label}`" :disabled="item.disabled" :rows="item.rows || 3"
              :maxlength="item.maxlength" :show-word-limit="item.showWordLimit" v-bind="item.attrs" />

            <!-- 数字输入框 -->
            <el-input-number v-else-if="item.type === 'number'" v-model="formData[item.prop]"
              :placeholder="item.placeholder || `请输入${item.label}`" :disabled="item.disabled" :min="item.min"
              :max="item.max" :step="item.step" :precision="item.precision" :controls-position="item.controlsPosition"
              v-bind="item.attrs" />

            <!-- 选择器 -->
            <el-select v-else-if="item.type === 'select'" v-model="formData[item.prop]"
              :placeholder="item.placeholder || `请选择${item.label}`" :disabled="item.disabled"
              :clearable="item.clearable !== false" :multiple="item.multiple" :filterable="item.filterable"
              :remote="item.remote" :remote-method="item.remoteMethod" :loading="item.loading" v-bind="item.attrs">
              <el-option v-for="option in item.options" :key="option.value" :label="option.label" :value="option.value"
                :disabled="option.disabled" />
            </el-select>

            <!-- 级联选择器 -->
            <el-cascader v-else-if="item.type === 'cascader'" v-model="formData[item.prop]" :options="item.options"
              :placeholder="item.placeholder || `请选择${item.label}`" :disabled="item.disabled"
              :clearable="item.clearable !== false" :show-all-levels="item.showAllLevels !== false"
              :collapse-tags="item.collapseTags" :separator="item.separator" :props="item.cascaderProps"
              v-bind="item.attrs" />

            <!-- 日期选择器 -->
            <el-date-picker v-else-if="item.type === 'date'" v-model="formData[item.prop]"
              :type="item.dateType || 'date'" :placeholder="item.placeholder || `请选择${item.label}`"
              :disabled="item.disabled" :clearable="item.clearable !== false" :format="item.format"
              :value-format="item.valueFormat" :start-placeholder="item.startPlaceholder"
              :end-placeholder="item.endPlaceholder" :range-separator="item.rangeSeparator" v-bind="item.attrs" />

            <!-- 时间选择器 -->
            <el-time-picker v-else-if="item.type === 'time'" v-model="formData[item.prop]"
              :placeholder="item.placeholder || `请选择${item.label}`" :disabled="item.disabled"
              :clearable="item.clearable !== false" :format="item.format" :value-format="item.valueFormat"
              v-bind="item.attrs" />

            <!-- 单选框组 -->
            <el-radio-group v-else-if="item.type === 'radio'" v-model="formData[item.prop]" :disabled="item.disabled"
              v-bind="item.attrs">
              <el-radio v-for="option in item.options" :key="option.value" :label="option.value"
                :disabled="option.disabled">
                {{ option.label }}
              </el-radio>
            </el-radio-group>

            <!-- 复选框组 -->
            <el-checkbox-group v-else-if="item.type === 'checkbox'" v-model="formData[item.prop]"
              :disabled="item.disabled" v-bind="item.attrs">
              <el-checkbox v-for="option in item.options" :key="option.value" :label="option.value"
                :disabled="option.disabled">
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>

            <!-- 开关 -->
            <el-switch v-else-if="item.type === 'switch'" v-model="formData[item.prop]" :disabled="item.disabled"
              :active-text="item.activeText" :inactive-text="item.inactiveText" :active-value="item.activeValue"
              :inactive-value="item.inactiveValue" v-bind="item.attrs" />

            <!-- 滑块 -->
            <el-slider v-else-if="item.type === 'slider'" v-model="formData[item.prop]" :disabled="item.disabled"
              :min="item.min" :max="item.max" :step="item.step" :show-input="item.showInput"
              :show-stops="item.showStops" :range="item.range" v-bind="item.attrs" />

            <!-- 评分 -->
            <el-rate v-else-if="item.type === 'rate'" v-model="formData[item.prop]" :disabled="item.disabled"
              :max="item.max" :allow-half="item.allowHalf" :show-text="item.showText" :texts="item.texts"
              v-bind="item.attrs" />

            <!-- 颜色选择器 -->
            <el-color-picker v-else-if="item.type === 'color'" v-model="formData[item.prop]" :disabled="item.disabled"
              :show-alpha="item.showAlpha" :color-format="item.colorFormat" v-bind="item.attrs" />

            <!-- 文件上传 -->
            <el-upload v-else-if="item.type === 'upload'" :action="item.action" :headers="item.headers"
              :data="item.data" :name="item.name" :with-credentials="item.withCredentials" :multiple="item.multiple"
              :accept="item.accept" :auto-upload="item.autoUpload !== false" :list-type="item.listType"
              :drag="item.drag" :disabled="item.disabled" :limit="item.limit" :file-list="formData[item.prop]"
              :before-upload="item.beforeUpload" :on-success="(response, file, fileList) =>
                  handleUploadSuccess(response, file, fileList, item.prop)
                " :on-error="item.onError" :on-remove="(file, fileList) =>
                  handleUploadRemove(file, fileList, item.prop)
                " v-bind="item.attrs">
              <template v-if="item.listType === 'picture-card'">
                <el-icon>
                  <Plus />
                </el-icon>
              </template>
              <template v-else-if="item.drag">
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
              </template>
              <template v-else>
                <el-button type="primary">点击上传</el-button>
              </template>
            </el-upload>

            <!-- 自定义插槽 -->
            <slot v-else-if="item.type === 'slot'" :name="item.slot" :item="item" :form-data="formData" />
          </el-form-item>
        </el-col>
      </template>
    </el-row>

    <!-- 表单操作按钮 -->
    <el-form-item v-if="showActions" class="form-actions">
      <slot name="actions">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ submitText }}
        </el-button>
      </slot>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { Plus, UploadFilled } from "@element-plus/icons-vue";

// Props
const props = defineProps({
  // 表单数据
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  // 表单项配置
  formItems: {
    type: Array,
    default: () => [],
  },
  // 表单验证规则
  rules: {
    type: Object,
    default: () => ({}),
  },
  // 标签宽度
  labelWidth: {
    type: String,
    default: "100px",
  },
  // 标签位置
  labelPosition: {
    type: String,
    default: "right",
  },
  // 表单尺寸
  size: {
    type: String,
    default: "default",
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 栅格间隔
  gutter: {
    type: Number,
    default: 20,
  },
  // 默认栅格占位
  defaultSpan: {
    type: Number,
    default: 24,
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true,
  },
  // 提交按钮文本
  submitText: {
    type: String,
    default: "提交",
  },
  // 提交加载状态
  submitLoading: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(["update:modelValue", "submit", "reset"]);

// Refs
const formRef = ref();

// 表单数据
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 表单验证规则
const formRules = computed(() => {
  const rules = { ...props.rules };

  // 根据表单项配置生成验证规则
  props.formItems.forEach((item) => {
    if (item.required && !rules[item.prop]) {
      rules[item.prop] = [
        { required: true, message: `请输入${item.label}`, trigger: "blur" },
      ];
    }
  });

  return rules;
});

// 文件上传成功处理
const handleUploadSuccess = (response, file, fileList, prop) => {
  formData.value[prop] = fileList;
};

// 文件移除处理
const handleUploadRemove = (file, fileList, prop) => {
  formData.value[prop] = fileList;
};

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    emit("submit", formData.value);
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

// 表单重置
const handleReset = () => {
  formRef.value.resetFields();
  emit("reset");
};

// 暴露方法
defineExpose({
  formRef,
  validate: () => formRef.value.validate(),
  validateField: (props) => formRef.value.validateField(props),
  resetFields: () => formRef.value.resetFields(),
  clearValidate: (props) => formRef.value.clearValidate(props),
});
</script>

<style lang="scss" scoped>
.form-actions {
  text-align: center;
  margin-top: 20px;
}
</style>
