const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const User = require("../models/User");
const Role = require("../models/Role");
const Permission = require("../models/Permission");
const Department = require("../models/Department");
const config = require("../config/config");

// 连接数据库
console.log("正在连接到MongoDB...");
console.log("连接URL:", config.mongodbUri);

mongoose
  .connect(config.mongodbUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    serverSelectionTimeoutMS: 30000, // 增加超时时间到30秒
    socketTimeoutMS: 45000, // socket超时时间
    connectTimeoutMS: 30000, // 连接超时时间
  })
  .then(() => {
    console.log("MongoDB连接成功");
    return initData();
  })
  .catch((err) => {
    console.error("MongoDB连接失败:", err);
    if (err.name === "MongoServerSelectionError") {
      console.error("无法连接到MongoDB服务器。请确保：");
      console.error("1. MongoDB服务已经启动");
      console.error(
        "2. MongoDB服务器地址正确（当前：" + config.mongodbUri + "）"
      );
      console.error(
        "3. 如果使用本地MongoDB，请确保已经创建了数据目录：C:\\data\\db"
      );
    }
    process.exit(1);
  });

// 初始化数据
async function initData() {
  try {
    // 清空现有数据
    console.log("清空现有数据...");
    await Promise.all([
      Department.deleteMany({}),
      Permission.deleteMany({}),
      Role.deleteMany({}),
      User.deleteMany({}),
    ]);

    console.log("开始创建初始数据...");

    // 创建部门
    console.log("创建部门...");
    const dept = await Department.create({
      name: "总公司",
      parentId: null,
      ancestors: "",
      leader: "系统管理员",
      phone: "15888888888",
      email: "<EMAIL>",
      status: 1,
      sort: 0,
    });
    console.log("部门创建成功:", dept.name);

    // 创建权限
    console.log("创建权限...");
    const permissions = await createPermissions();
    console.log("权限创建成功，共创建", permissions.length, "个权限");

    // 创建角色
    console.log("创建角色...");
    const role = await Role.create({
      name: "超级管理员",
      code: "admin",
      status: 1,
      permissions: permissions.map((p) => p._id),
      remark: "超级管理员角色，拥有所有权限",
    });
    console.log("角色创建成功:", role.name);

    // 创建管理员用户
    console.log("创建管理员用户...");
    const hashedPassword = await bcrypt.hash(
      "123456",
      config.bcrypt.saltRounds
    );
    const admin = await User.create({
      username: "admin",
      password: hashedPassword,
      nickname: "超级管理员",
      email: "<EMAIL>",
      phone: "15888888888",
      avatar: "",
      status: 1,
      deptId: dept._id,
      roles: [role._id],
      remark: "管理员",
    });
    console.log("管理员用户创建成功:", admin.username);

    console.log("\n数据初始化成功！");
    console.log("=================================");
    console.log("管理员账号：", admin.username);
    console.log("管理员密码：123456");
    console.log("=================================\n");

    process.exit(0);
  } catch (error) {
    console.error("数据初始化失败:", error);
    process.exit(1);
  }
}

// 创建权限数据
async function createPermissions() {
  const permissionsData = [
    // 系统管理
    {
      name: "系统管理",
      type: "menu",
      parentId: null,
      path: "/system",
      component: "Layout",
      code: "system",
      icon: "system",
      sort: 1,
      status: 1,
    },
    // 用户管理
    {
      name: "用户管理",
      type: "menu",
      parentId: null,
      path: "/system/user",
      component: "system/user/index",
      code: "system:user:list",
      icon: "user",
      sort: 1,
      status: 1,
    },
    {
      name: "用户查询",
      type: "button",
      parentId: null,
      code: "system:user:query",
      sort: 1,
      status: 1,
    },
    {
      name: "用户新增",
      type: "button",
      parentId: null,
      code: "system:user:create",
      sort: 2,
      status: 1,
    },
    {
      name: "用户修改",
      type: "button",
      parentId: null,
      code: "system:user:update",
      sort: 3,
      status: 1,
    },
    {
      name: "用户删除",
      type: "button",
      parentId: null,
      code: "system:user:delete",
      sort: 4,
      status: 1,
    },
    // 角色管理
    {
      name: "角色管理",
      type: "menu",
      parentId: null,
      path: "/system/role",
      component: "system/role/index",
      code: "system:role:list",
      icon: "peoples",
      sort: 2,
      status: 1,
    },
    {
      name: "角色查询",
      type: "button",
      parentId: null,
      code: "system:role:query",
      sort: 1,
      status: 1,
    },
    {
      name: "角色新增",
      type: "button",
      parentId: null,
      code: "system:role:create",
      sort: 2,
      status: 1,
    },
    {
      name: "角色修改",
      type: "button",
      parentId: null,
      code: "system:role:update",
      sort: 3,
      status: 1,
    },
    {
      name: "角色删除",
      type: "button",
      parentId: null,
      code: "system:role:delete",
      sort: 4,
      status: 1,
    },
    // 权限管理
    {
      name: "权限管理",
      type: "menu",
      parentId: null,
      path: "/system/permission",
      component: "system/permission/index",
      code: "system:permission:list",
      icon: "tree",
      sort: 3,
      status: 1,
    },
    {
      name: "权限查询",
      type: "button",
      parentId: null,
      code: "system:permission:query",
      sort: 1,
      status: 1,
    },
    {
      name: "权限新增",
      type: "button",
      parentId: null,
      code: "system:permission:create",
      sort: 2,
      status: 1,
    },
    {
      name: "权限修改",
      type: "button",
      parentId: null,
      code: "system:permission:update",
      sort: 3,
      status: 1,
    },
    {
      name: "权限删除",
      type: "button",
      parentId: null,
      code: "system:permission:delete",
      sort: 4,
      status: 1,
    },
    // 部门管理
    {
      name: "部门管理",
      type: "menu",
      parentId: null,
      path: "/system/dept",
      component: "system/dept/index",
      code: "system:dept:list",
      icon: "tree",
      sort: 4,
      status: 1,
    },
    {
      name: "部门查询",
      type: "button",
      parentId: null,
      code: "system:dept:query",
      sort: 1,
      status: 1,
    },
    {
      name: "部门新增",
      type: "button",
      parentId: null,
      code: "system:dept:create",
      sort: 2,
      status: 1,
    },
    {
      name: "部门修改",
      type: "button",
      parentId: null,
      code: "system:dept:update",
      sort: 3,
      status: 1,
    },
    {
      name: "部门删除",
      type: "button",
      parentId: null,
      code: "system:dept:delete",
      sort: 4,
      status: 1,
    },
  ];

  const createdPermissions = [];

  // 先创建所有权限
  for (const permData of permissionsData) {
    const permission = await Permission.create(permData);
    createdPermissions.push(permission);
  }

  // 设置父子关系
  const systemMenu = createdPermissions.find((p) => p.code === "system");
  const userMenu = createdPermissions.find(
    (p) => p.code === "system:user:list"
  );
  const roleMenu = createdPermissions.find(
    (p) => p.code === "system:role:list"
  );
  const permMenu = createdPermissions.find(
    (p) => p.code === "system:permission:list"
  );
  const deptMenu = createdPermissions.find(
    (p) => p.code === "system:dept:list"
  );

  // 设置菜单的父级关系
  if (systemMenu) {
    await Permission.updateMany(
      {
        code: {
          $in: [
            "system:user:list",
            "system:role:list",
            "system:permission:list",
            "system:dept:list",
          ],
        },
      },
      { parentId: systemMenu._id }
    );

    // 设置按钮的父级关系
    if (userMenu) {
      await Permission.updateMany(
        {
          code: {
            $in: [
              "system:user:query",
              "system:user:create",
              "system:user:update",
              "system:user:delete",
            ],
          },
        },
        { parentId: userMenu._id }
      );
    }

    if (roleMenu) {
      await Permission.updateMany(
        {
          code: {
            $in: [
              "system:role:query",
              "system:role:create",
              "system:role:update",
              "system:role:delete",
            ],
          },
        },
        { parentId: roleMenu._id }
      );
    }

    if (permMenu) {
      await Permission.updateMany(
        {
          code: {
            $in: [
              "system:permission:query",
              "system:permission:create",
              "system:permission:update",
              "system:permission:delete",
            ],
          },
        },
        { parentId: permMenu._id }
      );
    }

    if (deptMenu) {
      await Permission.updateMany(
        {
          code: {
            $in: [
              "system:dept:query",
              "system:dept:create",
              "system:dept:update",
              "system:dept:delete",
            ],
          },
        },
        { parentId: deptMenu._id }
      );
    }
  }

  return await Permission.find({});
}
