const Department = require("../models/Department");
const User = require("../models/User");
const { success, error } = require("../utils/response");

/**
 * 获取部门树
 * @route GET /api/departments/tree
 * @access Private
 */
const getDepartmentTree = async (req, res) => {
  try {
    // 查询所有部门
    const departments = await Department.find().sort({ sort: 1 });

    // 构建部门树
    const departmentTree = buildDepartmentTree(departments);

    return success(res, "获取部门树成功", departmentTree);
  } catch (err) {
    console.error("获取部门树错误:", err.message);
    return error(res, "获取部门树失败: " + err.message, 500);
  }
};

/**
 * 获取单个部门信息
 * @route GET /api/departments/:id
 * @access Private
 */
const getDepartmentById = async (req, res) => {
  try {
    const department = await Department.findById(req.params.id);

    if (!department) {
      return error(res, "部门不存在", 404);
    }

    return success(res, "获取部门信息成功", department);
  } catch (err) {
    console.error("获取部门信息错误:", err.message);
    return error(res, "获取部门信息失败: " + err.message, 500);
  }
};

/**
 * 创建部门
 * @route POST /api/departments
 * @access Private
 */
const createDepartment = async (req, res) => {
  try {
    const { name, parentId, leader, phone, email, status, sort } = req.body;

    // 创建新部门
    const department = new Department({
      name,
      parentId,
      leader,
      phone,
      email,
      status,
      sort,
    });

    // 如果有父部门，设置ancestors
    if (parentId) {
      const parent = await Department.findById(parentId);
      if (!parent) {
        return error(res, "父部门不存在", 400);
      }

      department.ancestors = parent.ancestors
        ? `${parent.ancestors},${parentId}`
        : parentId;
    }

    // 保存部门
    await department.save();

    return success(res, "创建部门成功", department, 201);
  } catch (err) {
    console.error("创建部门错误:", err.message);
    return error(res, "创建部门失败: " + err.message, 500);
  }
};

/**
 * 更新部门
 * @route PUT /api/departments/:id
 * @access Private
 */
const updateDepartment = async (req, res) => {
  try {
    const { name, parentId, leader, phone, email, status, sort } = req.body;

    // 查找部门
    const department = await Department.findById(req.params.id);
    if (!department) {
      return error(res, "部门不存在", 404);
    }

    // 检查是否将部门的父级设置为自己
    if (parentId && parentId.toString() === req.params.id) {
      return error(res, "父级部门不能是自己", 400);
    }

    // 检查是否将部门的父级设置为其子部门
    if (parentId) {
      const childDepts = await Department.find({
        ancestors: { $regex: req.params.id },
      });
      const childDeptIds = childDepts.map((dept) => dept._id.toString());

      if (childDeptIds.includes(parentId.toString())) {
        return error(res, "父级部门不能是其子部门", 400);
      }
    }

    // 更新部门信息
    if (name !== undefined) department.name = name;
    if (leader !== undefined) department.leader = leader;
    if (phone !== undefined) department.phone = phone;
    if (email !== undefined) department.email = email;
    if (status !== undefined) department.status = status;
    if (sort !== undefined) department.sort = sort;

    // 如果更新了父部门，需要更新ancestors
    if (parentId !== undefined && parentId !== department.parentId) {
      department.parentId = parentId;

      if (parentId) {
        const parent = await Department.findById(parentId);
        if (!parent) {
          return error(res, "父部门不存在", 400);
        }

        department.ancestors = parent.ancestors
          ? `${parent.ancestors},${parentId}`
          : parentId;
      } else {
        department.ancestors = "";
      }

      // 更新所有子部门的ancestors
      const childDepts = await Department.find({
        ancestors: { $regex: req.params.id },
      });
      const updatePromises = childDepts.map(async (childDept) => {
        const childAncestors = childDept.ancestors.split(",");
        const index = childAncestors.indexOf(req.params.id);

        // 构建新的ancestors
        const newAncestors = [
          ...childAncestors.slice(0, index),
          req.params.id,
          ...(department.ancestors ? department.ancestors.split(",") : []),
        ]
          .filter(Boolean)
          .join(",");

        childDept.ancestors = newAncestors;
        return childDept.save();
      });

      await Promise.all(updatePromises);
    }

    // 保存更新
    await department.save();

    return success(res, "更新部门成功", department);
  } catch (err) {
    console.error("更新部门错误:", err.message);
    return error(res, "更新部门失败: " + err.message, 500);
  }
};

/**
 * 删除部门
 * @route DELETE /api/departments/:id
 * @access Private
 */
const deleteDepartment = async (req, res) => {
  try {
    const department = await Department.findById(req.params.id);
    if (!department) {
      return error(res, "部门不存在", 404);
    }

    // 检查是否有子部门
    const childrenCount = await Department.countDocuments({
      parentId: req.params.id,
    });
    if (childrenCount > 0) {
      return error(res, "该部门下有子部门，无法删除", 400);
    }

    // 检查是否有用户属于此部门
    const usersInDept = await User.countDocuments({ deptId: req.params.id });
    if (usersInDept > 0) {
      return error(res, `该部门下有 ${usersInDept} 个用户，无法删除`, 400);
    }

    await department.remove();

    return success(res, "删除部门成功");
  } catch (err) {
    console.error("删除部门错误:", err.message);
    return error(res, "删除部门失败: " + err.message, 500);
  }
};

/**
 * 更新部门状态
 * @route PATCH /api/departments/:id/status
 * @access Private
 */
const updateDepartmentStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (status === undefined || ![0, 1].includes(status)) {
      return error(res, "请提供有效的状态值", 400);
    }

    const department = await Department.findById(req.params.id);
    if (!department) {
      return error(res, "部门不存在", 404);
    }

    department.status = status;
    await department.save();

    return success(res, `部门状态已${status === 1 ? "启用" : "禁用"}`);
  } catch (err) {
    console.error("更新部门状态错误:", err.message);
    return error(res, "更新部门状态失败: " + err.message, 500);
  }
};

/**
 * 构建部门树
 * @param {Array} departments - 部门列表
 * @returns {Array} 部门树
 */
const buildDepartmentTree = (departments) => {
  // 创建一个映射表，用于快速查找部门
  const departmentMap = {};
  departments.forEach((department) => {
    departmentMap[department._id] = {
      ...department.toObject(),
      children: [],
    };
  });

  // 构建树结构
  const departmentTree = [];
  departments.forEach((department) => {
    const departmentId = department._id;
    const parentId = department.parentId;

    if (!parentId) {
      // 如果没有父级，则为顶级部门
      departmentTree.push(departmentMap[departmentId]);
    } else if (departmentMap[parentId]) {
      // 如果有父级，且父级存在，则添加到父级的children中
      departmentMap[parentId].children.push(departmentMap[departmentId]);
    }
  });

  return departmentTree;
};

/**
 * 获取部门列表
 * @route GET /api/departments
 * @access Private
 */
const getDepartments = async (req, res) => {
  try {
    const { status } = req.query;
    const query = {};

    // 如果提供了状态参数，添加到查询条件
    if (status !== undefined && status !== "") {
      const statusNum = parseInt(status);
      if (!isNaN(statusNum) && (statusNum === 0 || statusNum === 1)) {
        query.status = statusNum;
      }
    }

    // 查询部门列表
    const departments = await Department.find(query).sort({ sort: 1 });

    return success(res, "获取部门列表成功", departments);
  } catch (err) {
    console.error("获取部门列表错误:", err.message);
    return error(res, "获取部门列表失败: " + err.message, 500);
  }
};

module.exports = {
  getDepartmentTree,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  updateDepartmentStatus,
  getDepartments,
};
