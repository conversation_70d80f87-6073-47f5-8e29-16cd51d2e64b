/**
 * 格式化日期时间
 * @param {Date|string|number} date 日期
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = "YYYY-MM-DD HH:mm:ss") {
  if (!date) return "";

  const d = new Date(date);
  if (isNaN(d.getTime())) return "";

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  const hours = String(d.getHours()).padStart(2, "0");
  const minutes = String(d.getMinutes()).padStart(2, "0");
  const seconds = String(d.getSeconds()).padStart(2, "0");

  return format
    .replace("YYYY", year)
    .replace("MM", month)
    .replace("DD", day)
    .replace("HH", hours)
    .replace("mm", minutes)
    .replace("ss", seconds);
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return "0 B";

  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
}

/**
 * 格式化数字
 * @param {number} num 数字
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的数字
 */
export function formatNumber(num, decimals = 0) {
  if (num === null || num === undefined || isNaN(num)) return "0";

  return Number(num).toLocaleString("zh-CN", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
}

/**
 * 格式化百分比
 * @param {number} num 数字
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的百分比
 */
export function formatPercent(num, decimals = 2) {
  if (num === null || num === undefined || isNaN(num)) return "0%";

  return (Number(num) * 100).toFixed(decimals) + "%";
}

/**
 * 格式化货币
 * @param {number} amount 金额
 * @param {string} currency 货币符号
 * @returns {string} 格式化后的货币
 */
export function formatCurrency(amount, currency = "¥") {
  if (amount === null || amount === undefined || isNaN(amount))
    return currency + "0.00";

  return (
    currency +
    Number(amount).toLocaleString("zh-CN", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  );
}

/**
 * 格式化时间间隔
 * @param {Date|string|number} startTime 开始时间
 * @param {Date|string|number} endTime 结束时间
 * @returns {string} 格式化后的时间间隔
 */
export function formatDuration(startTime, endTime = new Date()) {
  const start = new Date(startTime);
  const end = new Date(endTime);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) return "";

  const diff = Math.abs(end.getTime() - start.getTime());
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days}天`;
  if (hours > 0) return `${hours}小时`;
  if (minutes > 0) return `${minutes}分钟`;
  return `${seconds}秒`;
}

/**
 * 格式化相对时间
 * @param {Date|string|number} date 日期
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(date) {
  if (!date) return "";

  const d = new Date(date);
  if (isNaN(d.getTime())) return "";

  const now = new Date();
  const diff = now.getTime() - d.getTime();
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30);
  const years = Math.floor(months / 12);

  if (years > 0) return `${years}年前`;
  if (months > 0) return `${months}个月前`;
  if (days > 0) return `${days}天前`;
  if (hours > 0) return `${hours}小时前`;
  if (minutes > 0) return `${minutes}分钟前`;
  if (seconds > 0) return `${seconds}秒前`;
  return "刚刚";
}

/**
 * 格式化手机号
 * @param {string} phone 手机号
 * @returns {string} 格式化后的手机号
 */
export function formatPhone(phone) {
  if (!phone) return "";

  const cleaned = phone.replace(/\D/g, "");
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, "$1 $2 $3");
  }
  return phone;
}

/**
 * 格式化身份证号
 * @param {string} idCard 身份证号
 * @param {boolean} mask 是否遮蔽中间部分
 * @returns {string} 格式化后的身份证号
 */
export function formatIdCard(idCard, mask = true) {
  if (!idCard) return "";

  if (mask && idCard.length === 18) {
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, "$1********$2");
  }

  return idCard;
}

/**
 * 格式化银行卡号
 * @param {string} cardNumber 银行卡号
 * @param {boolean} mask 是否遮蔽中间部分
 * @returns {string} 格式化后的银行卡号
 */
export function formatBankCard(cardNumber, mask = true) {
  if (!cardNumber) return "";

  const cleaned = cardNumber.replace(/\D/g, "");

  if (mask && cleaned.length >= 8) {
    const start = cleaned.slice(0, 4);
    const end = cleaned.slice(-4);
    const middle = "*".repeat(cleaned.length - 8);
    return `${start} ${middle} ${end}`.replace(/(.{4})/g, "$1 ").trim();
  }

  return cleaned.replace(/(.{4})/g, "$1 ").trim();
}

/**
 * 截断文本
 * @param {string} text 文本
 * @param {number} length 最大长度
 * @param {string} suffix 后缀
 * @returns {string} 截断后的文本
 */
export function truncateText(text, length = 50, suffix = "...") {
  if (!text) return "";

  if (text.length <= length) return text;

  return text.slice(0, length) + suffix;
}

/**
 * 高亮关键词
 * @param {string} text 文本
 * @param {string} keyword 关键词
 * @param {string} className CSS类名
 * @returns {string} 高亮后的HTML
 */
export function highlightKeyword(text, keyword, className = "highlight") {
  if (!text || !keyword) return text;

  const regex = new RegExp(`(${keyword})`, "gi");
  return text.replace(regex, `<span class="${className}">$1</span>`);
}
