const express = require("express");
const router = express.Router();
const {
  getDepartmentTree,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  updateDepartmentStatus,
  getDepartments,
} = require("../controllers/departmentController");
const { authenticate, authorize } = require("../middleware/auth");
const { validate } = require("../middleware/validator");
const {
  createDepartmentValidator,
  updateDepartmentValidator,
  departmentStatusValidator,
} = require("../validators/departmentValidator");

// 所有部门路由都需要认证
router.use(authenticate);

// 获取部门列表
router.get("/", authorize("system:department:list"), getDepartments);

// 获取部门树
router.get("/tree", authorize("system:department:list"), getDepartmentTree);

// 获取单个部门
router.get("/:id", authorize("system:department:query"), getDepartmentById);

// 创建部门
router.post(
  "/",
  authorize("system:department:create"),
  createDepartmentValidator,
  validate,
  createDepartment
);

// 更新部门
router.put(
  "/:id",
  authorize("system:department:update"),
  updateDepartmentValidator,
  validate,
  updateDepartment
);

// 删除部门
router.delete("/:id", authorize("system:department:delete"), deleteDepartment);

// 更新部门状态
router.patch(
  "/:id/status",
  authorize("system:department:update"),
  departmentStatusValidator,
  validate,
  updateDepartmentStatus
);

module.exports = router;
