version: '3.8'

services:
  # MongoDB 数据库
  mongodb:
    image: mongo:6.0
    container_name: ruo-mongodb-dev
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: ruo-admin
    ports:
      - "27017:27017"
    volumes:
      - mongodb_dev_data:/data/db
    networks:
      - ruo-dev-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: ruo-redis-dev
    restart: unless-stopped
    command: redis-server --requirepass password123
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - ruo-dev-network

  # 后端服务（开发模式）
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: ruo-backend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3000
      MONGODB_URI: ********************************************************************
      JWT_SECRET: dev_jwt_secret_key
      JWT_EXPIRES_IN: 7d
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: password123
    ports:
      - "3000:3000"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    networks:
      - ruo-dev-network
    command: npm run dev

volumes:
  mongodb_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  ruo-dev-network:
    driver: bridge
