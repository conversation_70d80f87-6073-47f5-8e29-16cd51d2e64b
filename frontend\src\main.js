import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import { createPinia } from "pinia";
import ElementPlus from "element-plus";
import * as ElementPlusIcons from "@element-plus/icons-vue";
import "element-plus/dist/index.css";
import "./styles/index.css";
// 导入权限指令
import permission from "./directives/permission";
// 导入全局组件
import GlobalComponents from "./components";

const app = createApp(App);

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIcons)) {
  app.component(key, component);
}

app.use(createPinia());
app.use(router);
app.use(ElementPlus);
app.use(GlobalComponents);
// 注册权限指令
app.directive("permission", permission);

app.mount("#app");
