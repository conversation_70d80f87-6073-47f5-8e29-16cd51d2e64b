<template>
    <div class="app-container">
        <el-card class="box-card">
            <template #header>
                <div class="card-header">
                    <span>菜单管理</span>
                    <el-button type="primary" @click="handleAdd">新增</el-button>
                </div>
            </template>

            <!-- 搜索区域 -->
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
                <el-form-item label="菜单名称" prop="menuName">
                    <el-input v-model="queryParams.menuName" placeholder="请输入菜单名称" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-select v-model="queryParams.status" placeholder="菜单状态" clearable style="width: 200px">
                        <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label"
                            :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">搜索</el-button>
                    <el-button @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <!-- 操作按钮区域 -->
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="info" plain @click="toggleSearch">
                        <el-icon>
                            <Search />
                        </el-icon>
                        {{ showSearch ? '隐藏搜索' : '显示搜索' }}
                    </el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="success" plain @click="handleExpandAll">展开/折叠</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="warning" plain @click="handleRefresh">刷新</el-button>
                </el-col>
            </el-row>

            <!-- 表格区域 -->
            <el-table v-loading="loading" :data="menuList" row-key="id" :default-expand-all="isExpandAll"
                :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
                <el-table-column prop="menuName" label="菜单名称" :show-overflow-tooltip="true" width="180">
                    <template #default="scope">
                        <span>{{ scope.row.menuName }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="icon" label="图标" align="center" width="80">
                    <template #default="scope">
                        <el-icon v-if="scope.row.icon">
                            <component :is="scope.row.icon" />
                        </el-icon>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="orderNum" label="排序" width="80" align="center" />
                <el-table-column prop="perms" label="权限标识" :show-overflow-tooltip="true" />
                <el-table-column prop="path" label="路由地址" :show-overflow-tooltip="true" />
                <el-table-column prop="component" label="组件路径" :show-overflow-tooltip="true" />
                <el-table-column prop="status" label="状态" width="80" align="center">
                    <template #default="scope">
                        <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
                            {{ scope.row.status === '0' ? '正常' : '停用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
                <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button type="text" @click="handleUpdate(scope.row)" v-if="scope.row.menuType !== 'F'">
                            修改
                        </el-button>
                        <el-button type="text" @click="handleAdd(scope.row)" v-if="scope.row.menuType !== 'F'">
                            新增
                        </el-button>
                        <el-button type="text" style="color: #F56C6C;" @click="handleDelete(scope.row)">
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <!-- 添加或修改菜单对话框 -->
        <el-dialog :title="title" v-model="open" width="680px" append-to-body>
            <el-form ref="menuFormRef" :model="form" :rules="rules" label-width="100px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="上级菜单" prop="parentId">
                            <el-tree-select v-model="form.parentId" :data="menuOptions"
                                :props="{ label: 'menuName', value: 'id' }" value-key="id" placeholder="选择上级菜单"
                                check-strictly />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="菜单类型" prop="menuType">
                            <el-radio-group v-model="form.menuType">
                                <el-radio label="M">目录</el-radio>
                                <el-radio label="C">菜单</el-radio>
                                <el-radio label="F">按钮</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="form.menuType !== 'F'">
                        <el-form-item label="菜单图标" prop="icon">
                            <el-input v-model="form.icon" placeholder="请选择图标">
                                <template #append>
                                    <el-button @click="showIconSelect = true">选择</el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="菜单名称" prop="menuName">
                            <el-input v-model="form.menuName" placeholder="请输入菜单名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="显示排序" prop="orderNum">
                            <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="form.menuType !== 'F'">
                        <el-form-item label="路由地址" prop="path">
                            <el-input v-model="form.path" placeholder="请输入路由地址" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="form.menuType === 'C'">
                        <el-form-item label="组件路径" prop="component">
                            <el-input v-model="form.component" placeholder="请输入组件路径" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="form.menuType !== 'M'">
                        <el-form-item label="权限标识" prop="perms">
                            <el-input v-model="form.perms" placeholder="请输入权限标识" maxlength="50" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="form.menuType !== 'F'">
                        <el-form-item label="路由参数" prop="query">
                            <el-input v-model="form.query" placeholder="请输入路由参数" maxlength="128" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="form.menuType === 'C'">
                        <el-form-item label="是否外链" prop="isFrame">
                            <el-radio-group v-model="form.isFrame">
                                <el-radio :label="1">是</el-radio>
                                <el-radio :label="0">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="form.menuType !== 'F'">
                        <el-form-item label="菜单状态" prop="status">
                            <el-radio-group v-model="form.status">
                                <el-radio :label="'0'">显示</el-radio>
                                <el-radio :label="'1'">隐藏</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="form.menuType === 'C'">
                        <el-form-item label="是否缓存" prop="isCache">
                            <el-radio-group v-model="form.isCache">
                                <el-radio :label="1">缓存</el-radio>
                                <el-radio :label="0">不缓存</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 图标选择器 -->
        <el-dialog title="图标选择" v-model="showIconSelect" width="800px" append-to-body>
            <div class="icon-container">
                <div v-for="(icon, index) in iconList" :key="index" class="icon-item" @click="selectIcon(icon)">
                    <el-icon>
                        <component :is="icon" />
                    </el-icon>
                    <span>{{ icon }}</span>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import * as ElementPlusIcons from '@element-plus/icons-vue';

// 图标列表
const iconList = computed(() => {
    return Object.keys(ElementPlusIcons);
});

// 查询参数
const queryParams = reactive({
    menuName: '',
    status: ''
});

// 表格数据
const menuList = ref([]);
const loading = ref(false);
const showSearch = ref(true);
const isExpandAll = ref(true);
const statusOptions = [
    { value: '0', label: '正常' },
    { value: '1', label: '停用' }
];

// 弹出层标题
const title = ref('');
const open = ref(false);
const showIconSelect = ref(false);

// 表单参数
const menuFormRef = ref(null);
const form = reactive({
    id: null,
    parentId: 0,
    menuName: '',
    icon: '',
    menuType: 'M',
    orderNum: 0,
    path: '',
    component: '',
    query: '',
    perms: '',
    isFrame: 0,
    isCache: 0,
    status: '0'
});

// 表单校验
const rules = {
    menuName: [
        { required: true, message: '菜单名称不能为空', trigger: 'blur' }
    ],
    orderNum: [
        { required: true, message: '菜单顺序不能为空', trigger: 'blur' }
    ],
    path: [
        { required: true, message: '路由地址不能为空', trigger: 'blur' }
    ]
};

// 菜单选项
const menuOptions = ref([]);

// 生命周期钩子
onMounted(() => {
    // 加载菜单数据
    getList();
});

// 获取菜单列表
const getList = () => {
    loading.value = true;
    // 这里应该调用API获取菜单列表
    // 模拟数据
    setTimeout(() => {
        menuList.value = [
            {
                id: 1,
                parentId: 0,
                menuName: '系统管理',
                icon: 'Setting',
                orderNum: 1,
                path: '/system',
                component: 'Layout',
                perms: '',
                status: '0',
                createTime: '2023-01-01',
                children: [
                    {
                        id: 2,
                        parentId: 1,
                        menuName: '用户管理',
                        icon: 'User',
                        orderNum: 1,
                        path: 'user',
                        component: 'system/user/index',
                        perms: 'system:user:list',
                        status: '0',
                        createTime: '2023-01-01'
                    },
                    {
                        id: 3,
                        parentId: 1,
                        menuName: '角色管理',
                        icon: 'UserFilled',
                        orderNum: 2,
                        path: 'role',
                        component: 'system/role/index',
                        perms: 'system:role:list',
                        status: '0',
                        createTime: '2023-01-01'
                    },
                    {
                        id: 4,
                        parentId: 1,
                        menuName: '菜单管理',
                        icon: 'Menu',
                        orderNum: 3,
                        path: 'menu',
                        component: 'system/menu/index',
                        perms: 'system:menu:list',
                        status: '0',
                        createTime: '2023-01-01'
                    }
                ]
            },
            {
                id: 5,
                parentId: 0,
                menuName: '系统监控',
                icon: 'Monitor',
                orderNum: 2,
                path: '/monitor',
                component: 'Layout',
                perms: '',
                status: '0',
                createTime: '2023-01-01',
                children: [
                    {
                        id: 6,
                        parentId: 5,
                        menuName: '在线用户',
                        icon: 'UserFilled',
                        orderNum: 1,
                        path: 'online',
                        component: 'monitor/online/index',
                        perms: 'monitor:online:list',
                        status: '0',
                        createTime: '2023-01-01'
                    }
                ]
            }
        ];

        // 构建菜单选项
        menuOptions.value = [{
            id: 0,
            menuName: '主目录',
            children: menuList.value
        }];

        loading.value = false;
    }, 300);
};

// 查询操作
const handleQuery = () => {
    getList();
};

// 重置查询操作
const resetQuery = () => {
    queryParams.menuName = '';
    queryParams.status = '';
    handleQuery();
};

// 展开/折叠操作
const handleExpandAll = () => {
    isExpandAll.value = !isExpandAll.value;
};

// 刷新操作
const handleRefresh = () => {
    getList();
};

// 显示/隐藏搜索
const toggleSearch = () => {
    showSearch.value = !showSearch.value;
};

// 新增菜单
const handleAdd = (row) => {
    reset();
    if (row && row.id) {
        form.parentId = row.id;
    }
    open.value = true;
    title.value = '添加菜单';
};

// 修改菜单
const handleUpdate = (row) => {
    console.log("🚀 ~ handleUpdate ~ row:", row)
    reset();
    // 这里应该调用API获取菜单详情
    // 模拟数据
    Object.assign(form, row);
    open.value = true;
    title.value = '修改菜单';
};

// 删除菜单
const handleDelete = (row) => {
    ElMessageBox.confirm(`确认删除菜单: ${row.menuName}?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        // 这里应该调用API删除菜单
        // 模拟删除成功
        ElMessage.success('删除成功');
        getList();
    }).catch(() => { });
};

// 表单重置
const reset = () => {
    form.id = null;
    form.parentId = 0;
    form.menuName = '';
    form.icon = '';
    form.menuType = 'M';
    form.orderNum = 0;
    form.path = '';
    form.component = '';
    form.query = '';
    form.perms = '';
    form.isFrame = 0;
    form.isCache = 0;
    form.status = '0';
};

// 选择图标
const selectIcon = (icon) => {
    form.icon = icon;
    showIconSelect.value = false;
};

// 取消按钮
const cancel = () => {
    open.value = false;
    reset();
};

// 提交表单
const submitForm = async () => {
    try {
        await menuFormRef.value.validate();

        // 这里应该调用API保存菜单
        // 模拟保存成功
        ElMessage.success(form.id ? '修改成功' : '新增成功');
        open.value = false;
        getList();
    } catch (error) {
        console.error('表单验证失败:', error);
    }
};
</script>

<style lang="scss" scoped>
.app-container {
    padding: 20px;
}

.box-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
        font-weight: bold;
    }
}

.mb8 {
    margin-bottom: 8px;
}

.icon-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    max-height: 400px;
    overflow-y: auto;

    .icon-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 80px;
        height: 80px;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
            background-color: #f5f7fa;
            border-color: #409eff;
            color: #409eff;
        }

        .el-icon {
            font-size: 24px;
            margin-bottom: 5px;
        }

        span {
            font-size: 12px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            width: 100%;
            text-align: center;
        }
    }
}
</style>