#!/usr/bin/env node

/**
 * 检查 Vue 3 项目中常见的问题
 */

const fs = require("fs");
const path = require("path");

// 需要检查的文件扩展名
const VUE_EXTENSIONS = [".vue"];
const JS_EXTENSIONS = [".js", ".ts"];

// 问题模式
const ISSUES = [
  {
    name: "v-model on props",
    pattern: /v-model\s*=\s*["']props\./g,
    description: "v-model 不能直接绑定到 props",
    severity: "error",
  },
  {
    name: "v-model:prop-name",
    pattern: /v-model:[\w-]+\s*=\s*["'][\w.]+["']/g,
    description: "检查 v-model 双向绑定的使用",
    severity: "warning",
  },
  {
    name: "reactive props mutation",
    pattern: /^\s*props\.[\w.]+\s*=\s*/gm,
    description: "不能直接修改 props",
    severity: "error",
  },
];

/**
 * 递归获取目录下的所有文件
 */
function getAllFiles(dir, extensions) {
  const files = [];

  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);

    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // 跳过 node_modules 和 .git 目录
        if (!["node_modules", ".git", "dist", "build"].includes(item)) {
          traverse(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(fullPath);
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
  }

  traverse(dir);
  return files;
}

/**
 * 检查文件中的问题
 */
function checkFile(filePath) {
  const content = fs.readFileSync(filePath, "utf-8");
  const issues = [];

  for (const issue of ISSUES) {
    const matches = content.matchAll(issue.pattern);

    for (const match of matches) {
      // 计算行号
      const beforeMatch = content.substring(0, match.index);
      const lineNumber = beforeMatch.split("\n").length;

      issues.push({
        file: filePath,
        line: lineNumber,
        issue: issue.name,
        description: issue.description,
        severity: issue.severity,
        code: match[0],
      });
    }
  }

  return issues;
}

/**
 * 主函数
 */
function main() {
  console.log("🔍 检查 Vue 3 项目中的常见问题...\n");

  const frontendDir = path.join(__dirname, "../frontend");

  if (!fs.existsSync(frontendDir)) {
    console.error("❌ 前端目录不存在:", frontendDir);
    process.exit(1);
  }

  // 获取所有 Vue 文件
  const vueFiles = getAllFiles(frontendDir, VUE_EXTENSIONS);
  console.log(`📁 找到 ${vueFiles.length} 个 Vue 文件`);

  let totalIssues = 0;
  let errorCount = 0;
  let warningCount = 0;

  // 检查每个文件
  for (const file of vueFiles) {
    const issues = checkFile(file);

    if (issues.length > 0) {
      console.log(`\n📄 ${path.relative(frontendDir, file)}:`);

      for (const issue of issues) {
        const icon = issue.severity === "error" ? "❌" : "⚠️";
        console.log(`  ${icon} 行 ${issue.line}: ${issue.description}`);
        console.log(`     代码: ${issue.code}`);

        if (issue.severity === "error") {
          errorCount++;
        } else {
          warningCount++;
        }
        totalIssues++;
      }
    }
  }

  // 输出总结
  console.log("\n" + "=".repeat(50));
  console.log("📊 检查结果总结:");
  console.log(`   总文件数: ${vueFiles.length}`);
  console.log(`   总问题数: ${totalIssues}`);
  console.log(`   错误: ${errorCount}`);
  console.log(`   警告: ${warningCount}`);

  if (totalIssues === 0) {
    console.log("\n✅ 恭喜！没有发现问题。");
  } else {
    console.log("\n💡 建议修复上述问题以确保代码质量。");
  }

  process.exit(errorCount > 0 ? 1 : 0);
}

// 运行检查
if (require.main === module) {
  main();
}
