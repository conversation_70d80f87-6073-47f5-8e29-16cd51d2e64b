const nodemailer = require("nodemailer");
const path = require("path");
const fs = require("fs");

class EmailService {
  constructor() {
    this.transporter = null;
    this.init();
  }

  // 初始化邮件传输器
  init() {
    try {
      const config = {
        host: process.env.SMTP_HOST || "smtp.qq.com",
        port: parseInt(process.env.SMTP_PORT) || 587,
        secure: process.env.SMTP_SECURE === "true", // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      };

      if (!config.auth.user || !config.auth.pass) {
        console.warn("邮件服务未配置，请设置 SMTP_USER 和 SMTP_PASS 环境变量");
        return;
      }

      this.transporter = nodemailer.createTransport(config);

      // 验证连接（异步，不阻塞启动）
      this.transporter.verify((error) => {
        if (error) {
          console.error("邮件服务连接失败:", error.message);
        } else {
          console.log("邮件服务连接成功");
        }
      });
    } catch (error) {
      console.error("邮件服务初始化失败:", error);
    }
  }

  // 发送邮件
  async sendMail(options) {
    if (!this.transporter) {
      throw new Error("邮件服务未初始化");
    }

    const mailOptions = {
      from: process.env.SMTP_FROM || process.env.SMTP_USER,
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html,
      attachments: options.attachments,
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log("邮件发送成功:", info.messageId);
      return {
        success: true,
        messageId: info.messageId,
        response: info.response,
      };
    } catch (error) {
      console.error("邮件发送失败:", error);
      throw error;
    }
  }

  // 发送验证码邮件
  async sendVerificationCode(email, code, type = "register") {
    const templates = {
      register: {
        subject: "注册验证码",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">注册验证码</h2>
            <p>您好！</p>
            <p>您正在注册账户，验证码为：</p>
            <div style="background: #f5f5f5; padding: 20px; text-align: center; margin: 20px 0;">
              <span style="font-size: 24px; font-weight: bold; color: #007bff;">${code}</span>
            </div>
            <p>验证码有效期为10分钟，请及时使用。</p>
            <p>如果这不是您的操作，请忽略此邮件。</p>
            <hr style="margin: 30px 0;">
            <p style="color: #666; font-size: 12px;">此邮件由系统自动发送，请勿回复。</p>
          </div>
        `,
      },
      reset: {
        subject: "密码重置验证码",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">密码重置验证码</h2>
            <p>您好！</p>
            <p>您正在重置密码，验证码为：</p>
            <div style="background: #f5f5f5; padding: 20px; text-align: center; margin: 20px 0;">
              <span style="font-size: 24px; font-weight: bold; color: #dc3545;">${code}</span>
            </div>
            <p>验证码有效期为10分钟，请及时使用。</p>
            <p>如果这不是您的操作，请立即联系管理员。</p>
            <hr style="margin: 30px 0;">
            <p style="color: #666; font-size: 12px;">此邮件由系统自动发送，请勿回复。</p>
          </div>
        `,
      },
    };

    const template = templates[type] || templates.register;

    return await this.sendMail({
      to: email,
      subject: template.subject,
      html: template.html,
    });
  }

  // 发送系统通知邮件
  async sendSystemNotification(email, title, content, level = "info") {
    const colors = {
      info: "#007bff",
      success: "#28a745",
      warning: "#ffc107",
      error: "#dc3545",
    };

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: ${colors[level]};">${title}</h2>
        <div style="background: #f8f9fa; padding: 20px; border-left: 4px solid ${
          colors[level]
        };">
          ${content}
        </div>
        <hr style="margin: 30px 0;">
        <p style="color: #666; font-size: 12px;">
          发送时间: ${new Date().toLocaleString("zh-CN")}
        </p>
        <p style="color: #666; font-size: 12px;">此邮件由系统自动发送，请勿回复。</p>
      </div>
    `;

    return await this.sendMail({
      to: email,
      subject: `[系统通知] ${title}`,
      html,
    });
  }

  // 发送密码重置链接
  async sendPasswordResetLink(email, resetToken, username) {
    const resetUrl = `${
      process.env.FRONTEND_URL || "http://localhost:5173"
    }/reset-password?token=${resetToken}`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">密码重置</h2>
        <p>您好，${username}！</p>
        <p>您请求重置密码，请点击下面的链接重置您的密码：</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" 
             style="background: #007bff; color: white; padding: 12px 30px; 
                    text-decoration: none; border-radius: 5px; display: inline-block;">
            重置密码
          </a>
        </div>
        <p>或者复制以下链接到浏览器地址栏：</p>
        <p style="background: #f5f5f5; padding: 10px; word-break: break-all;">
          ${resetUrl}
        </p>
        <p style="color: #dc3545;">此链接有效期为1小时，请及时使用。</p>
        <p>如果这不是您的操作，请忽略此邮件。</p>
        <hr style="margin: 30px 0;">
        <p style="color: #666; font-size: 12px;">此邮件由系统自动发送，请勿回复。</p>
      </div>
    `;

    return await this.sendMail({
      to: email,
      subject: "密码重置链接",
      html,
    });
  }

  // 发送账户激活邮件
  async sendAccountActivation(email, activationToken, username) {
    const activationUrl = `${
      process.env.FRONTEND_URL || "http://localhost:5173"
    }/activate?token=${activationToken}`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #28a745;">欢迎注册！</h2>
        <p>您好，${username}！</p>
        <p>感谢您注册我们的系统，请点击下面的链接激活您的账户：</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${activationUrl}" 
             style="background: #28a745; color: white; padding: 12px 30px; 
                    text-decoration: none; border-radius: 5px; display: inline-block;">
            激活账户
          </a>
        </div>
        <p>或者复制以下链接到浏览器地址栏：</p>
        <p style="background: #f5f5f5; padding: 10px; word-break: break-all;">
          ${activationUrl}
        </p>
        <p style="color: #dc3545;">此链接有效期为24小时，请及时激活。</p>
        <hr style="margin: 30px 0;">
        <p style="color: #666; font-size: 12px;">此邮件由系统自动发送，请勿回复。</p>
      </div>
    `;

    return await this.sendMail({
      to: email,
      subject: "账户激活",
      html,
    });
  }

  // 批量发送邮件
  async sendBulkMail(recipients, subject, content, options = {}) {
    const results = [];

    for (const recipient of recipients) {
      try {
        const result = await this.sendMail({
          to: recipient.email,
          subject: subject,
          html: content.replace(
            /\{\{name\}\}/g,
            recipient.name || recipient.email
          ),
          ...options,
        });

        results.push({
          email: recipient.email,
          success: true,
          messageId: result.messageId,
        });
      } catch (error) {
        results.push({
          email: recipient.email,
          success: false,
          error: error.message,
        });
      }

      // 添加延迟避免发送过快
      if (options.delay) {
        await new Promise((resolve) => setTimeout(resolve, options.delay));
      }
    }

    return results;
  }

  // 检查邮件服务状态
  async checkStatus() {
    if (!this.transporter) {
      return { status: "disabled", message: "邮件服务未配置" };
    }

    try {
      await this.transporter.verify();
      return { status: "active", message: "邮件服务正常" };
    } catch (error) {
      return { status: "error", message: error.message };
    }
  }
}

// 创建单例实例
const emailService = new EmailService();

module.exports = emailService;
