const express = require("express");
const router = express.Router();
const {
  upload,
  avatarUpload,
  handleUploadError,
  processFileInfo,
} = require("../middleware/upload");
const { authenticate } = require("../middleware/auth");
const path = require("path");
const fs = require("fs");

// 通用文件上传
router.post(
  "/file",
  authenticate,
  upload.single("file"),
  handleUploadError,
  processFileInfo,
  (req, res) => {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        success: false,
        message: "请选择要上传的文件",
      });
    }

    res.json({
      code: 200,
      success: true,
      message: "文件上传成功",
      data: req.fileInfo,
    });
  }
);

// 多文件上传
router.post(
  "/files",
  authenticate,
  upload.array("files", 5),
  handleUploadError,
  processFileInfo,
  (req, res) => {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        code: 400,
        success: false,
        message: "请选择要上传的文件",
      });
    }

    res.json({
      code: 200,
      success: true,
      message: "文件上传成功",
      data: req.filesInfo,
    });
  }
);

// 头像上传
router.post(
  "/avatar",
  authenticate,
  avatarUpload.single("avatar"),
  handleUploadError,
  processFileInfo,
  async (req, res) => {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        success: false,
        message: "请选择要上传的头像",
      });
    }

    try {
      // 更新用户头像信息
      const User = require("../models/User");
      await User.findByIdAndUpdate(req.user.id, {
        avatar: req.fileInfo.url,
      });

      res.json({
        code: 200,
        success: true,
        message: "头像上传成功",
        data: req.fileInfo,
      });
    } catch (error) {
      console.error("更新用户头像失败:", error);
      res.status(500).json({
        code: 500,
        success: false,
        message: "头像上传失败",
      });
    }
  }
);

// 文件删除
router.delete("/file/:filename", authenticate, (req, res) => {
  try {
    const { filename } = req.params;
    const { type = "others" } = req.query;

    const filePath = path.join(__dirname, "../uploads", type, filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        code: 404,
        success: false,
        message: "文件不存在",
      });
    }

    fs.unlinkSync(filePath);

    res.json({
      code: 200,
      success: true,
      message: "文件删除成功",
    });
  } catch (error) {
    console.error("删除文件失败:", error);
    res.status(500).json({
      code: 500,
      success: false,
      message: "删除文件失败",
    });
  }
});

// 获取文件信息
router.get("/file/:filename", (req, res) => {
  try {
    const { filename } = req.params;
    const { type = "others" } = req.query;

    const filePath = path.join(__dirname, "../uploads", type, filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        code: 404,
        success: false,
        message: "文件不存在",
      });
    }

    const stats = fs.statSync(filePath);

    res.json({
      code: 200,
      success: true,
      message: "获取文件信息成功",
      data: {
        filename,
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        url: `/uploads/${type}/${filename}`,
      },
    });
  } catch (error) {
    console.error("获取文件信息失败:", error);
    res.status(500).json({
      code: 500,
      success: false,
      message: "获取文件信息失败",
    });
  }
});

module.exports = router;
