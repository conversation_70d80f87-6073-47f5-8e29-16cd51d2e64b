const User = require("../models/User");
const { generateToken } = require("../utils/jwt");
const { success, error } = require("../utils/response");

/**
 * 用户注册
 * @route POST /api/auth/register
 * @access Public
 */
const register = async (req, res) => {
  try {
    const { username, password, email, phone, nickname } = req.body;

    // 创建新用户
    const user = new User({
      username,
      password,
      email,
      phone,
      nickname,
    });

    // 保存用户
    await user.save();

    // 生成JWT令牌
    const token = generateToken({ id: user._id });

    // 返回用户信息（不包含密码）
    const userResponse = {
      _id: user._id,
      username: user.username,
      email: user.email,
      phone: user.phone,
      nickname: user.nickname,
      status: user.status,
      createTime: user.createTime,
    };

    return success(res, "注册成功", { user: userResponse, token }, 201);
  } catch (err) {
    console.error("注册错误:", err.message);
    return error(res, "注册失败: " + err.message, 500);
  }
};

/**
 * 用户登录
 * @route POST /api/auth/login
 * @access Public
 */
const login = async (req, res) => {
  try {
    const { username, password } = req.body;

    // 测试用户 - 不需要数据库
    if (username === "admin" && password === "123456") {
      // 生成JWT令牌
      const token = generateToken({ id: "test-user-id" });

      // 返回测试用户信息
      const userResponse = {
        _id: "test-user-id",
        username: "admin",
        email: "<EMAIL>",
        phone: "13800138000",
        nickname: "管理员",
        status: 1,
        createTime: new Date().toISOString(),
      };

      return success(res, "登录成功", { user: userResponse, token });
    }

    // 如果MongoDB连接正常，使用数据库查询
    try {
      // 查找用户
      const user = await User.findOne({ username });
      if (!user) {
        return error(res, "用户名或密码错误", 401);
      }

      // 检查用户状态
      if (user.status !== 1) {
        return error(res, "账号已被禁用，请联系管理员", 403);
      }

      // 验证密码
      const isMatch = await user.comparePassword(password);
      if (!isMatch) {
        return error(res, "用户名或密码错误", 401);
      }

      // 生成JWT令牌
      const token = generateToken({ id: user._id });

      // 返回用户信息（不包含密码）
      const userResponse = {
        _id: user._id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        nickname: user.nickname,
        status: user.status,
        createTime: user.createTime,
      };

      return success(res, "登录成功", { user: userResponse, token });
    } catch (dbErr) {
      console.error("数据库查询错误:", dbErr.message);
      return error(res, "登录失败: 数据库连接问题", 500);
    }
  } catch (err) {
    console.error("登录错误:", err.message);
    return error(res, "登录失败: " + err.message, 500);
  }
};

/**
 * 获取当前用户信息
 * @route GET /api/auth/profile
 * @access Private
 */
const getProfile = async (req, res) => {
  try {
    // 测试用户处理
    if (req.user && req.user._id === "test-user-id") {
      const testUser = {
        _id: "test-user-id",
        username: "admin",
        email: "<EMAIL>",
        phone: "13800138000",
        nickname: "管理员",
        status: 1,
        createTime: new Date().toISOString(),
        roles: [
          {
            _id: "test-role-id",
            name: "超级管理员",
            code: "admin",
          },
        ],
        permissions: [
          "system:user:list",
          "system:user:query",
          "system:user:create",
          "system:user:update",
          "system:user:delete",
          "system:role:list",
          "system:role:query",
          "system:role:create",
          "system:role:update",
          "system:role:delete",
          "system:permission:list",
          "system:permission:query",
          "system:permission:create",
          "system:permission:update",
          "system:permission:delete",
          "system:dept:list",
          "system:dept:query",
          "system:dept:create",
          "system:dept:update",
          "system:dept:delete",
        ],
      };
      return success(res, "获取用户信息成功", testUser);
    }

    // 正常数据库查询
    try {
      // 查找用户并填充角色信息
      const user = await User.findById(req.user._id)
        .select("-password")
        .populate({
          path: "roles",
          select: "name code",
          match: { status: 1 },
        })
        .populate({
          path: "deptId",
          select: "name",
          match: { status: 1 },
        });

      if (!user) {
        return error(res, "用户不存在", 404);
      }

      // 提取角色编码
      const roles = user.roles.map((role) => role.code);
      // 提取权限列表（这里可以根据实际需求从角色中获取权限）
      const permissions = [];

      // 返回格式化的用户信息
      return success(res, "获取用户信息成功", {
        user,
        roles,
        permissions,
      });
    } catch (dbErr) {
      console.error("数据库查询错误:", dbErr.message);
      return error(res, "获取用户信息失败: 数据库连接问题", 500);
    }
  } catch (err) {
    console.error("获取用户信息错误:", err.message);
    return error(res, "获取用户信息失败: " + err.message, 500);
  }
};

/**
 * 修改密码
 * @route PUT /api/auth/change-password
 * @access Private
 */
const changePassword = async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;

    // 查找用户
    const user = await User.findById(req.user._id);
    if (!user) {
      return error(res, "用户不存在", 404);
    }

    // 验证旧密码
    const isMatch = await user.comparePassword(oldPassword);
    if (!isMatch) {
      return error(res, "旧密码不正确", 400);
    }

    // 更新密码
    user.password = newPassword;
    await user.save();

    return success(res, "密码修改成功");
  } catch (err) {
    console.error("修改密码错误:", err.message);
    return error(res, "修改密码失败: " + err.message, 500);
  }
};

/**
 * 退出登录
 * @route POST /api/auth/logout
 * @access Private
 */
const logout = async (req, res) => {
  // 由于使用JWT，服务器端不需要做特殊处理
  // 客户端只需要删除本地存储的token即可
  return success(res, "退出登录成功");
};

module.exports = {
  register,
  login,
  getProfile,
  changePassword,
  logout,
};
