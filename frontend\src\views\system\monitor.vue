<template>
  <div class="monitor-container">
    <el-row :gutter="20">
      <!-- 系统概览卡片 -->
      <el-col :span="6" v-for="card in overviewCards" :key="card.key">
        <el-card class="overview-card" :class="card.type">
          <div class="card-content">
            <div class="card-icon">
              <el-icon :size="32">
                <component :is="card.icon" />
              </el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">{{ card.title }}</div>
              <div class="card-value">{{ card.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 系统信息 -->
      <el-col :span="12">
        <el-card title="系统信息">
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
              <el-button type="primary" size="small" @click="refreshSystemInfo">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <el-descriptions :column="1" border>
            <el-descriptions-item label="操作系统">
              {{ systemInfo.system?.platform }} {{ systemInfo.system?.arch }}
            </el-descriptions-item>
            <el-descriptions-item label="主机名">
              {{ systemInfo.system?.hostname }}
            </el-descriptions-item>
            <el-descriptions-item label="系统运行时间">
              {{ formatUptime(systemInfo.system?.uptime) }}
            </el-descriptions-item>
            <el-descriptions-item label="Node.js版本">
              {{ systemInfo.node?.version }}
            </el-descriptions-item>
            <el-descriptions-item label="进程运行时间">
              {{ formatUptime(systemInfo.node?.uptime) }}
            </el-descriptions-item>
            <el-descriptions-item label="CPU核心数">
              {{ systemInfo.cpu?.cores }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <!-- 内存使用情况 -->
      <el-col :span="12">
        <el-card title="内存使用情况">
          <template #header>
            <div class="card-header">
              <span>内存使用情况</span>
              <el-tag :type="getMemoryStatus(systemInfo.memory?.usage)">
                {{ systemInfo.memory?.usage }}%
              </el-tag>
            </div>
          </template>
          
          <div class="memory-info">
            <el-progress 
              :percentage="parseFloat(systemInfo.memory?.usage || 0)" 
              :color="getMemoryColor(systemInfo.memory?.usage)"
              :stroke-width="20"
            />
            <div class="memory-details">
              <div class="memory-item">
                <span>总内存:</span>
                <span>{{ formatBytes(systemInfo.memory?.total) }}</span>
              </div>
              <div class="memory-item">
                <span>已使用:</span>
                <span>{{ formatBytes(systemInfo.memory?.used) }}</span>
              </div>
              <div class="memory-item">
                <span>可用内存:</span>
                <span>{{ formatBytes(systemInfo.memory?.free) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 数据库状态 -->
      <el-col :span="8">
        <el-card title="数据库状态">
          <div class="db-status">
            <div class="status-indicator">
              <el-tag 
                :type="systemInfo.database?.status === 'connected' ? 'success' : 'danger'"
                size="large"
              >
                {{ systemInfo.database?.status === 'connected' ? '已连接' : '未连接' }}
              </el-tag>
            </div>
            <div class="db-info">
              <p><strong>主机:</strong> {{ systemInfo.database?.host }}</p>
              <p><strong>端口:</strong> {{ systemInfo.database?.port }}</p>
              <p><strong>数据库:</strong> {{ systemInfo.database?.name }}</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 应用统计 -->
      <el-col :span="16">
        <el-card title="应用统计">
          <el-row :gutter="16">
            <el-col :span="6" v-for="stat in appStats" :key="stat.key">
              <div class="stat-item">
                <div class="stat-title">{{ stat.title }}</div>
                <div class="stat-value">{{ stat.total }}</div>
                <div class="stat-detail">
                  <span class="active">活跃: {{ stat.active }}</span>
                  <span class="inactive">禁用: {{ stat.inactive }}</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card title="系统操作">
          <el-space>
            <el-button type="primary" @click="clearCache" :loading="clearingCache">
              <el-icon><Delete /></el-icon>
              清理缓存
            </el-button>
            <el-button type="success" @click="downloadLogs">
              <el-icon><Download /></el-icon>
              下载日志
            </el-button>
            <el-button type="warning" @click="checkHealth">
              <el-icon><Monitor /></el-icon>
              健康检查
            </el-button>
          </el-space>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, Delete, Download, Monitor,
  Server, User, Setting, Database
} from '@element-plus/icons-vue'
import { getSystemInfo, getAppStats, clearSystemCache, getLogFiles } from '@/api/system'

// 响应式数据
const systemInfo = ref({})
const appStats = ref([])
const clearingCache = ref(false)

// 概览卡片数据
const overviewCards = reactive([
  {
    key: 'users',
    title: '用户总数',
    value: '0',
    icon: User,
    type: 'primary'
  },
  {
    key: 'roles',
    title: '角色总数', 
    value: '0',
    icon: Setting,
    type: 'success'
  },
  {
    key: 'permissions',
    title: '权限总数',
    value: '0', 
    icon: Database,
    type: 'warning'
  },
  {
    key: 'departments',
    title: '部门总数',
    value: '0',
    icon: Server,
    type: 'info'
  }
])

// 获取系统信息
const getSystemData = async () => {
  try {
    const [sysRes, statsRes] = await Promise.all([
      getSystemInfo(),
      getAppStats()
    ])
    
    systemInfo.value = sysRes.data
    
    // 更新概览卡片
    const stats = statsRes.data
    overviewCards[0].value = stats.users?.total || 0
    overviewCards[1].value = stats.roles?.total || 0  
    overviewCards[2].value = stats.permissions?.total || 0
    overviewCards[3].value = stats.departments?.total || 0
    
    // 更新应用统计
    appStats.value = [
      {
        key: 'users',
        title: '用户',
        total: stats.users?.total || 0,
        active: stats.users?.active || 0,
        inactive: stats.users?.inactive || 0
      },
      {
        key: 'roles', 
        title: '角色',
        total: stats.roles?.total || 0,
        active: stats.roles?.active || 0,
        inactive: stats.roles?.inactive || 0
      },
      {
        key: 'permissions',
        title: '权限', 
        total: stats.permissions?.total || 0,
        active: stats.permissions?.active || 0,
        inactive: stats.permissions?.inactive || 0
      },
      {
        key: 'departments',
        title: '部门',
        total: stats.departments?.total || 0, 
        active: stats.departments?.active || 0,
        inactive: stats.departments?.inactive || 0
      }
    ]
  } catch (error) {
    ElMessage.error('获取系统信息失败')
  }
}

// 刷新系统信息
const refreshSystemInfo = () => {
  getSystemData()
  ElMessage.success('系统信息已刷新')
}

// 清理缓存
const clearCache = async () => {
  try {
    await ElMessageBox.confirm('确定要清理系统缓存吗？', '确认操作', {
      type: 'warning'
    })
    
    clearingCache.value = true
    await clearSystemCache()
    ElMessage.success('缓存清理成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('缓存清理失败')
    }
  } finally {
    clearingCache.value = false
  }
}

// 下载日志
const downloadLogs = async () => {
  try {
    const res = await getLogFiles()
    if (res.data.length === 0) {
      ElMessage.info('暂无日志文件')
      return
    }
    
    // 这里可以实现日志文件列表选择和下载
    ElMessage.info('日志下载功能开发中')
  } catch (error) {
    ElMessage.error('获取日志文件失败')
  }
}

// 健康检查
const checkHealth = async () => {
  try {
    const response = await fetch('/api/v1/system/health')
    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('系统健康状态良好')
    } else {
      ElMessage.warning('系统健康状态异常')
    }
  } catch (error) {
    ElMessage.error('健康检查失败')
  }
}

// 工具函数
const formatUptime = (seconds) => {
  if (!seconds) return '0秒'
  
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  let result = ''
  if (days > 0) result += `${days}天`
  if (hours > 0) result += `${hours}小时`
  if (minutes > 0) result += `${minutes}分钟`
  
  return result || '不到1分钟'
}

const formatBytes = (bytes) => {
  if (!bytes) return '0 B'
  
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const getMemoryStatus = (usage) => {
  const percent = parseFloat(usage || 0)
  if (percent < 60) return 'success'
  if (percent < 80) return 'warning'
  return 'danger'
}

const getMemoryColor = (usage) => {
  const percent = parseFloat(usage || 0)
  if (percent < 60) return '#67c23a'
  if (percent < 80) return '#e6a23c'
  return '#f56c6c'
}

// 生命周期
onMounted(() => {
  getSystemData()
  
  // 每30秒自动刷新一次
  setInterval(getSystemData, 30000)
})
</script>

<style scoped>
.monitor-container {
  padding: 20px;
}

.overview-card {
  margin-bottom: 20px;
}

.card-content {
  display: flex;
  align-items: center;
}

.card-icon {
  margin-right: 16px;
  padding: 12px;
  border-radius: 8px;
  background: rgba(64, 158, 255, 0.1);
}

.overview-card.success .card-icon {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.overview-card.warning .card-icon {
  background: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
}

.overview-card.info .card-icon {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.memory-info {
  margin-top: 16px;
}

.memory-details {
  margin-top: 16px;
}

.memory-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.db-status {
  text-align: center;
}

.status-indicator {
  margin-bottom: 16px;
}

.db-info {
  text-align: left;
}

.db-info p {
  margin: 8px 0;
}

.stat-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-detail {
  font-size: 12px;
}

.stat-detail .active {
  color: #67c23a;
  margin-right: 8px;
}

.stat-detail .inactive {
  color: #f56c6c;
}
</style>