#!/bin/bash

# 若依管理系统部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "Docker 环境检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    mkdir -p backend/logs
    mkdir -p backend/uploads
    chmod 755 backend/logs
    chmod 755 backend/uploads
}

# 复制环境配置文件
setup_env() {
    log_info "设置环境配置..."
    
    # 后端环境配置
    if [ ! -f backend/.env ]; then
        if [ -f backend/.env.example ]; then
            cp backend/.env.example backend/.env
            log_warn "请编辑 backend/.env 文件，配置正确的环境变量"
        else
            log_error "backend/.env.example 文件不存在"
            exit 1
        fi
    fi
    
    # 前端环境配置
    if [ ! -f frontend/.env.production ]; then
        if [ -f frontend/.env.example ]; then
            cp frontend/.env.example frontend/.env.production
            log_warn "请编辑 frontend/.env.production 文件，配置正确的环境变量"
        else
            log_error "frontend/.env.example 文件不存在"
            exit 1
        fi
    fi
}

# 构建和启动服务
deploy_production() {
    log_info "开始生产环境部署..."
    
    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose down
    
    # 构建镜像
    log_info "构建 Docker 镜像..."
    docker-compose build --no-cache
    
    # 启动服务
    log_info "启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    check_services
    
    # 初始化数据库
    init_database
    
    log_info "部署完成！"
    log_info "前端访问地址: http://localhost"
    log_info "后端API地址: http://localhost:3000"
    log_info "API文档地址: http://localhost:3000/api-docs"
}

# 开发环境部署
deploy_development() {
    log_info "开始开发环境部署..."
    
    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose -f docker-compose.dev.yml down
    
    # 启动服务
    log_info "启动开发环境服务..."
    docker-compose -f docker-compose.dev.yml up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 20
    
    # 检查服务状态
    check_services_dev
    
    # 初始化数据库
    init_database_dev
    
    log_info "开发环境部署完成！"
    log_info "后端API地址: http://localhost:3000"
    log_info "API文档地址: http://localhost:3000/api-docs"
    log_info "前端请单独启动: cd frontend && npm run dev"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查后端服务
    if curl -f http://localhost:3000/api/v1/test > /dev/null 2>&1; then
        log_info "后端服务运行正常"
    else
        log_error "后端服务启动失败"
        docker-compose logs backend
        exit 1
    fi
    
    # 检查前端服务
    if curl -f http://localhost > /dev/null 2>&1; then
        log_info "前端服务运行正常"
    else
        log_error "前端服务启动失败"
        docker-compose logs frontend
        exit 1
    fi
}

# 检查开发环境服务状态
check_services_dev() {
    log_info "检查开发环境服务状态..."
    
    # 检查后端服务
    if curl -f http://localhost:3000/api/v1/test > /dev/null 2>&1; then
        log_info "后端服务运行正常"
    else
        log_error "后端服务启动失败"
        docker-compose -f docker-compose.dev.yml logs backend
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    docker-compose exec backend npm run init-db
}

# 初始化开发环境数据库
init_database_dev() {
    log_info "初始化开发环境数据库..."
    docker-compose -f docker-compose.dev.yml exec backend npm run init-db
}

# 显示帮助信息
show_help() {
    echo "若依管理系统部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  prod, production    部署生产环境"
    echo "  dev, development    部署开发环境"
    echo "  stop               停止所有服务"
    echo "  restart            重启所有服务"
    echo "  logs               查看服务日志"
    echo "  clean              清理所有容器和镜像"
    echo "  help               显示此帮助信息"
    echo ""
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose down
    docker-compose -f docker-compose.dev.yml down
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    docker-compose restart
    log_info "服务已重启"
}

# 查看日志
show_logs() {
    docker-compose logs -f
}

# 清理环境
clean_environment() {
    log_warn "这将删除所有容器、镜像和数据卷，确定要继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "清理环境..."
        docker-compose down -v --rmi all
        docker-compose -f docker-compose.dev.yml down -v --rmi all
        docker system prune -f
        log_info "环境清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        prod|production)
            check_docker
            create_directories
            setup_env
            deploy_production
            ;;
        dev|development)
            check_docker
            create_directories
            setup_env
            deploy_development
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs
            ;;
        clean)
            clean_environment
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
