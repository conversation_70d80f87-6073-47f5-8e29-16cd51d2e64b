console.log("🚀 开始启动调试...");

try {
  console.log("1. 加载基础模块...");
  const express = require("express");
  const cors = require("cors");
  const dotenv = require("dotenv");
  console.log("✅ 基础模块加载成功");

  console.log("2. 加载环境变量...");
  dotenv.config();
  console.log("✅ 环境变量加载成功");

  console.log("3. 加载配置文件...");
  const config = require("./config/config");
  console.log("✅ 配置文件加载成功");

  console.log("4. 尝试连接数据库...");
  const connectDB = require("./config/db");
  console.log("✅ 数据库模块加载成功");

  console.log("5. 加载工具模块...");
  const { logger } = require("./utils/logger");
  console.log("✅ 日志工具加载成功");

  console.log("6. 加载中间件...");
  const {
    errorHandler,
    notFoundHandler,
  } = require("./middleware/errorHandler");
  console.log("✅ 错误处理中间件加载成功");

  console.log("7. 加载安全中间件...");
  const {
    securityHeaders,
    requestLogger,
    apiLimiter,
  } = require("./middleware/security");
  console.log("✅ 安全中间件加载成功");

  console.log("8. 加载服务...");
  const cronService = require("./services/cronService");
  console.log("✅ 定时任务服务加载成功");

  const emailService = require("./services/emailService");
  console.log("✅ 邮件服务加载成功");

  console.log("9. 创建Express应用...");
  const app = express();
  console.log("✅ Express应用创建成功");

  console.log("10. 配置中间件...");
  app.use(cors(config.cors));
  app.use(express.json({ limit: "10mb" }));
  app.use(express.urlencoded({ extended: false, limit: "10mb" }));
  console.log("✅ 基础中间件配置成功");

  console.log("11. 连接数据库...");
  connectDB()
    .then(() => {
      console.log("✅ 数据库连接成功");
    })
    .catch((error) => {
      console.log("⚠️ 数据库连接失败:", error.message);
    });

  console.log("12. 添加测试路由和加载API路由...");
  app.get("/", (req, res) => {
    res.json({ success: true, message: "调试服务器运行正常" });
  });

  app.get("/health", (req, res) => {
    res.json({ status: "OK", timestamp: new Date().toISOString() });
  });
  
  // 添加API测试路由
  app.get("/api/v1/test", (req, res) => {
    res.json({
      success: true,
      message: "后端API测试成功",
      data: {
        time: new Date().toISOString(),
      },
    });
  });
  
  // 加载API路由
  app.use(require("./routes"));
  console.log("✅ 测试路由和API路由加载成功");

  console.log("13. 启动服务器...");
  const PORT = config.port;
  const server = app.listen(PORT, () => {
    console.log(`🎉 调试服务器启动成功！`);
    console.log(`📍 端口: ${PORT}`);
    console.log(`🌐 地址: http://localhost:${PORT}`);

    // 测试服务初始化
    console.log("14. 测试服务初始化...");
    try {
      cronService.initDefaultTasks();
      console.log("✅ 定时任务初始化成功");
    } catch (error) {
      console.log("⚠️ 定时任务初始化失败:", error.message);
    }

    emailService
      .checkStatus()
      .then((status) => {
        console.log(`✅ 邮件服务状态: ${status.status} - ${status.message}`);
      })
      .catch((error) => {
        console.log("⚠️ 邮件服务检查失败:", error.message);
      });
  });

  // 错误处理
  server.on("error", (error) => {
    console.error("❌ 服务器启动失败:", error);
  });

  // 优雅关闭
  process.on("SIGINT", () => {
    console.log("\n🛑 收到关闭信号，正在关闭服务器...");
    server.close(() => {
      console.log("✅ 服务器已关闭");
      process.exit(0);
    });
  });
} catch (error) {
  console.error("❌ 启动过程中发生错误:", error);
  console.error("错误堆栈:", error.stack);
  process.exit(1);
}
