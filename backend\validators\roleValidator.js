const { body } = require("express-validator");
const Role = require("../models/Role");

/**
 * 角色创建验证规则
 */
const createRoleValidator = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("角色名称不能为空")
    .isLength({ max: 50 })
    .withMessage("角色名称长度不能超过50个字符")
    .custom(async (value) => {
      const role = await Role.findOne({ name: value });
      if (role) {
        throw new Error("角色名称已存在");
      }
      return true;
    }),

  body("code")
    .trim()
    .notEmpty()
    .withMessage("角色编码不能为空")
    .isLength({ max: 50 })
    .withMessage("角色编码长度不能超过50个字符")
    .custom(async (value) => {
      const role = await Role.findOne({ code: value });
      if (role) {
        throw new Error("角色编码已存在");
      }
      return true;
    }),

  body("status").optional().isIn([0, 1]).withMessage("状态值无效"),

  body("permissions").optional().isArray().withMessage("权限必须是数组格式"),

  body("remark")
    .optional()
    .isLength({ max: 500 })
    .withMessage("备注长度不能超过500个字符"),
];

/**
 * 角色更新验证规则
 */
const updateRoleValidator = [
  body("name")
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage("角色名称长度不能超过50个字符")
    .custom(async (value, { req }) => {
      if (!value) return true;
      const role = await Role.findOne({
        name: value,
        _id: { $ne: req.params.id },
      });
      if (role) {
        throw new Error("角色名称已存在");
      }
      return true;
    }),

  body("code")
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage("角色编码长度不能超过50个字符")
    .custom(async (value, { req }) => {
      if (!value) return true;
      const role = await Role.findOne({
        code: value,
        _id: { $ne: req.params.id },
      });
      if (role) {
        throw new Error("角色编码已存在");
      }
      return true;
    }),

  body("status").optional().isIn([0, 1]).withMessage("状态值无效"),

  body("permissions").optional().isArray().withMessage("权限必须是数组格式"),

  body("remark")
    .optional()
    .isLength({ max: 500 })
    .withMessage("备注长度不能超过500个字符"),
];

/**
 * 角色状态验证规则
 */
const roleStatusValidator = [
  body("status").isIn([0, 1]).withMessage("状态值必须为0或1"),
];

module.exports = {
  createRoleValidator,
  updateRoleValidator,
  roleStatusValidator,
};
