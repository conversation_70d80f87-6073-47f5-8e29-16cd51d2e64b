const express = require("express");
const cors = require("cors");
const morgan = require("morgan");
const dotenv = require("dotenv");
const path = require("path");
const config = require("./config/config");
const connectDB = require("./config/db");
const { logger, httpLogger } = require("./utils/logger");
const { errorHandler, notFoundHandler } = require("./middleware/errorHandler");
const {
  swaggerSpec,
  swaggerUi,
  swaggerUiOptions,
} = require("./config/swagger");

// 导入新的中间件和服务
const {
  securityHeaders,
  requestLogger,
  apiLimiter,
} = require("./middleware/security");
const cronService = require("./services/cronService");
const emailService = require("./services/emailService");

// 加载环境变量
dotenv.config();

// 创建Express应用
const app = express();

// 安全中间件
app.use(securityHeaders);
app.use(requestLogger);

// 基础中间件
app.use(cors(config.cors));
app.use(httpLogger); // 使用自定义HTTP日志中间件
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: false, limit: "10mb" }));

// API频率限制
app.use("/api/", apiLimiter);

// 静态文件服务
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// 连接MongoDB数据库
connectDB();

// API文档路由
app.use(
  "/api-docs",
  swaggerUi.serve,
  swaggerUi.setup(swaggerSpec, swaggerUiOptions)
);

// 添加一个测试路由
app.get("/api/v1/test", (req, res) => {
  res.json({
    success: true,
    message: "后端API测试成功",
    data: {
      time: new Date().toISOString(),
    },
  });
});

// 路由
app.use(require("./routes"));

// 404处理中间件
app.use(notFoundHandler);

// 全局错误处理中间件
app.use(errorHandler);

// 启动服务器
const PORT = config.port;

// 添加全局错误处理
process.on("uncaughtException", (error) => {
  console.error("未捕获的异常:", error);
  logger.error("未捕获的异常:", error);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("未处理的Promise拒绝:", reason);
  logger.error("未处理的Promise拒绝:", reason);
});

const server = app.listen(PORT, () => {
  logger.info(`服务器运行在端口 ${PORT}`);
  logger.info(`环境: ${config.env}`);
  logger.info(`数据库: ${config.mongodbUri}`);

  // 安全地初始化定时任务
  try {
    cronService.initDefaultTasks();
    logger.info("定时任务初始化完成");
  } catch (error) {
    logger.error("定时任务初始化失败:", error);
  }

  // 安全地检查邮件服务状态
  emailService
    .checkStatus()
    .then((status) => {
      logger.info(`邮件服务状态: ${status.status} - ${status.message}`);
    })
    .catch((error) => {
      logger.error("邮件服务状态检查失败:", error);
    });

  if (config.env !== "production") {
    console.log(`API文档地址: http://localhost:${PORT}/api-docs`);
  }
});

// 优雅关闭
process.on("SIGTERM", () => {
  console.log("收到SIGTERM信号，正在关闭服务器...");
  server.close(() => {
    console.log("服务器已关闭");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  console.log("收到SIGINT信号，正在关闭服务器...");
  server.close(() => {
    console.log("服务器已关闭");
    process.exit(0);
  });
});

module.exports = app;
