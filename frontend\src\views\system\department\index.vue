<template>
    <div class="app-container">
        <el-card class="box-card">
            <template #header>
                <div class="card-header">
                    <span>部门管理</span>
                    <el-button type="primary" @click="handleAdd" v-permission="'system:department:create'">
                        新增部门
                    </el-button>
                </div>
            </template>

            <!-- 部门树 -->
            <el-tree :data="departmentTree" :props="defaultProps" :expand-on-click-node="false" default-expand-all
                node-key="_id">
                <template #default="{ node, data }">
                    <div class="custom-tree-node">
                        <div class="node-label">
                            <span>{{ node.label }}</span>
                            <el-tag size="small" :type="data.status === 1 ? 'success' : 'danger'" effect="dark">
                                {{ data.status === 1 ? '正常' : '停用' }}
                            </el-tag>
                        </div>
                        <div class="node-operations">
                            <el-button type="primary" link @click="handleAdd(data)"
                                v-permission="'system:department:create'">
                                新增
                            </el-button>
                            <el-button type="primary" link @click="handleEdit(data)"
                                v-permission="'system:department:update'">
                                修改
                            </el-button>
                            <el-button type="primary" link @click="handleDelete(data)"
                                v-permission="'system:department:delete'">
                                删除
                            </el-button>
                        </div>
                    </div>
                </template>
            </el-tree>
        </el-card>

        <!-- 部门表单对话框 -->
        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px" @close="handleDialogClose">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="上级部门">
                    <el-tree-select v-model="form.parentId" :data="departmentTree" :props="defaultProps" check-strictly
                        default-expand-all placeholder="请选择上级部门" />
                </el-form-item>
                <el-form-item label="部门名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入部门名称" />
                </el-form-item>
                <el-form-item label="负责人" prop="leader">
                    <el-input v-model="form.leader" placeholder="请输入负责人" />
                </el-form-item>
                <el-form-item label="联系电话" prop="phone">
                    <el-input v-model="form.phone" placeholder="请输入联系电话" />
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                    <el-input v-model="form.email" placeholder="请输入邮箱" />
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input-number v-model="form.sort" :min="0" :max="999" />
                </el-form-item>
                <el-form-item label="状态">
                    <el-radio-group v-model="form.status">
                        <el-radio :label="1">正常</el-radio>
                        <el-radio :label="0">停用</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="handleSubmit">确 定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
    getDepartmentTree,
    createDepartment,
    updateDepartment,
    deleteDepartment
} from '@/api/department'
import usePermission from '@/hooks/usePermission'

const { permission } = usePermission()

// 部门树配置
const departmentTree = ref([])
const defaultProps = {
    children: 'children',
    label: 'name'
}

// 表单相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref(null)
const form = ref({
    name: '',
    parentId: null,
    leader: '',
    phone: '',
    email: '',
    status: 1,
    sort: 0
})

// 表单校验规则
const rules = {
    name: [
        { required: true, message: '请输入部门名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    leader: [
        { max: 20, message: '长度不能超过20个字符', trigger: 'blur' }
    ],
    phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    email: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ],
    sort: [
        { type: 'number', message: '请输入数字', trigger: 'blur' }
    ]
}

// 获取部门树数据
const loadDepartmentTree = async () => {
    try {
        const res = await getDepartmentTree()
        departmentTree.value = res.data
    } catch (error) {
        console.error('获取部门树失败:', error)
    }
}

// 新增部门
const handleAdd = (data) => {
    resetForm()
    dialogTitle.value = '新增部门'
    if (data) {
        form.value.parentId = data._id
    }
    dialogVisible.value = true
}

// 编辑部门
const handleEdit = (data) => {
    resetForm()
    dialogTitle.value = '编辑部门'
    Object.assign(form.value, data)
    dialogVisible.value = true
}

// 删除部门
const handleDelete = async (data) => {
    try {
        await ElMessageBox.confirm('确认要删除该部门吗？', '警告', {
            type: 'warning'
        })
        await deleteDepartment(data._id)
        ElMessage.success('删除成功')
        loadDepartmentTree()
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除部门失败:', error)
        }
    }
}

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return
    try {
        await formRef.value.validate()
        if (form.value._id) {
            await updateDepartment(form.value._id, form.value)
            ElMessage.success('更新成功')
        } else {
            await createDepartment(form.value)
            ElMessage.success('创建成功')
        }
        dialogVisible.value = false
        loadDepartmentTree()
    } catch (error) {
        console.error('提交表单失败:', error)
    }
}

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields()
    }
    form.value = {
        name: '',
        parentId: null,
        leader: '',
        phone: '',
        email: '',
        status: 1,
        sort: 0
    }
}

// 关闭对话框
const handleDialogClose = () => {
    resetForm()
}

// 页面加载时获取数据
onMounted(() => {
    loadDepartmentTree()
})
</script>

<style scoped>
.app-container {
    padding: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
}

.node-label {
    display: flex;
    align-items: center;
    gap: 8px;
}

.node-operations {
    display: flex;
    gap: 8px;
}

:deep(.el-tree-node__content) {
    height: 40px;
}
</style>