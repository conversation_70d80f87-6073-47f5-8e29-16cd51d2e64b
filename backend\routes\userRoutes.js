const express = require("express");
const router = express.Router();
const {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  batchDeleteUsers,
  updateUserStatus,
  resetPassword,
  updateAvatar,
  getUserStats,
  exportUsers,
  batchUpdateStatus,
} = require("../controllers/userController");
const { getProfile } = require("../controllers/authController");
const { authenticate, authorize } = require("../middleware/auth");
const { validate } = require("../middleware/validator");
const {
  createUserValidator,
  updateUserValidator,
  resetPasswordValidator,
  userStatusValidator,
} = require("../validators/userValidator");

// 所有用户路由都需要认证
router.use(authenticate);

// 获取当前用户信息
router.get("/me", getProfile);

// 获取用户列表（分页）
router.get("/", authorize("system:user:list"), getUsers);

// 获取单个用户
router.get("/:id", authorize("system:user:query"), getUserById);

// 创建用户
router.post(
  "/",
  authorize("system:user:create"),
  createUserValidator,
  validate,
  createUser
);

// 更新用户
router.put(
  "/:id",
  authorize("system:user:update"),
  updateUserValidator,
  validate,
  updateUser
);

// 删除用户
router.delete("/:id", authorize("system:user:delete"), deleteUser);

// 批量删除用户
router.delete("/batch", authorize("system:user:delete"), batchDeleteUsers);

// 更新用户状态
router.patch(
  "/:id/status",
  authorize("system:user:update"),
  userStatusValidator,
  validate,
  updateUserStatus
);

// 重置用户密码
router.patch(
  "/:id/password-reset",
  authorize("system:user:update"),
  resetPasswordValidator,
  validate,
  resetPassword
);

// 更新用户头像
router.patch("/:id/avatar", authorize("system:user:update"), updateAvatar);

// 获取用户统计信息
router.get("/stats/overview", authorize("system:user:list"), getUserStats);

// 导出用户数据
router.get("/export/data", authorize("system:user:export"), exportUsers);

// 批量更新用户状态
router.patch(
  "/batch/status",
  authorize("system:user:update"),
  batchUpdateStatus
);

module.exports = router;
