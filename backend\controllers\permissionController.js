const Permission = require("../models/Permission");
const Role = require("../models/Role");
const { success, error } = require("../utils/response");

/**
 * 获取权限树
 * @route GET /api/permissions/tree
 * @access Private
 */
const getPermissionTree = async (req, res) => {
  try {
    // 查询所有权限
    const permissions = await Permission.find().sort({ sort: 1 });

    // 构建权限树
    const permissionTree = buildPermissionTree(permissions);

    return success(res, "获取权限树成功", permissionTree);
  } catch (err) {
    console.error("获取权限树错误:", err.message);
    return error(res, "获取权限树失败: " + err.message, 500);
  }
};

/**
 * 获取菜单树（用于前端路由）
 * @route GET /api/permissions/menu-tree
 * @access Private
 */
const getMenuTree = async (req, res) => {
  try {
    // 查询用户角色
    const user = await req.user.populate({
      path: "roles",
      match: { status: 1 },
      select: "permissions",
    });

    // 收集用户所有角色的权限ID
    const permissionIds = new Set();
    user.roles.forEach((role) => {
      if (role.permissions && role.permissions.length > 0) {
        role.permissions.forEach((permId) => {
          permissionIds.add(permId.toString());
        });
      }
    });

    // 查询用户有权限的菜单
    const query = {
      type: "menu", // 只查询菜单类型
      status: 1, // 只查询启用状态
    };

    // 如果不是超级管理员，则根据权限过滤
    const isAdmin = user.roles.some((role) => role.code === "admin");
    if (!isAdmin && permissionIds.size > 0) {
      query._id = { $in: Array.from(permissionIds) };
    }

    const menus = await Permission.find(query).sort({ sort: 1 });

    // 构建菜单树
    const menuTree = buildPermissionTree(menus);

    return success(res, "获取菜单树成功", menuTree);
  } catch (err) {
    console.error("获取菜单树错误:", err.message);
    return error(res, "获取菜单树失败: " + err.message, 500);
  }
};

/**
 * 获取单个权限信息
 * @route GET /api/permissions/:id
 * @access Private
 */
const getPermissionById = async (req, res) => {
  try {
    const permission = await Permission.findById(req.params.id);

    if (!permission) {
      return error(res, "权限不存在", 404);
    }

    return success(res, "获取权限信息成功", permission);
  } catch (err) {
    console.error("获取权限信息错误:", err.message);
    return error(res, "获取权限信息失败: " + err.message, 500);
  }
};

/**
 * 创建权限
 * @route POST /api/permissions
 * @access Private
 */
const createPermission = async (req, res) => {
  try {
    const {
      name,
      type,
      parentId,
      path,
      component,
      code,
      icon,
      sort,
      status = 1,
    } = req.body;

    // 处理路由路径
    let finalPath = path;
    if (type === "menu" && path) {
      // 确保路径以 / 开头
      finalPath = path.startsWith("/") ? path : "/" + path;

      // 如果有父级，获取父级路径
      if (parentId) {
        const parentPermission = await Permission.findById(parentId);
        if (parentPermission && parentPermission.path) {
          // 避免重复拼接路径
          // 如果子路径以父路径开头，说明已经是完整路径，不需要再拼接
          if (!finalPath.startsWith(parentPermission.path)) {
            // 移除当前路径开头的斜杠，避免多余的斜杠
            const childPath = finalPath.startsWith("/")
              ? finalPath.slice(1)
              : finalPath;
            finalPath = `${parentPermission.path}/${childPath}`;
          }
        }
      }
    }

    // 创建新权限
    const permission = new Permission({
      name,
      type,
      parentId,
      path: finalPath,
      component,
      code,
      icon,
      sort,
      status,
    });

    // 保存权限
    await permission.save();

    return success(res, "创建权限成功", permission, 201);
  } catch (err) {
    console.error("创建权限错误:", err.message);
    return error(res, "创建权限失败: " + err.message, 500);
  }
};

/**
 * 更新权限
 * @route PUT /api/permissions/:id
 * @access Private
 */
const updatePermission = async (req, res) => {
  try {
    const { name, type, parentId, path, component, code, icon, sort, status } =
      req.body;

    const permission = await Permission.findById(req.params.id);
    if (!permission) {
      return error(res, "权限不存在", 404);
    }

    // 处理路由路径
    let finalPath = path;
    if (type === "menu" && path) {
      // 确保路径以 / 开头
      finalPath = path.startsWith("/") ? path : "/" + path;

      // 如果有父级，获取父级路径
      if (parentId) {
        const parentPermission = await Permission.findById(parentId);
        if (parentPermission && parentPermission.path) {
          // 避免重复拼接路径
          // 如果子路径以父路径开头，说明已经是完整路径，不需要再拼接
          if (!finalPath.startsWith(parentPermission.path)) {
            // 移除当前路径开头的斜杠，避免多余的斜杠
            const childPath = finalPath.startsWith("/")
              ? finalPath.slice(1)
              : finalPath;
            finalPath = `${parentPermission.path}/${childPath}`;
          }
        }
      }
    }

    // 更新权限
    permission.name = name;
    permission.type = type;
    permission.parentId = parentId;
    permission.path = finalPath;
    permission.component = component;
    permission.code = code;
    permission.icon = icon;
    permission.sort = sort;
    permission.status = status;

    await permission.save();

    return success(res, "更新权限成功", permission);
  } catch (err) {
    console.error("更新权限错误:", err.message);
    return error(res, "更新权限失败: " + err.message, 500);
  }
};

/**
 * 删除权限
 * @route DELETE /api/permissions/:id
 * @access Private
 */
const deletePermission = async (req, res) => {
  try {
    const permission = await Permission.findById(req.params.id);
    if (!permission) {
      return error(res, "权限不存在", 404);
    }

    // 检查是否有子权限
    const childrenCount = await Permission.countDocuments({
      parentId: req.params.id,
    });
    if (childrenCount > 0) {
      return error(res, "该权限下有子权限，无法删除", 400);
    }

    // 检查是否有角色使用此权限
    const rolesWithPermission = await Role.countDocuments({
      permissions: req.params.id,
    });
    if (rolesWithPermission > 0) {
      return error(
        res,
        `该权限正在被 ${rolesWithPermission} 个角色使用，无法删除`,
        400
      );
    }

    await permission.remove();

    return success(res, "删除权限成功");
  } catch (err) {
    console.error("删除权限错误:", err.message);
    return error(res, "删除权限失败: " + err.message, 500);
  }
};

/**
 * 更新权限状态
 * @route PATCH /api/permissions/:id/status
 * @access Private
 */
const updatePermissionStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (status === undefined || ![0, 1].includes(status)) {
      return error(res, "请提供有效的状态值", 400);
    }

    const permission = await Permission.findById(req.params.id);
    if (!permission) {
      return error(res, "权限不存在", 404);
    }

    permission.status = status;
    await permission.save();

    return success(res, `权限状态已${status === 1 ? "启用" : "禁用"}`);
  } catch (err) {
    console.error("更新权限状态错误:", err.message);
    return error(res, "更新权限状态失败: " + err.message, 500);
  }
};

/**
 * 构建权限树
 * @param {Array} permissions - 权限列表
 * @returns {Array} 权限树
 */
const buildPermissionTree = (permissions) => {
  // 创建一个映射表，用于快速查找权限
  const permissionMap = {};
  permissions.forEach((permission) => {
    permissionMap[permission._id] = {
      ...permission.toObject(),
      children: [],
    };
  });

  // 构建树结构
  const permissionTree = [];
  permissions.forEach((permission) => {
    const permissionId = permission._id;
    const parentId = permission.parentId;

    if (!parentId) {
      // 如果没有父级，则为顶级权限
      permissionTree.push(permissionMap[permissionId]);
    } else if (permissionMap[parentId]) {
      // 如果有父级，且父级存在，则添加到父级的children中
      permissionMap[parentId].children.push(permissionMap[permissionId]);
    }
  });

  return permissionTree;
};

module.exports = {
  getPermissionTree,
  getMenuTree,
  getPermissionById,
  createPermission,
  updatePermission,
  deletePermission,
  updatePermissionStatus,
};
