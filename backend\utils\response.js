/**
 * 统一API响应格式
 */

/**
 * 成功响应
 * @param {Object} res - Express响应对象
 * @param {String} message - 成功消息
 * @param {Object|Array} data - 响应数据
 * @param {Number} statusCode - HTTP状态码
 */
const success = (res, message = "操作成功", data = {}, statusCode = 200) => {
  return res.status(statusCode).json({
    code: 200,
    success: true,
    message,
    data,
  });
};

/**
 * 错误响应
 * @param {Object} res - Express响应对象
 * @param {String} message - 错误消息
 * @param {Number} statusCode - HTTP状态码
 * @param {Object} errors - 详细错误信息
 */
const error = (res, message = "操作失败", statusCode = 400, errors = null) => {
  const responseBody = {
    code: statusCode,
    success: false,
    message,
  };

  if (errors) {
    responseBody.errors = errors;
  }

  return res.status(statusCode).json(responseBody);
};

/**
 * 分页响应
 * @param {Object} res - Express响应对象
 * @param {String} message - 成功消息
 * @param {Array} data - 分页数据
 * @param {Number} total - 总记录数
 * @param {Number} page - 当前页码
 * @param {Number} limit - 每页记录数
 */
const paginate = (
  res,
  message = "获取成功",
  data = [],
  total = 0,
  page = 1,
  limit = 10
) => {
  return res.status(200).json({
    code: 200,
    success: true,
    message,
    data,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit),
    },
  });
};

module.exports = {
  success,
  error,
  paginate,
};
