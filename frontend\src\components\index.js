// 全局组件注册
import Table from "./Table/index.vue";
import Form from "./Form/index.vue";
import Dialog from "./Dialog/index.vue";
import Loading from "./Loading/index.vue";
import Empty from "./Empty/index.vue";

// 组件列表
const components = [
  { name: "RuoTable", component: Table },
  { name: "RuoForm", component: Form },
  { name: "RuoDialog", component: Dialog },
  { name: "RuoLoading", component: Loading },
  { name: "RuoEmpty", component: Empty },
];

// 安装插件
const install = (app) => {
  components.forEach(({ name, component }) => {
    app.component(name, component);
  });
};

export default {
  install,
};

// 按需导出
export { Table, Form, Dialog, Loading, Empty };
