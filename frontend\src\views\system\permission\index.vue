<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 操作按钮区域 -->
      <el-row :gutter="10" class="mb-4">
        <el-col :span="1.5">
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </el-col>
      </el-row>

      <!-- 权限表格组件 -->
      <PermissionTable
        :permission-list="permissionList"
        :loading="loading"
        :expanded-keys="expandedKeys"
        @add="handleAdd"
        @edit="handleEdit"
        @delete="handleDelete"
        @refresh="getList"
      />

      <!-- 权限表单组件 -->
      <PermissionForm
        v-model:visible="dialogVisible"
        :title="dialogTitle"
        :form-data="currentFormData"
        :permission-options="permissionOptions"
        @submit="handleFormSubmit"
        @cancel="handleFormCancel"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getPermissionTree,
  createPermission,
  updatePermission,
  deletePermission,
} from "@/api/permission";
import PermissionTable from "@/components/Permission/PermissionTable.vue";
import PermissionForm from "@/components/Permission/PermissionForm.vue";

// 权限列表数据
const permissionList = ref([]);
const loading = ref(false);
const expandedKeys = ref([]);

// 权限表单
const dialogVisible = ref(false);
const dialogTitle = ref("");
const permissionOptions = ref([]);
const currentFormData = ref({});

// 获取权限树数据
const getList = async () => {
  try {
    loading.value = true;
    const { data } = await getPermissionTree();
    permissionList.value = data;
    permissionOptions.value = [{ _id: "0", name: "顶级权限", children: data }];
  } catch (error) {
    console.error("获取权限列表失败:", error);
    ElMessage.error("获取权限列表失败");
  } finally {
    loading.value = false;
  }
};

// 添加权限
const handleAdd = (row) => {
  currentFormData.value = {};
  if (row) {
    currentFormData.value.parentId = row._id;
  }
  dialogTitle.value = "添加权限";
  dialogVisible.value = true;
};

// 编辑权限
const handleEdit = (row) => {
  currentFormData.value = { ...row };
  dialogTitle.value = "编辑权限";
  dialogVisible.value = true;
};

// 处理表单提交
const handleFormSubmit = async (formData) => {
  try {
    // 处理菜单路由路径
    if (formData.type === "menu") {
      // 如果不是以 / 开头，添加 /
      if (!formData.path.startsWith("/")) {
        formData.path = "/" + formData.path;
      }

      // 如果有父级菜单，需要拼接父级路径
      if (formData.parentId && formData.parentId !== "0") {
        const parentNode = findParentNode(
          permissionList.value,
          formData.parentId
        );
        if (parentNode && parentNode.path) {
          // 避免重复拼接路径
          if (!formData.path.startsWith(parentNode.path)) {
            // 移除当前路径开头的斜杠，避免多余的斜杠
            const childPath = formData.path.startsWith("/")
              ? formData.path.slice(1)
              : formData.path;
            formData.path = `${parentNode.path}/${childPath}`;
          }
        }
      }
    }

    if (formData._id) {
      // 编辑权限
      await updatePermission(formData._id, formData);
      ElMessage.success("更新权限成功");
    } else {
      // 添加权限
      await createPermission(formData);
      ElMessage.success("添加权限成功");
    }

    dialogVisible.value = false;
    getList();
  } catch (error) {
    console.error("操作失败:", error);
    ElMessage.error("操作失败: " + (error.message || "未知错误"));
  }
};

// 处理表单取消
const handleFormCancel = () => {
  dialogVisible.value = false;
};

// 查找父级节点
const findParentNode = (tree, parentId) => {
  for (const node of tree) {
    if (node._id === parentId) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findParentNode(node.children, parentId);
      if (found) return found;
    }
  }
  return null;
};

// 删除权限
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除权限 ${row.name} 吗？`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        await deletePermission(row._id);
        ElMessage.success("删除成功");
        getList();
      } catch (error) {
        console.error("删除失败:", error);
        ElMessage.error("删除失败: " + (error.message || "未知错误"));
      }
    })
    .catch(() => {});
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.dialog-footer {
  text-align: right;
}
</style>
