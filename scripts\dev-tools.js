#!/usr/bin/env node

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

// 命令行参数
const command = process.argv[2];

// 帮助信息
function showHelp() {
  console.log(`
🛠️  开发工具脚本

用法: node scripts/dev-tools.js <command>

可用命令:
  help          显示帮助信息
  check         检查项目状态
  clean         清理项目文件
  reset         重置项目数据
  backup        备份数据库
  restore       恢复数据库
  logs          查看日志
  test          运行测试
  build         构建项目
  deploy        部署项目

示例:
  node scripts/dev-tools.js check
  node scripts/dev-tools.js clean
  node scripts/dev-tools.js reset
`);
}

// 检查项目状态
function checkProject() {
  console.log("🔍 检查项目状态...\n");

  // 检查环境变量文件
  const envFiles = ["backend/.env", "frontend/.env.development"];
  envFiles.forEach((file) => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} 存在`);
    } else {
      console.log(`❌ ${file} 不存在`);
    }
  });

  // 检查依赖
  console.log("\n📦 检查依赖安装状态...");
  const packageDirs = [".", "backend", "frontend"];
  packageDirs.forEach((dir) => {
    const nodeModulesPath = path.join(dir, "node_modules");
    if (fs.existsSync(nodeModulesPath)) {
      console.log(`✅ ${dir}/node_modules 存在`);
    } else {
      console.log(`❌ ${dir}/node_modules 不存在`);
    }
  });

  // 检查数据库连接
  console.log("\n🗄️  检查数据库连接...");
  try {
    execSync("npm run check-data", { stdio: "inherit" });
  } catch (error) {
    console.log("❌ 数据库连接失败");
  }

  // 检查端口占用
  console.log("\n🌐 检查端口占用...");
  try {
    execSync("netstat -an | findstr :3000", { stdio: "pipe" });
    console.log("⚠️  端口3000已被占用");
  } catch (error) {
    console.log("✅ 端口3000可用");
  }

  try {
    execSync("netstat -an | findstr :5173", { stdio: "pipe" });
    console.log("⚠️  端口5173已被占用");
  } catch (error) {
    console.log("✅ 端口5173可用");
  }
}

// 清理项目文件
function cleanProject() {
  console.log("🧹 清理项目文件...\n");

  const cleanDirs = [
    "node_modules",
    "backend/node_modules",
    "frontend/node_modules",
    "backend/logs",
    "backend/uploads/temp",
    "frontend/dist",
  ];

  cleanDirs.forEach((dir) => {
    if (fs.existsSync(dir)) {
      try {
        if (process.platform === "win32") {
          execSync(`rmdir /s /q "${dir}"`, { stdio: "inherit" });
        } else {
          execSync(`rm -rf "${dir}"`, { stdio: "inherit" });
        }
        console.log(`🗑️  删除: ${dir}`);
      } catch (error) {
        console.log(`❌ 删除失败: ${dir}`);
      }
    }
  });

  console.log("\n✅ 清理完成");
}

// 重置项目数据
function resetProject() {
  console.log("🔄 重置项目数据...\n");

  try {
    // 重新初始化数据库
    execSync("npm run init-db", { stdio: "inherit" });
    console.log("✅ 数据库重置完成");

    // 清理上传文件
    const uploadDirs = [
      "backend/uploads/images",
      "backend/uploads/documents",
      "backend/uploads/avatars",
    ];

    uploadDirs.forEach((dir) => {
      if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir);
        files.forEach((file) => {
          if (file !== ".gitkeep") {
            fs.unlinkSync(path.join(dir, file));
          }
        });
        console.log(`🗑️  清理上传文件: ${dir}`);
      }
    });

    console.log("✅ 项目数据重置完成");
  } catch (error) {
    console.error("❌ 重置失败:", error.message);
  }
}

// 备份数据库
function backupDatabase() {
  console.log("💾 备份数据库...\n");

  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const backupDir = `backups/db-${timestamp}`;

  try {
    // 创建备份目录
    if (!fs.existsSync("backups")) {
      fs.mkdirSync("backups");
    }

    // 使用mongodump备份
    execSync(`mongodump --db ruo-admin --out ${backupDir}`, {
      stdio: "inherit",
    });
    console.log(`✅ 数据库备份完成: ${backupDir}`);
  } catch (error) {
    console.error("❌ 备份失败:", error.message);
  }
}

// 恢复数据库
function restoreDatabase() {
  console.log("📥 恢复数据库...\n");

  const backupsDir = "backups";
  if (!fs.existsSync(backupsDir)) {
    console.log("❌ 备份目录不存在");
    return;
  }

  const backups = fs
    .readdirSync(backupsDir)
    .filter((dir) => dir.startsWith("db-"))
    .sort()
    .reverse();

  if (backups.length === 0) {
    console.log("❌ 没有找到备份文件");
    return;
  }

  const latestBackup = backups[0];
  console.log(`使用最新备份: ${latestBackup}`);

  try {
    execSync(
      `mongorestore --db ruo-admin --drop ${backupsDir}/${latestBackup}/ruo-admin`,
      { stdio: "inherit" }
    );
    console.log("✅ 数据库恢复完成");
  } catch (error) {
    console.error("❌ 恢复失败:", error.message);
  }
}

// 查看日志
function viewLogs() {
  console.log("📋 查看日志...\n");

  const logDir = "backend/logs";
  if (!fs.existsSync(logDir)) {
    console.log("❌ 日志目录不存在");
    return;
  }

  const logFiles = fs
    .readdirSync(logDir)
    .filter((file) => file.endsWith(".log"))
    .sort()
    .reverse();

  if (logFiles.length === 0) {
    console.log("❌ 没有找到日志文件");
    return;
  }

  const latestLog = logFiles[0];
  console.log(`显示最新日志: ${latestLog}\n`);

  try {
    const logContent = fs.readFileSync(path.join(logDir, latestLog), "utf8");
    const lines = logContent.split("\n").slice(-50); // 显示最后50行
    console.log(lines.join("\n"));
  } catch (error) {
    console.error("❌ 读取日志失败:", error.message);
  }
}

// 运行测试
function runTests() {
  console.log("🧪 运行测试...\n");

  try {
    // 运行后端测试
    console.log("运行后端测试...");
    execSync("cd backend && npm test", { stdio: "inherit" });

    // 运行前端测试
    console.log("运行前端测试...");
    execSync("cd frontend && npm test", { stdio: "inherit" });

    console.log("✅ 测试完成");
  } catch (error) {
    console.error("❌ 测试失败:", error.message);
  }
}

// 构建项目
function buildProject() {
  console.log("🏗️  构建项目...\n");

  try {
    execSync("npm run build", { stdio: "inherit" });
    console.log("✅ 构建完成");
  } catch (error) {
    console.error("❌ 构建失败:", error.message);
  }
}

// 部署项目
function deployProject() {
  console.log("🚀 部署项目...\n");

  try {
    execSync("npm run deploy:prod", { stdio: "inherit" });
    console.log("✅ 部署完成");
  } catch (error) {
    console.error("❌ 部署失败:", error.message);
  }
}

// 主函数
function main() {
  switch (command) {
    case "help":
    case "--help":
    case "-h":
      showHelp();
      break;
    case "check":
      checkProject();
      break;
    case "clean":
      cleanProject();
      break;
    case "reset":
      resetProject();
      break;
    case "backup":
      backupDatabase();
      break;
    case "restore":
      restoreDatabase();
      break;
    case "logs":
      viewLogs();
      break;
    case "test":
      runTests();
      break;
    case "build":
      buildProject();
      break;
    case "deploy":
      deployProject();
      break;
    default:
      console.log("❌ 未知命令，使用 --help 查看帮助");
      break;
  }
}

// 运行主函数
main();
