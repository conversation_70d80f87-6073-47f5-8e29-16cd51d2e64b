const mongoose = require("mongoose");

const RoleSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      maxlength: 50,
    },
    code: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      maxlength: 50,
    },
    status: {
      type: Number,
      enum: [0, 1], // 0: 禁用, 1: 正常
      default: 1,
    },
    permissions: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Permission",
      },
    ],
    remark: {
      type: String,
      maxlength: 500,
    },
  },
  {
    timestamps: {
      createdAt: "createTime",
      updatedAt: "updateTime",
    },
  }
);

const Role = mongoose.model("Role", RoleSchema);

module.exports = Role;
