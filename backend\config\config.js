const dotenv = require("dotenv");

// 加载环境变量
dotenv.config();

module.exports = {
  // 服务器配置
  port: process.env.PORT || 3000,
  env: process.env.NODE_ENV || "development",

  // 数据库配置
  mongodbUri: process.env.MONGODB_URI || "mongodb://127.0.0.1:27018/ruo-admin",

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || "ruo_admin_secret_key",
    expiresIn: process.env.JWT_EXPIRES_IN || "7d",
  },

  // 跨域配置
  cors: {
    origin: function (origin, callback) {
      // 开发环境允许所有localhost和127.0.0.1的请求
      if (process.env.NODE_ENV !== "production") {
        // 允许所有本地开发请求
        callback(null, true);
        return;
      }

      // 生产环境的严格检查
      const allowedOrigins = [
        "http://localhost:5173",
        "http://localhost:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:3000",
        process.env.CORS_ORIGIN,
      ].filter(Boolean);

      // 如果没有origin（比如移动端应用）或者origin在允许列表中
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error("不允许的跨域请求"));
      }
    },
    credentials: true,
    methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
    exposedHeaders: ["Content-Range", "X-Content-Range"],
    maxAge: 86400, // 24小时
  },

  // 密码加密配置
  bcrypt: {
    saltRounds: 10,
  },
};
