/**
 * 权限检查中间件
 */

/**
 * 检查用户是否有指定权限
 * @param {string} permission - 权限代码
 * @returns {Function} Express中间件函数
 */
const checkPermission = (permission) => {
  return (req, res, next) => {
    // 如果用户未登录，直接拒绝
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        success: false,
        message: "请先登录",
      });
    }

    // 超级管理员拥有所有权限
    if (req.user.username === "admin") {
      return next();
    }

    // 检查用户权限
    const userPermissions = req.user.permissions || [];

    if (userPermissions.includes(permission)) {
      return next();
    }

    return res.status(403).json({
      code: 403,
      success: false,
      message: "权限不足",
    });
  };
};

/**
 * 检查用户是否有任一权限
 * @param {Array} permissions - 权限代码数组
 * @returns {Function} Express中间件函数
 */
const checkAnyPermission = (permissions) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        success: false,
        message: "请先登录",
      });
    }

    // 超级管理员拥有所有权限
    if (req.user.username === "admin") {
      return next();
    }

    const userPermissions = req.user.permissions || [];

    // 检查是否有任一权限
    const hasPermission = permissions.some((permission) =>
      userPermissions.includes(permission)
    );

    if (hasPermission) {
      return next();
    }

    return res.status(403).json({
      code: 403,
      success: false,
      message: "权限不足",
    });
  };
};

/**
 * 检查用户是否有所有权限
 * @param {Array} permissions - 权限代码数组
 * @returns {Function} Express中间件函数
 */
const checkAllPermissions = (permissions) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        success: false,
        message: "请先登录",
      });
    }

    // 超级管理员拥有所有权限
    if (req.user.username === "admin") {
      return next();
    }

    const userPermissions = req.user.permissions || [];

    // 检查是否有所有权限
    const hasAllPermissions = permissions.every((permission) =>
      userPermissions.includes(permission)
    );

    if (hasAllPermissions) {
      return next();
    }

    return res.status(403).json({
      code: 403,
      success: false,
      message: "权限不足",
    });
  };
};

/**
 * 检查用户角色
 * @param {string|Array} roles - 角色代码或角色代码数组
 * @returns {Function} Express中间件函数
 */
const checkRole = (roles) => {
  const roleArray = Array.isArray(roles) ? roles : [roles];

  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        success: false,
        message: "请先登录",
      });
    }

    // 超级管理员拥有所有角色
    if (req.user.username === "admin") {
      return next();
    }

    const userRoles = req.user.roles || [];

    // 检查是否有指定角色
    const hasRole = roleArray.some((role) =>
      userRoles.some((userRole) => userRole.code === role)
    );

    if (hasRole) {
      return next();
    }

    return res.status(403).json({
      code: 403,
      success: false,
      message: "角色权限不足",
    });
  };
};

module.exports = {
  checkPermission,
  checkAnyPermission,
  checkAllPermissions,
  checkRole,
};
