import { defineStore } from "pinia";
import { ref, watch } from "vue";
import { useRoute } from "vue-router";

// 从 localStorage 获取已保存的标签页
const getStoredTags = () => {
  const storedTags = localStorage.getItem("visitedViews");
  return storedTags ? JSON.parse(storedTags) : [];
};

export const useTagsViewStore = defineStore("tagsView", () => {
  // 访问过的视图
  const visitedViews = ref(getStoredTags());
  // 缓存的视图（用于keep-alive）
  const cachedViews = ref([]);

  // 保存标签页到 localStorage
  const saveVisitedViews = () => {
    localStorage.setItem("visitedViews", JSON.stringify(visitedViews.value));
  };

  // 添加访问视图
  const addVisitedView = (view) => {
    // 如果已经存在该视图，则不重复添加
    if (visitedViews.value.some((v) => v.path === view.path)) {
      // 更新已存在视图的标题
      const existingView = visitedViews.value.find((v) => v.path === view.path);
      if (existingView && view.meta?.title) {
        existingView.title = view.meta.title;
      }
      return;
    }

    // 添加到访问视图列表
    visitedViews.value.push(
      Object.assign({}, view, {
        title: view.meta?.title || "无标题",
      })
    );

    // 保存到 localStorage
    saveVisitedViews();
  };

  // 添加缓存视图
  const addCachedView = (view) => {
    // 如果视图不需要缓存或已经在缓存列表中，则返回
    if (!view.name || cachedViews.value.includes(view.name)) return;

    // 如果视图设置了需要缓存
    if (view.meta?.keepAlive) {
      cachedViews.value.push(view.name);
    }
  };

  // 删除访问视图
  const delVisitedView = (view) => {
    const index = visitedViews.value.findIndex((v) => v.path === view.path);
    if (index !== -1) {
      visitedViews.value.splice(index, 1);
      // 保存到 localStorage
      saveVisitedViews();
    }
  };

  // 删除缓存视图
  const delCachedView = (view) => {
    if (!view.name) return;

    const index = cachedViews.value.indexOf(view.name);
    if (index !== -1) {
      cachedViews.value.splice(index, 1);
    }
  };

  // 关闭标签页
  const closeTag = (view) => {
    delVisitedView(view);
    delCachedView(view);
  };

  // 关闭其他标签页
  const closeOtherTags = (view) => {
    visitedViews.value = visitedViews.value.filter((v) => {
      return v.meta?.affix || v.path === view.path;
    });
    saveVisitedViews();

    cachedViews.value = cachedViews.value.filter((name) => {
      return name === view.name;
    });
  };

  // 关闭所有标签页
  const closeAllTags = () => {
    // 过滤出固定的标签
    visitedViews.value = visitedViews.value.filter((tag) => tag.meta?.affix);
    saveVisitedViews();
    cachedViews.value = [];
  };

  // 更新访问视图
  const updateVisitedView = (view) => {
    for (let v of visitedViews.value) {
      if (v.path === view.path) {
        v = Object.assign(v, view);
        break;
      }
    }
    // 保存到 localStorage
    saveVisitedViews();
  };

  return {
    visitedViews,
    cachedViews,
    addVisitedView,
    addCachedView,
    delVisitedView,
    delCachedView,
    closeTag,
    closeOtherTags,
    closeAllTags,
    updateVisitedView,
  };
});

// 创建一个组合式函数来自动添加当前路由到标签视图
export function useTagsViewAutoAdd() {
  const route = useRoute();
  const tagsViewStore = useTagsViewStore();

  // 监听路由变化，自动添加到标签视图
  watch(
    () => route,
    (newRoute) => {
      if (newRoute.path) {
        // 添加到访问视图和缓存视图
        tagsViewStore.addVisitedView(newRoute);
        tagsViewStore.addCachedView(newRoute);
      }
    },
    { immediate: true, deep: true }
  );

  return tagsViewStore;
}
