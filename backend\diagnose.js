console.log("🔍 开始诊断后端服务...\n");

let step = 1;

function logStep(message) {
  console.log(`${step}. ${message}`);
  step++;
}

try {
  // 步骤1: 基础模块
  logStep("加载基础模块...");
  const express = require("express");
  const cors = require("cors");
  const dotenv = require("dotenv");
  console.log("   ✅ 基础模块加载成功");

  // 步骤2: 环境变量
  logStep("加载环境变量...");
  dotenv.config();
  console.log("   ✅ 环境变量加载成功");
  console.log(`   📍 PORT: ${process.env.PORT}`);
  console.log(`   📍 NODE_ENV: ${process.env.NODE_ENV}`);

  // 步骤3: 配置文件
  logStep("加载配置文件...");
  const config = require("./config/config");
  console.log("   ✅ 配置文件加载成功");
  console.log(`   📍 服务端口: ${config.port}`);

  // 步骤4: 数据库配置
  logStep("加载数据库配置...");
  const connectDB = require("./config/db");
  console.log("   ✅ 数据库配置加载成功");

  // 步骤5: 工具模块
  logStep("加载工具模块...");
  const { logger } = require("./utils/logger");
  console.log("   ✅ 日志工具加载成功");

  // 步骤6: 中间件
  logStep("加载中间件...");
  const {
    errorHandler,
    notFoundHandler,
  } = require("./middleware/errorHandler");
  console.log("   ✅ 错误处理中间件加载成功");

  // 步骤7: 安全中间件
  logStep("加载安全中间件...");
  const {
    securityHeaders,
    requestLogger,
    apiLimiter,
  } = require("./middleware/security");
  console.log("   ✅ 安全中间件加载成功");

  // 步骤8: 服务模块
  logStep("加载服务模块...");
  const cronService = require("./services/cronService");
  console.log("   ✅ 定时任务服务加载成功");

  const emailService = require("./services/emailService");
  console.log("   ✅ 邮件服务加载成功");

  // 步骤9: 路由模块
  logStep("加载路由模块...");
  const routes = require("./routes");
  console.log("   ✅ 路由模块加载成功");

  // 步骤10: 创建应用
  logStep("创建Express应用...");
  const app = express();
  console.log("   ✅ Express应用创建成功");

  // 步骤11: 配置中间件
  logStep("配置中间件...");
  app.use(cors(config.cors));
  app.use(express.json({ limit: "10mb" }));
  app.use(express.urlencoded({ extended: false, limit: "10mb" }));
  console.log("   ✅ 基础中间件配置成功");

  // 步骤12: 添加路由
  logStep("添加路由...");
  app.get("/test", (req, res) => {
    res.json({ message: "诊断测试成功", timestamp: new Date().toISOString() });
  });
  console.log("   ✅ 测试路由添加成功");

  // 步骤13: 启动服务器
  logStep("启动服务器...");
  const PORT = config.port || 3000;

  const server = app.listen(PORT, () => {
    console.log(`   ✅ 服务器启动成功！`);
    console.log(`   🌐 地址: http://localhost:${PORT}`);
    console.log(`   🧪 测试: http://localhost:${PORT}/test`);

    // 10秒后自动关闭
    setTimeout(() => {
      console.log("\n🛑 诊断完成，关闭服务器...");
      server.close(() => {
        console.log("✅ 服务器已关闭");
        process.exit(0);
      });
    }, 10000);
  });

  server.on("error", (error) => {
    console.error(`   ❌ 服务器启动失败: ${error.message}`);
    if (error.code === "EADDRINUSE") {
      console.error(`   💡 端口 ${PORT} 已被占用，请尝试其他端口`);
    }
    process.exit(1);
  });
} catch (error) {
  console.error(`❌ 第${step - 1}步失败:`, error.message);
  console.error("错误详情:", error.stack);
  process.exit(1);
}
