const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const config = require("../config/config");

const UserSchema = new mongoose.Schema(
  {
    username: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      minlength: 3,
      maxlength: 50,
    },
    password: {
      type: String,
      required: true,
      minlength: 6,
    },
    nickname: {
      type: String,
      required: false,
      trim: true,
      maxlength: 50,
    },
    email: {
      type: String,
      required: false,
      trim: true,
      lowercase: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        "请提供有效的邮箱地址",
      ],
    },
    phone: {
      type: String,
      required: false,
      trim: true,
    },
    avatar: {
      type: String,
      default: "",
    },
    status: {
      type: Number,
      enum: [0, 1], // 0: 禁用, 1: 正常
      default: 1,
    },
    deptId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Department",
      required: false,
    },
    roles: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Role",
      },
    ],
    remark: {
      type: String,
      maxlength: 500,
    },
  },
  {
    timestamps: {
      createdAt: "createTime",
      updatedAt: "updateTime",
    },
  }
);

// 保存前加密密码
UserSchema.pre("save", async function (next) {
  // 只有在密码被修改时才重新加密
  if (!this.isModified("password")) return next();

  try {
    // 生成盐
    const salt = await bcrypt.genSalt(config.bcrypt.saltRounds);
    // 加密密码
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// 验证密码
UserSchema.methods.comparePassword = async function (candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model("User", UserSchema);

module.exports = User;
