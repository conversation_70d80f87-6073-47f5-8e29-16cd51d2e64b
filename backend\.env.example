# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
MONGODB_URI=mongodb://127.0.0.1:27017/ruo-admin

# JWT配置
JWT_SECRET=your_jwt_secret_key_here_please_change_in_production
JWT_EXPIRES_IN=7d

# 跨域配置
CORS_ORIGIN=http://localhost:5173

# 密码加密配置
BCRYPT_SALT_ROUNDS=10

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 邮件配置（可选）
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
SMTP_FROM=<EMAIL>

# 文件上传配置
UPLOAD_PATH=uploads
MAX_FILE_SIZE=10485760

# Redis配置（可选，用于会话存储）
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_MAX=5

# 定时任务配置
ENABLE_DB_BACKUP=false
CRON_TIMEZONE=Asia/Shanghai

# 前端地址（用于邮件链接）
FRONTEND_URL=http://localhost:5173

# 系统监控配置
ENABLE_SYSTEM_MONITOR=true
HEALTH_CHECK_INTERVAL=300000
