const rateLimit = require("express-rate-limit");
const helmet = require("helmet");

// 请求频率限制
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      code: 429,
      success: false,
      message,
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
};

// 登录频率限制
const loginLimiter = createRateLimiter(
  15 * 60 * 1000, // 15分钟
  5, // 最多5次尝试
  "登录尝试过于频繁，请15分钟后再试"
);

// API频率限制
const apiLimiter = createRateLimiter(
  15 * 60 * 1000, // 15分钟
  100, // 最多100次请求
  "API请求过于频繁，请稍后再试"
);

// 严格的API频率限制（用于敏感操作）
const strictLimiter = createRateLimiter(
  15 * 60 * 1000, // 15分钟
  10, // 最多10次请求
  "敏感操作过于频繁，请稍后再试"
);

// 安全头设置
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
});

// IP白名单中间件
const ipWhitelist = (whitelist = []) => {
  return (req, res, next) => {
    if (process.env.NODE_ENV !== "production") {
      return next();
    }

    const clientIP = req.ip || req.connection.remoteAddress;

    if (whitelist.length > 0 && !whitelist.includes(clientIP)) {
      return res.status(403).json({
        code: 403,
        success: false,
        message: "IP地址不在白名单中",
      });
    }

    next();
  };
};

// 请求日志中间件
const requestLogger = (req, res, next) => {
  const start = Date.now();

  res.on("finish", () => {
    const duration = Date.now() - start;
    const logData = {
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get("User-Agent"),
      timestamp: new Date().toISOString(),
    };

    if (req.user) {
      logData.userId = req.user.id;
      logData.username = req.user.username;
    }

    console.log("API请求:", JSON.stringify(logData));
  });

  next();
};

module.exports = {
  loginLimiter,
  apiLimiter,
  strictLimiter,
  securityHeaders,
  ipWhitelist,
  requestLogger,
};
