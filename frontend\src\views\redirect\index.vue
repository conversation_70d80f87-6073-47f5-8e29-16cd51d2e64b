<script setup>
import { onBeforeMount } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

onBeforeMount(() => {
    const { params, query } = route
    const { path } = params

    // 获取完整路径
    const fullPath = Array.isArray(path) ? '/' + path.join('/') : '/' + path

    router.replace({
        path: fullPath,
        query
    })
})
</script>