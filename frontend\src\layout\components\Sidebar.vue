<template>
  <el-menu
    :default-active="activeMenu"
    :collapse="isCollapse"
    :unique-opened="true"
    :collapse-transition="false"
    background-color="#304156"
    text-color="#bfcbd9"
    active-text-color="#ffd04b"
    mode="vertical"
    @select="handleSelect"
  >
    <sidebar-item
      v-for="route in menuRoutes"
      :key="route.path"
      :item="route"
      :base-path="route.path"
    />
  </el-menu>
</template>

<script setup>
import { computed, ref, onMounted, onBeforeUnmount } from "vue";
import { useRoute, useRouter } from "vue-router";
import { usePermissionStore } from "@/stores/permission";
import SidebarItem from "./SidebarItem.vue";

const props = defineProps({
  isCollapse: {
    type: Boolean,
    default: false,
  },
});

const route = useRoute();
const router = useRouter();
const permissionStore = usePermissionStore();
const isMenuClickable = ref(true);

// 当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route;
  // 优先使用meta中指定的activeMenu
  if (meta && meta.activeMenu) {
    return meta.activeMenu;
  }

  // 对于系统管理子菜单，需要特殊处理
  if (path.startsWith("/system/")) {
    // 返回完整路径以确保子菜单高亮
    return path;
  }

  return path;
});

// 处理菜单选择事件
const handleSelect = (index, indexPath) => {
  // 防止频繁点击
  if (!isMenuClickable.value) return;

  isMenuClickable.value = false;
  console.log("菜单选择:", index, indexPath);

  // 300ms后允许再次点击
  setTimeout(() => {
    isMenuClickable.value = true;
  }, 300);
};

// 过滤出需要显示在菜单中的路由
const menuRoutes = computed(() => {
  // 只显示顶级路由
  return permissionStore.routes.filter((route) => {
    // 过滤掉隐藏的路由
    if (route.hidden) {
      return false;
    }

    // 过滤掉404路由
    if (route.path.includes("*")) {
      return false;
    }

    // 过滤掉没有组件的路由（除了/system）
    if (!route.component && route.path !== "/system") {
      return false;
    }

    // 只保留顶级路由或者有子路由的路由
    return !route.meta || !route.meta.parentPath;
  });
});

// 打印路由信息，用于调试
console.log("所有路由:", permissionStore.routes);
console.log("菜单路由:", menuRoutes.value);
console.log("当前激活菜单:", activeMenu.value);
</script>

<!-- <template>
  <h5 class="mb-2">Default colors</h5>
  <el-menu default-active="2" class="el-menu-vertical-demo" @open="handleOpen" @close="handleClose">
    <el-sub-menu index="1">
      <template #title>
        <el-icon>
          <location />
        </el-icon>
        <span>Navigator One</span>
      </template>
      <el-menu-item-group title="Group One">
        <el-menu-item index="1-1">item one</el-menu-item>
        <el-menu-item index="1-2">item two</el-menu-item>
      </el-menu-item-group>
      <el-menu-item-group title="Group Two">
        <el-menu-item index="1-3">item three</el-menu-item>
      </el-menu-item-group>
      <el-sub-menu index="1-4">
        <template #title>item four</template>
        <el-menu-item index="1-4-1">item one</el-menu-item>
      </el-sub-menu>
    </el-sub-menu>
    <el-menu-item index="2">
      <el-icon><icon-menu /></el-icon>
      <span>Navigator Two</span>
    </el-menu-item>
    <el-menu-item index="3" disabled>
      <el-icon>
        <document />
      </el-icon>
      <span>Navigator Three</span>
    </el-menu-item>
    <el-menu-item index="4">
      <el-icon>
        <setting />
      </el-icon>
      <span>Navigator Four</span>
    </el-menu-item>
  </el-menu>
</template>

<script lang="ts" setup>
import {
  Document,
  Menu as IconMenu,
  Location,
  Setting,
} from '@element-plus/icons-vue'

const handleOpen = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
</script> -->
