const jwt = require("jsonwebtoken");
const config = require("../config/config");

/**
 * 生成JWT令牌
 * @param {Object} payload - 要编码到令牌中的数据
 * @returns {String} JWT令牌
 */
const generateToken = (payload) => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  });
};

/**
 * 验证JWT令牌
 * @param {String} token - 要验证的JWT令牌
 * @returns {Object|null} 解码后的数据或null（如果令牌无效）
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, config.jwt.secret);
  } catch (error) {
    console.error("JWT验证失败:", error.name, error.message);
    return null;
  }
};

module.exports = {
  generateToken,
  verifyToken,
};
