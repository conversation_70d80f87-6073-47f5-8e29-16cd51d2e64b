import request from "@/utils/request";

/**
 * 获取角色列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getRoleList(params) {
  return request({
    url: "/roles",
    method: "get",
    params,
  });
}

// 获取单个角色
export function getRoleById(id) {
  return request({
    url: `/roles/${id}`,
    method: "get",
  });
}

/**
 * 创建角色
 * @param {Object} data - 角色数据
 * @returns {Promise}
 */
export function createRole(data) {
  return request({
    url: "/roles",
    method: "post",
    data,
  });
}

/**
 * 更新角色
 * @param {string} id - 角色ID
 * @param {Object} data - 角色数据
 * @returns {Promise}
 */
export function updateRole(id, data) {
  return request({
    url: `/roles/${id}`,
    method: "put",
    data,
  });
}

/**
 * 删除角色
 * @param {string} id - 角色ID
 * @returns {Promise}
 */
export function deleteRole(id) {
  return request({
    url: `/roles/${id}`,
    method: "delete",
  });
}

/**
 * 批量删除角色
 * @param {Array} ids - 角色ID数组
 * @returns {Promise}
 */
export function batchDeleteRoles(ids) {
  return request({
    url: "/roles/batch",
    method: "delete",
    data: { ids },
  });
}

/**
 * 更新角色状态
 * @param {string} id - 角色ID
 * @param {number} status - 状态值
 * @returns {Promise}
 */
export function updateRoleStatus(id, status) {
  console.log("角色状态更新 - 原始数据:", {
    id,
    status,
    statusType: typeof status,
  });

  // 确保status是数字类型，防止数据格式错误
  let numericStatus = typeof status === "number" ? status : Number(status);

  // 如果转换失败，尝试其他方式
  if (isNaN(numericStatus)) {
    numericStatus = status ? 1 : 0;
  }

  // 强制转换为0或1
  numericStatus = numericStatus ? 1 : 0;

  console.log("角色状态更新 - 最终值:", numericStatus);

  return request({
    url: `/roles/${id}/status`,
    method: "patch",
    data: { status: numericStatus },
  });
}

// 获取所有角色（不分页）
export function getAllRoles() {
  return request({
    url: "/roles/all",
    method: "get",
  });
}

/**
 * 获取角色的权限
 * @param {string} id - 角色ID
 * @returns {Promise}
 */
export function getRolePermissions(id) {
  return request({
    url: `/roles/${id}/permissions`,
    method: "get",
  });
}

/**
 * 更新角色的权限
 * @param {string} id - 角色ID
 * @param {Array} permissions - 权限ID数组
 * @returns {Promise}
 */
export function updateRolePermissions(id, permissions) {
  return request({
    url: `/roles/${id}/permissions`,
    method: "put",
    data: { permissions },
  });
}
