#!/usr/bin/env node

/**
 * 清理空目录脚本
 */

const fs = require('fs');
const path = require('path');

function removeEmptyDirectories(dirPath) {
  try {
    if (!fs.existsSync(dirPath)) {
      return false;
    }

    const files = fs.readdirSync(dirPath);
    
    if (files.length === 0) {
      // 目录为空，删除它
      fs.rmdirSync(dirPath);
      console.log(`✅ 删除空目录: ${dirPath}`);
      return true;
    }

    // 递归检查子目录
    let hasFiles = false;
    for (const file of files) {
      const fullPath = path.join(dirPath, file);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        const removed = removeEmptyDirectories(fullPath);
        if (!removed) {
          hasFiles = true;
        }
      } else {
        hasFiles = true;
      }
    }

    // 如果所有子目录都被删除且没有文件，删除当前目录
    if (!hasFiles) {
      fs.rmdirSync(dirPath);
      console.log(`✅ 删除空目录: ${dirPath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 删除目录失败 ${dirPath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🧹 开始清理空目录...\n');
  
  const dirsToCheck = [
    path.join(__dirname, '../frontend/src/views/example'),
    path.join(__dirname, '../frontend/src/views/menu'),
  ];
  
  let removedCount = 0;
  
  for (const dir of dirsToCheck) {
    if (removeEmptyDirectories(dir)) {
      removedCount++;
    }
  }
  
  console.log(`\n🎉 清理完成！删除了 ${removedCount} 个空目录`);
}

if (require.main === module) {
  main();
}
