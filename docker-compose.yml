version: "3.8"

services:
  # MongoDB 数据库
  mongodb:
    image: mongo:6.0
    container_name: ruo-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: ruo-admin
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - ruo-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: ruo-redis
    restart: unless-stopped
    command: redis-server --requirepass password123 --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ruo-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ruo-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      MONGODB_URI: ********************************************************************
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_key_change_in_production}
      JWT_EXPIRES_IN: 7d
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: password123
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost}
      ENABLE_SYSTEM_MONITOR: true
    ports:
      - "3000:3000"
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    networks:
      - ruo-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ruo-frontend
    restart: unless-stopped
    environment:
      VITE_APP_BASE_API: ${VITE_APP_BASE_API:-http://localhost:3000/api/v1}
      VITE_APP_TITLE: ${VITE_APP_TITLE:-后台管理系统}
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - ruo-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  ruo-network:
    driver: bridge
