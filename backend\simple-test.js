console.log("Node.js 测试开始...");

try {
  const express = require("express");
  console.log("✅ Express 加载成功");

  const app = express();
  console.log("✅ Express 应用创建成功");

  app.get("/", (req, res) => {
    res.json({ message: "测试服务运行正常", time: new Date().toISOString() });
  });

  const PORT = 3001; // 使用不同端口避免冲突

  const server = app.listen(PORT, () => {
    console.log(`✅ 测试服务启动成功，端口: ${PORT}`);
    console.log(`🌐 访问地址: http://localhost:${PORT}`);

    // 5秒后自动关闭
    setTimeout(() => {
      console.log("🛑 测试完成，关闭服务器");
      server.close(() => {
        console.log("✅ 服务器已关闭");
        process.exit(0);
      });
    }, 5000);
  });

  server.on("error", (error) => {
    console.error("❌ 服务器启动失败:", error.message);
    process.exit(1);
  });
} catch (error) {
  console.error("❌ 测试失败:", error.message);
  console.error("错误堆栈:", error.stack);
  process.exit(1);
}
