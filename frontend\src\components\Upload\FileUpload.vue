<template>
  <div class="file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :multiple="multiple"
      :limit="limit"
      :file-list="fileList"
      :accept="accept"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-progress="handleProgress"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      :auto-upload="autoUpload"
      :show-file-list="showFileList"
      :drag="drag"
      :disabled="disabled"
      class="upload-component"
    >
      <template v-if="drag">
        <div class="upload-dragger">
          <el-icon class="upload-icon"><UploadFilled /></el-icon>
          <div class="upload-text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="upload-tip" v-if="tip">
            {{ tip }}
          </div>
        </div>
      </template>

      <template v-else>
        <el-button type="primary" :disabled="disabled">
          <el-icon><Upload /></el-icon>
          {{ buttonText }}
        </el-button>
        <div class="upload-tip" v-if="tip">
          {{ tip }}
        </div>
      </template>
    </el-upload>

    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress
        :percentage="uploadProgress"
        :status="uploadStatus"
        :stroke-width="6"
      />
      <div class="progress-text">
        {{ uploadProgressText }}
      </div>
    </div>

    <!-- 文件预览 -->
    <div v-if="previewVisible" class="file-preview">
      <el-dialog
        v-model="previewVisible"
        title="文件预览"
        width="60%"
        :before-close="closePreview"
      >
        <div class="preview-content">
          <img
            v-if="isImage(previewFile)"
            :src="previewFile.url"
            alt="预览图片"
            style="max-width: 100%; max-height: 500px"
          />
          <div v-else class="file-info">
            <el-icon size="48"><Document /></el-icon>
            <p>{{ previewFile.name }}</p>
            <p>文件大小: {{ formatFileSize(previewFile.size) }}</p>
            <el-button type="primary" @click="downloadFile(previewFile)">
              下载文件
            </el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { Upload, UploadFilled, Document } from "@element-plus/icons-vue";
import { getToken } from "@/utils/auth";

// Props
const props = defineProps({
  // 上传地址
  action: {
    type: String,
    default: "/api/v1/upload/file",
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 最大上传数量
  limit: {
    type: Number,
    default: 1,
  },
  // 文件列表
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 接受的文件类型
  accept: {
    type: String,
    default: "",
  },
  // 文件大小限制(MB)
  maxSize: {
    type: Number,
    default: 10,
  },
  // 是否自动上传
  autoUpload: {
    type: Boolean,
    default: true,
  },
  // 是否显示文件列表
  showFileList: {
    type: Boolean,
    default: true,
  },
  // 是否启用拖拽上传
  drag: {
    type: Boolean,
    default: false,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 按钮文字
  buttonText: {
    type: String,
    default: "选择文件",
  },
  // 提示文字
  tip: {
    type: String,
    default: "",
  },
  // 额外的上传参数
  data: {
    type: Object,
    default: () => ({}),
  },
});

// Emits
const emit = defineEmits([
  "update:modelValue",
  "success",
  "error",
  "progress",
  "remove",
]);

// 响应式数据
const uploadRef = ref();
const fileList = ref([]);
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadStatus = ref("");
const uploadProgressText = ref("");
const previewVisible = ref(false);
const previewFile = ref({});

// 计算属性
const uploadUrl = computed(() => {
  return import.meta.env.VITE_APP_BASE_API + props.action;
});

const uploadHeaders = computed(() => {
  const token = getToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
});

const uploadData = computed(() => {
  return { ...props.data };
});

// 监听文件列表变化
watch(
  () => props.modelValue,
  (newVal) => {
    fileList.value = newVal.map((file) => ({
      name: file.name || file.filename,
      url: file.url,
      uid: file.uid || Date.now() + Math.random(),
      status: "success",
      ...file,
    }));
  },
  { immediate: true, deep: true }
);

// 上传前检查
const beforeUpload = (file) => {
  // 检查文件大小
  if (file.size > props.maxSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`);
    return false;
  }

  // 检查文件类型
  if (props.accept) {
    const acceptTypes = props.accept.split(",").map((type) => type.trim());
    const fileType = file.type;
    const fileName = file.name;
    const fileExt = fileName.substring(fileName.lastIndexOf("."));

    const isAccepted = acceptTypes.some((type) => {
      if (type.startsWith(".")) {
        return fileExt.toLowerCase() === type.toLowerCase();
      } else {
        return fileType.includes(type.replace("*", ""));
      }
    });

    if (!isAccepted) {
      ElMessage.error(`不支持的文件类型: ${fileType}`);
      return false;
    }
  }

  uploading.value = true;
  uploadProgress.value = 0;
  uploadStatus.value = "";
  uploadProgressText.value = "准备上传...";

  return true;
};

// 上传成功
const handleSuccess = (response, file, fileList) => {
  uploading.value = false;
  uploadProgress.value = 100;
  uploadStatus.value = "success";
  uploadProgressText.value = "上传成功";

  if (response.success) {
    const newFile = {
      name: response.data.originalname,
      filename: response.data.filename,
      url: response.data.url,
      size: response.data.size,
      type: response.data.mimetype,
      uid: file.uid,
    };

    const newFileList = [...props.modelValue, newFile];
    emit("update:modelValue", newFileList);
    emit("success", response, file, fileList);

    ElMessage.success("文件上传成功");
  } else {
    ElMessage.error(response.message || "上传失败");
  }
};

// 上传失败
const handleError = (error, file, fileList) => {
  uploading.value = false;
  uploadStatus.value = "exception";
  uploadProgressText.value = "上传失败";

  console.error("上传失败:", error);
  ElMessage.error("文件上传失败");
  emit("error", error, file, fileList);
};

// 上传进度
const handleProgress = (event, file, fileList) => {
  uploadProgress.value = Math.round(event.percent);
  uploadProgressText.value = `上传中... ${uploadProgress.value}%`;
  emit("progress", event, file, fileList);
};

// 移除文件
const handleRemove = (file, fileList) => {
  const newFileList = props.modelValue.filter((item) => item.uid !== file.uid);
  emit("update:modelValue", newFileList);
  emit("remove", file, fileList);
};

// 超出限制
const handleExceed = (files, fileList) => {
  ElMessage.warning(`最多只能上传 ${props.limit} 个文件`);
};

// 预览文件
const previewFiles = (file) => {
  previewFile.value = file;
  previewVisible.value = true;
};

// 关闭预览
const closePreview = () => {
  previewVisible.value = false;
  previewFile.value = {};
};

// 判断是否为图片
const isImage = (file) => {
  const imageTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
  ];
  return (
    imageTypes.includes(file.type) ||
    /\.(jpg|jpeg|png|gif|webp)$/i.test(file.name)
  );
};

// 下载文件
const downloadFile = (file) => {
  const link = document.createElement("a");
  link.href = file.url;
  link.download = file.name;
  link.click();
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return "0 B";

  const units = ["B", "KB", "MB", "GB"];
  let index = 0;

  while (size >= 1024 && index < units.length - 1) {
    size /= 1024;
    index++;
  }

  return Math.round(size * 100) / 100 + " " + units[index];
};

// 手动上传
const submit = () => {
  uploadRef.value?.submit();
};

// 清空文件列表
const clearFiles = () => {
  uploadRef.value?.clearFiles();
  emit("update:modelValue", []);
};

// 暴露方法
defineExpose({
  submit,
  clearFiles,
});
</script>

<style scoped>
.file-upload {
  width: 100%;
}

.upload-component {
  width: 100%;
}

.upload-dragger {
  padding: 40px;
  text-align: center;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-dragger:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.upload-text em {
  color: #409eff;
  font-style: normal;
}

.upload-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}

.upload-progress {
  margin-top: 16px;
}

.progress-text {
  text-align: center;
  color: #606266;
  font-size: 12px;
  margin-top: 8px;
}

.file-preview .preview-content {
  text-align: center;
}

.file-preview .file-info {
  padding: 20px;
}

.file-preview .file-info p {
  margin: 8px 0;
  color: #606266;
}
</style>
