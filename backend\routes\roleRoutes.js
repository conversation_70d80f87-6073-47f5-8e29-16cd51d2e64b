const express = require("express");
const router = express.Router();
const {
  getRoles,
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  batchDeleteRoles,
  updateRoleStatus,
} = require("../controllers/roleController");
const { authenticate, authorize } = require("../middleware/auth");
const { validate } = require("../middleware/validator");
const {
  createRoleValidator,
  updateRoleValidator,
  roleStatusValidator,
} = require("../validators/roleValidator");

// 所有角色路由都需要认证
router.use(authenticate);

// 获取角色列表（分页）
router.get("/", authorize("system:role:list"), getRoles);

// 获取所有角色（不分页，用于下拉选择）
router.get("/all", authorize("system:role:list"), getAllRoles);

// 获取单个角色
router.get("/:id", authorize("system:role:query"), getRoleById);

// 创建角色
router.post(
  "/",
  authorize("system:role:create"),
  createRoleValidator,
  validate,
  createRole
);

// 更新角色
router.put(
  "/:id",
  authorize("system:role:update"),
  updateRoleValidator,
  validate,
  updateRole
);

// 删除角色
router.delete("/:id", authorize("system:role:delete"), deleteRole);

// 批量删除角色
router.delete("/batch", authorize("system:role:delete"), batchDeleteRoles);

// 更新角色状态
router.patch(
  "/:id/status",
  authorize("system:role:update"),
  roleStatusValidator,
  validate,
  updateRoleStatus
);

module.exports = router;
