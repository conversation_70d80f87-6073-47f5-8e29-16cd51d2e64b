const { logger } = require("../utils/logger");
const { error } = require("../utils/response");

/**
 * 全局错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  let statusCode = 500;
  let message = "服务器内部错误";
  let details = null;

  // 记录错误日志
  logger.error(`Error in ${req.method} ${req.path}:`, {
    error: err.message,
    stack: err.stack,
    body: req.body,
    params: req.params,
    query: req.query,
    user: req.user ? req.user._id : null,
  });

  // 根据错误类型设置响应
  if (err.name === "ValidationError") {
    // Mongoose验证错误
    statusCode = 400;
    message = "数据验证失败";
    details = Object.values(err.errors).map((error) => ({
      field: error.path,
      message: error.message,
    }));
  } else if (err.name === "CastError") {
    // Mongoose类型转换错误
    statusCode = 400;
    message = "无效的ID格式";
  } else if (err.code === 11000) {
    // MongoDB重复键错误
    statusCode = 400;
    message = "数据已存在";
    const field = Object.keys(err.keyValue)[0];
    details = `${field} 已存在`;
  } else if (err.name === "JsonWebTokenError") {
    // JWT错误
    statusCode = 401;
    message = "无效的访问令牌";
  } else if (err.name === "TokenExpiredError") {
    // JWT过期错误
    statusCode = 401;
    message = "访问令牌已过期";
  } else if (err.name === "MulterError") {
    // 文件上传错误
    statusCode = 400;
    if (err.code === "LIMIT_FILE_SIZE") {
      message = "文件大小超出限制";
    } else if (err.code === "LIMIT_FILE_COUNT") {
      message = "文件数量超出限制";
    } else {
      message = "文件上传失败";
    }
  } else if (err.statusCode || err.status) {
    // 自定义错误状态码
    statusCode = err.statusCode || err.status;
    message = err.message;
  } else if (process.env.NODE_ENV === "development") {
    // 开发环境显示详细错误信息
    message = err.message;
    details = err.stack;
  }

  // 发送错误响应
  return error(res, message, statusCode, details);
};

/**
 * 404错误处理中间件
 */
const notFoundHandler = (req, res, next) => {
  const message = `路由 ${req.originalUrl} 不存在`;
  logger.warn(`404 - ${message}`, {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get("User-Agent"),
  });
  
  return error(res, message, 404);
};

/**
 * 异步错误捕获包装器
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 自定义错误类
 */
class AppError extends Error {
  constructor(message, statusCode = 500, details = null) {
    super(message);
    this.statusCode = statusCode;
    this.details = details;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 验证错误类
 */
class ValidationError extends AppError {
  constructor(message, details = null) {
    super(message, 400, details);
  }
}

/**
 * 认证错误类
 */
class AuthenticationError extends AppError {
  constructor(message = "认证失败") {
    super(message, 401);
  }
}

/**
 * 授权错误类
 */
class AuthorizationError extends AppError {
  constructor(message = "权限不足") {
    super(message, 403);
  }
}

/**
 * 资源未找到错误类
 */
class NotFoundError extends AppError {
  constructor(message = "资源未找到") {
    super(message, 404);
  }
}

/**
 * 冲突错误类
 */
class ConflictError extends AppError {
  constructor(message = "资源冲突") {
    super(message, 409);
  }
}

module.exports = {
  errorHandler,
  notFoundHandler,
  asyncHandler,
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
};
