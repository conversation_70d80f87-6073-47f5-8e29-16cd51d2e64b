const mongoose = require("mongoose");

const PermissionSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
    },
    type: {
      type: String,
      enum: ["menu", "button"], // 菜单或按钮
      required: true,
    },
    parentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Permission",
      default: null,
    },
    path: {
      type: String,
      trim: true,
      maxlength: 100,
    },
    component: {
      type: String,
      trim: true,
      maxlength: 100,
    },
    code: {
      type: String,
      trim: true,
      maxlength: 100,
    },
    icon: {
      type: String,
      trim: true,
      maxlength: 50,
    },
    sort: {
      type: Number,
      default: 0,
    },
    status: {
      type: Number,
      enum: [0, 1], // 0: 禁用, 1: 正常
      default: 1,
    },
  },
  {
    timestamps: {
      createdAt: "createTime",
      updatedAt: "updateTime",
    },
  }
);

const Permission = mongoose.model("Permission", PermissionSchema);

module.exports = Permission;
