<template>
    <el-scrollbar ref="scrollContainer" :vertical="false" class="scroll-container" @wheel.prevent="handleScroll">
        <slot />
    </el-scrollbar>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'

const scrollContainer = ref(null)
const scrollWrapper = computed(() => scrollContainer.value?.$el.querySelector('.el-scrollbar__wrap'))

// 滚动速度
const scrollSpeed = 15

// 处理滚轮事件
const handleScroll = (e) => {
    const eventDelta = e.wheelDelta || -e.deltaY * 40
    const $scrollWrapper = scrollWrapper.value
    $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft - eventDelta / 4
}

// 移动到目标标签
const moveToTarget = (currentTag) => {
    const $container = scrollContainer.value?.$el
    const $containerWidth = $container.offsetWidth
    const $scrollWrapper = scrollWrapper.value

    // 获取所有标签
    const $tags = $container.querySelectorAll('.tags-view-item')

    let firstTag = null
    let lastTag = null

    // 找出第一个和最后一个标签的位置
    if ($tags.length > 0) {
        firstTag = $tags[0]
        lastTag = $tags[$tags.length - 1]
    }

    if (!firstTag || !lastTag) return

    // 目标标签的位置
    let tagIndex = 0
    const currentTagElement = Array.from($tags).find((el, index) => {
        if (el.dataset.path === currentTag.path) {
            tagIndex = index
            return true
        }
        return false
    })

    if (!currentTagElement) return

    // 目标标签的左侧位置
    const tagLeft = currentTagElement.offsetLeft
    // 目标标签的宽度
    const tagWidth = currentTagElement.offsetWidth

    // 目标标签在可视区域左侧
    if (tagLeft < $scrollWrapper.scrollLeft) {
        $scrollWrapper.scrollLeft = tagLeft - 10
    }
    // 目标标签在可视区域右侧
    else if (tagLeft + tagWidth > $scrollWrapper.scrollLeft + $containerWidth) {
        $scrollWrapper.scrollLeft = tagLeft + tagWidth - $containerWidth + 10
    }
}

// 滚动到左侧
const scrollLeft = () => {
    const $scrollWrapper = scrollWrapper.value
    $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft - scrollSpeed
}

// 滚动到右侧
const scrollRight = () => {
    const $scrollWrapper = scrollWrapper.value
    $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + scrollSpeed
}

defineExpose({
    moveToTarget,
    scrollLeft,
    scrollRight
})
</script>

<style lang="scss" scoped>
.scroll-container {
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    width: 100%;

    :deep(.el-scrollbar__bar) {
        bottom: 0px;
    }

    :deep(.el-scrollbar__wrap) {
        height: 49px;
    }
}
</style>