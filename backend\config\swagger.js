const swaggerJSDoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");

// Swagger配置
const swaggerOptions = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "若依管理系统 API",
      version: "1.0.0",
      description: "基于 Node.js 和 Vue3 的后台管理系统 API 文档",
      contact: {
        name: "API Support",
        email: "<EMAIL>",
      },
    },
    servers: [
      {
        url: "http://localhost:3000/api/v1",
        description: "开发环境",
      },
      {
        url: "https://your-api-domain.com/api/v1",
        description: "生产环境",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
      schemas: {
        User: {
          type: "object",
          properties: {
            _id: {
              type: "string",
              description: "用户ID",
            },
            username: {
              type: "string",
              description: "用户名",
            },
            nickname: {
              type: "string",
              description: "昵称",
            },
            email: {
              type: "string",
              description: "邮箱",
            },
            phone: {
              type: "string",
              description: "手机号",
            },
            avatar: {
              type: "string",
              description: "头像URL",
            },
            status: {
              type: "number",
              enum: [0, 1],
              description: "状态：0-禁用，1-正常",
            },
            deptId: {
              type: "string",
              description: "部门ID",
            },
            roles: {
              type: "array",
              items: {
                type: "string",
              },
              description: "角色ID数组",
            },
            createTime: {
              type: "string",
              format: "date-time",
              description: "创建时间",
            },
            updateTime: {
              type: "string",
              format: "date-time",
              description: "更新时间",
            },
            remark: {
              type: "string",
              description: "备注",
            },
          },
        },
        Role: {
          type: "object",
          properties: {
            _id: {
              type: "string",
              description: "角色ID",
            },
            name: {
              type: "string",
              description: "角色名称",
            },
            code: {
              type: "string",
              description: "角色编码",
            },
            status: {
              type: "number",
              enum: [0, 1],
              description: "状态：0-禁用，1-正常",
            },
            permissions: {
              type: "array",
              items: {
                type: "string",
              },
              description: "权限ID数组",
            },
            createTime: {
              type: "string",
              format: "date-time",
              description: "创建时间",
            },
            updateTime: {
              type: "string",
              format: "date-time",
              description: "更新时间",
            },
            remark: {
              type: "string",
              description: "备注",
            },
          },
        },
        Permission: {
          type: "object",
          properties: {
            _id: {
              type: "string",
              description: "权限ID",
            },
            name: {
              type: "string",
              description: "权限名称",
            },
            type: {
              type: "string",
              enum: ["menu", "button"],
              description: "类型：menu-菜单，button-按钮",
            },
            parentId: {
              type: "string",
              description: "父级ID",
            },
            path: {
              type: "string",
              description: "路由路径",
            },
            component: {
              type: "string",
              description: "组件路径",
            },
            code: {
              type: "string",
              description: "权限标识",
            },
            icon: {
              type: "string",
              description: "图标",
            },
            sort: {
              type: "number",
              description: "排序",
            },
            status: {
              type: "number",
              enum: [0, 1],
              description: "状态：0-禁用，1-正常",
            },
            createTime: {
              type: "string",
              format: "date-time",
              description: "创建时间",
            },
            updateTime: {
              type: "string",
              format: "date-time",
              description: "更新时间",
            },
          },
        },
        Department: {
          type: "object",
          properties: {
            _id: {
              type: "string",
              description: "部门ID",
            },
            name: {
              type: "string",
              description: "部门名称",
            },
            parentId: {
              type: "string",
              description: "父级部门ID",
            },
            ancestors: {
              type: "string",
              description: "祖级列表",
            },
            leader: {
              type: "string",
              description: "负责人",
            },
            phone: {
              type: "string",
              description: "联系电话",
            },
            email: {
              type: "string",
              description: "邮箱",
            },
            status: {
              type: "number",
              enum: [0, 1],
              description: "状态：0-禁用，1-正常",
            },
            sort: {
              type: "number",
              description: "排序",
            },
            createTime: {
              type: "string",
              format: "date-time",
              description: "创建时间",
            },
            updateTime: {
              type: "string",
              format: "date-time",
              description: "更新时间",
            },
          },
        },
        ApiResponse: {
          type: "object",
          properties: {
            code: {
              type: "number",
              description: "响应状态码",
            },
            success: {
              type: "boolean",
              description: "是否成功",
            },
            message: {
              type: "string",
              description: "响应消息",
            },
            data: {
              description: "响应数据",
            },
          },
        },
        Error: {
          type: "object",
          properties: {
            code: {
              type: "number",
              description: "错误状态码",
            },
            success: {
              type: "boolean",
              example: false,
              description: "是否成功",
            },
            message: {
              type: "string",
              description: "错误消息",
            },
            data: {
              description: "错误详情",
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: [
    "./routes/*.js",
    "./controllers/*.js",
    "./models/*.js",
  ],
};

// 生成Swagger规范
const swaggerSpec = swaggerJSDoc(swaggerOptions);

// 自定义Swagger UI配置
const swaggerUiOptions = {
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info .title { color: #3b82f6 }
  `,
  customSiteTitle: "若依管理系统 API 文档",
  customfavIcon: "/favicon.ico",
};

module.exports = {
  swaggerSpec,
  swaggerUi,
  swaggerUiOptions,
};
