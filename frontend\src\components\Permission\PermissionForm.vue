<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="600px"
    append-to-body
    @close="handleClose"
  >
    <el-form
      ref="permFormRef"
      :model="permForm"
      :rules="permFormRules"
      label-width="100px"
    >
      <el-form-item label="上级权限">
        <el-tree-select
          v-model="permForm.parentId"
          :data="permissionOptions"
          :props="{ label: 'name', value: '_id', children: 'children' }"
          placeholder="请选择上级权限"
          check-strictly
          clearable
        />
      </el-form-item>

      <el-form-item label="权限名称" prop="name">
        <el-input v-model="permForm.name" placeholder="请输入权限名称" />
      </el-form-item>

      <el-form-item label="权限类型" prop="type">
        <el-radio-group v-model="permForm.type">
          <el-radio label="menu">菜单</el-radio>
          <el-radio label="button">按钮</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        label="权限标识"
        prop="code"
        v-if="permForm.type === 'button'"
      >
        <el-input v-model="permForm.code" placeholder="请输入权限标识" />
      </el-form-item>

      <el-form-item
        label="路由地址"
        prop="path"
        v-if="permForm.type === 'menu'"
      >
        <el-input v-model="permForm.path" placeholder="请输入路由地址" />
      </el-form-item>

      <el-form-item
        label="组件路径"
        prop="component"
        v-if="permForm.type === 'menu'"
      >
        <el-input v-model="permForm.component" placeholder="请输入组件路径" />
      </el-form-item>

      <el-form-item
        label="菜单图标"
        prop="icon"
        v-if="permForm.type === 'menu'"
      >
        <el-input v-model="permForm.icon" placeholder="请输入菜单图标" />
      </el-form-item>

      <el-form-item label="显示顺序" prop="sort">
        <el-input-number
          v-model="permForm.sort"
          :min="0"
          :max="999"
          controls-position="right"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="permForm.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from "vue";

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "权限表单",
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  permissionOptions: {
    type: Array,
    default: () => [],
  },
});

// Emits
const emit = defineEmits(["update:visible", "submit", "cancel"]);

// 响应式数据
const dialogVisible = ref(false);
const dialogTitle = ref("");
const permFormRef = ref(null);

// 表单数据
const permForm = reactive({
  _id: "",
  parentId: "",
  name: "",
  type: "menu",
  code: "",
  path: "",
  component: "",
  icon: "",
  sort: 0,
  status: 1,
});

// 表单验证规则
const permFormRules = {
  name: [
    { required: true, message: "请输入权限名称", trigger: "blur" },
    { max: 50, message: "权限名称长度不能超过 50 个字符", trigger: "blur" },
  ],
  type: [{ required: true, message: "请选择权限类型", trigger: "change" }],
  code: [
    { required: true, message: "请输入权限标识", trigger: "blur" },
    { max: 100, message: "权限标识长度不能超过 100 个字符", trigger: "blur" },
  ],
  path: [
    { required: true, message: "请输入路由地址", trigger: "blur" },
    { max: 200, message: "路由地址长度不能超过 200 个字符", trigger: "blur" },
  ],
  component: [
    { required: true, message: "请输入组件路径", trigger: "blur" },
    { max: 200, message: "组件路径长度不能超过 200 个字符", trigger: "blur" },
  ],
  sort: [{ required: true, message: "请输入显示顺序", trigger: "blur" }],
};

// 重置表单
const resetForm = () => {
  Object.assign(permForm, {
    _id: "",
    parentId: "",
    name: "",
    type: "menu",
    code: "",
    path: "",
    component: "",
    icon: "",
    sort: 0,
    status: 1,
  });

  if (permFormRef.value) {
    permFormRef.value.resetFields();
  }
};

// 监听props变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
  },
  { immediate: true }
);

watch(
  () => props.title,
  (newVal) => {
    dialogTitle.value = newVal;
  },
  { immediate: true }
);

watch(
  () => props.formData,
  (newVal) => {
    if (newVal && Object.keys(newVal).length > 0) {
      Object.assign(permForm, newVal);
    } else {
      resetForm();
    }
  },
  { deep: true, immediate: true }
);

// 处理提交
const handleSubmit = () => {
  permFormRef.value.validate((valid) => {
    if (valid) {
      emit("submit", { ...permForm });
    }
  });
};

// 处理取消
const handleCancel = () => {
  emit("update:visible", false);
  emit("cancel");
};

// 处理关闭
const handleClose = () => {
  emit("update:visible", false);
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
