# 权限管理组件

## 组件结构

### PermissionTable.vue

权限表格组件，负责展示权限树形结构和处理表格相关操作。

**Props:**

- `permissionList`: 权限列表数据
- `loading`: 加载状态
- `expandedKeys`: 展开的节点 keys

**Events:**

- `add`: 新增权限事件
- `edit`: 编辑权限事件
- `delete`: 删除权限事件
- `refresh`: 刷新数据事件

**功能:**

- 权限树形表格展示
- 权限状态切换
- 权限操作按钮（新增、编辑、删除）

### PermissionForm.vue

权限表单组件，负责权限的新增和编辑表单。

**Props:**

- `visible`: 对话框显示状态
- `title`: 对话框标题
- `formData`: 表单数据
- `permissionOptions`: 权限选项（用于上级权限选择）

**Events:**

- `update:visible`: 更新显示状态
- `submit`: 表单提交事件
- `cancel`: 取消事件

**功能:**

- 权限表单验证
- 动态表单项（根据权限类型显示不同字段）
- 上级权限树形选择

## 使用方式

```vue
<template>
  <div>
    <!-- 权限表格 -->
    <PermissionTable
      :permission-list="permissionList"
      :loading="loading"
      :expanded-keys="expandedKeys"
      @add="handleAdd"
      @edit="handleEdit"
      @delete="handleDelete"
      @refresh="getList"
    />

    <!-- 权限表单 -->
    <PermissionForm
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :form-data="currentFormData"
      :permission-options="permissionOptions"
      @submit="handleFormSubmit"
      @cancel="handleFormCancel"
    />
  </div>
</template>
```

## 重构说明

1. **组件拆分**: 将原来的单一页面拆分为表格组件和表单组件
2. **职责分离**: 表格组件负责数据展示和基础操作，表单组件负责数据编辑
3. **事件驱动**: 通过事件机制实现组件间通信
4. **可复用性**: 组件可以在其他页面中复用
5. **维护性**: 代码结构更清晰，便于维护和扩展
