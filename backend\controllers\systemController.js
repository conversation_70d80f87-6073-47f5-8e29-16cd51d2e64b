const os = require("os");
const fs = require("fs");
const path = require("path");
const mongoose = require("mongoose");

// 获取系统信息
const getSystemInfo = async (req, res) => {
  try {
    const systemInfo = {
      // 系统基本信息
      system: {
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        uptime: os.uptime(),
        loadavg: os.loadavg(),
      },

      // 内存信息
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem(),
        usage: (((os.totalmem() - os.freemem()) / os.totalmem()) * 100).toFixed(
          2
        ),
      },

      // CPU信息
      cpu: {
        model: os.cpus()[0].model,
        cores: os.cpus().length,
        speed: os.cpus()[0].speed,
      },

      // Node.js信息
      node: {
        version: process.version,
        uptime: process.uptime(),
        pid: process.pid,
        memoryUsage: process.memoryUsage(),
      },

      // 数据库连接状态
      database: {
        status:
          mongoose.connection.readyState === 1 ? "connected" : "disconnected",
        host: mongoose.connection.host,
        port: mongoose.connection.port,
        name: mongoose.connection.name,
      },
    };

    res.json({
      code: 200,
      success: true,
      message: "获取系统信息成功",
      data: systemInfo,
    });
  } catch (error) {
    console.error("获取系统信息失败:", error);
    res.status(500).json({
      code: 500,
      success: false,
      message: "获取系统信息失败",
    });
  }
};

// 健康检查
const healthCheck = async (req, res) => {
  try {
    const health = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      checks: {},
    };

    // 检查数据库连接
    try {
      await mongoose.connection.db.admin().ping();
      health.checks.database = { status: "healthy", message: "数据库连接正常" };
    } catch (error) {
      health.checks.database = {
        status: "unhealthy",
        message: "数据库连接异常",
        error: error.message,
      };
      health.status = "unhealthy";
    }

    // 检查内存使用率
    const memoryUsage = ((os.totalmem() - os.freemem()) / os.totalmem()) * 100;
    if (memoryUsage > 90) {
      health.checks.memory = {
        status: "warning",
        message: `内存使用率过高: ${memoryUsage.toFixed(2)}%`,
      };
      health.status = "warning";
    } else {
      health.checks.memory = {
        status: "healthy",
        message: `内存使用率正常: ${memoryUsage.toFixed(2)}%`,
      };
    }

    // 检查磁盘空间
    try {
      const stats = fs.statSync(process.cwd());
      health.checks.disk = { status: "healthy", message: "磁盘空间正常" };
    } catch (error) {
      health.checks.disk = { status: "warning", message: "无法检查磁盘空间" };
    }

    const statusCode =
      health.status === "healthy"
        ? 200
        : health.status === "warning"
        ? 200
        : 503;

    res.status(statusCode).json({
      code: statusCode,
      success: health.status !== "unhealthy",
      message: `系统状态: ${health.status}`,
      data: health,
    });
  } catch (error) {
    console.error("健康检查失败:", error);
    res.status(503).json({
      code: 503,
      success: false,
      message: "健康检查失败",
      data: {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: error.message,
      },
    });
  }
};

// 获取应用统计信息
const getAppStats = async (req, res) => {
  try {
    const User = require("../models/User");
    const Role = require("../models/Role");
    const Permission = require("../models/Permission");
    const Department = require("../models/Department");

    const stats = {
      users: {
        total: await User.countDocuments(),
        active: await User.countDocuments({ status: 1 }),
        inactive: await User.countDocuments({ status: 0 }),
      },
      roles: {
        total: await Role.countDocuments(),
        active: await Role.countDocuments({ status: 1 }),
        inactive: await Role.countDocuments({ status: 0 }),
      },
      permissions: {
        total: await Permission.countDocuments(),
        active: await Permission.countDocuments({ status: 1 }),
        inactive: await Permission.countDocuments({ status: 0 }),
      },
      departments: {
        total: await Department.countDocuments(),
        active: await Department.countDocuments({ status: 1 }),
        inactive: await Department.countDocuments({ status: 0 }),
      },
    };

    res.json({
      code: 200,
      success: true,
      message: "获取应用统计信息成功",
      data: stats,
    });
  } catch (error) {
    console.error("获取应用统计信息失败:", error);
    res.status(500).json({
      code: 500,
      success: false,
      message: "获取应用统计信息失败",
    });
  }
};

// 清理系统缓存
const clearCache = async (req, res) => {
  try {
    // 强制垃圾回收（如果可用）
    if (global.gc) {
      global.gc();
    }

    // 清理require缓存（谨慎使用）
    const cacheKeys = Object.keys(require.cache);
    let clearedCount = 0;

    // 只清理非核心模块的缓存
    cacheKeys.forEach((key) => {
      if (!key.includes("node_modules") && key.includes(process.cwd())) {
        delete require.cache[key];
        clearedCount++;
      }
    });

    res.json({
      code: 200,
      success: true,
      message: "缓存清理完成",
      data: {
        clearedModules: clearedCount,
        memoryUsage: process.memoryUsage(),
      },
    });
  } catch (error) {
    console.error("清理缓存失败:", error);
    res.status(500).json({
      code: 500,
      success: false,
      message: "清理缓存失败",
    });
  }
};

// 获取日志文件列表
const getLogFiles = async (req, res) => {
  try {
    const logsDir = path.join(__dirname, "../logs");

    if (!fs.existsSync(logsDir)) {
      return res.json({
        code: 200,
        success: true,
        message: "日志目录不存在",
        data: [],
      });
    }

    const files = fs.readdirSync(logsDir);
    const logFiles = files
      .filter((file) => file.endsWith(".log"))
      .map((file) => {
        const filePath = path.join(logsDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
        };
      })
      .sort((a, b) => b.modified - a.modified);

    res.json({
      code: 200,
      success: true,
      message: "获取日志文件列表成功",
      data: logFiles,
    });
  } catch (error) {
    console.error("获取日志文件列表失败:", error);
    res.status(500).json({
      code: 500,
      success: false,
      message: "获取日志文件列表失败",
    });
  }
};

// 下载日志文件
const downloadLogFile = async (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(__dirname, "../logs", filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        code: 404,
        success: false,
        message: "日志文件不存在",
      });
    }

    res.download(filePath, filename, (error) => {
      if (error) {
        console.error("下载日志文件失败:", error);
        res.status(500).json({
          code: 500,
          success: false,
          message: "下载日志文件失败",
        });
      }
    });
  } catch (error) {
    console.error("下载日志文件失败:", error);
    res.status(500).json({
      code: 500,
      success: false,
      message: "下载日志文件失败",
    });
  }
};

module.exports = {
  getSystemInfo,
  healthCheck,
  getAppStats,
  clearCache,
  getLogFiles,
  downloadLogFile,
};
